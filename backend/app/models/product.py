from pydantic import BaseModel, Field
from typing import List, Optional, Union


class IngredientInfo(BaseModel):
    """成分信息模型"""
    name: str = Field(description="成分名称")
    amount: Optional[float] = Field(default=None, description="含量数值")
    unit: Optional[str] = Field(default=None, description="单位")
    ingredient_id: Optional[str] = Field(default=None, description="成分ID，用于实体链接")


class ProductInfo(BaseModel):
    """产品信息模型"""
    product_name: str = Field(description="产品名称")
    brand: str = Field(description="品牌名称")
    product_type: str = Field(default="保健品", description="产品类型")
    sku: Optional[str] = Field(default=None, description="产品SKU")
    ingredients: List[IngredientInfo] = Field(default_factory=list, description="成分列表")
    benefits: Optional[str] = Field(default=None, description="功效描述")
    brand_id: Optional[str] = Field(default=None, description="品牌ID，用于实体链接")


class ExtractionRequest(BaseModel):
    """AI提取请求模型"""
    text: str = Field(max_length=10000, description="待提取的产品描述文本")


class ExtractionResponse(BaseModel):
    """AI提取响应模型"""
    success: bool
    data: Optional[ProductInfo] = None
    confidence: float = 0.0
    error: Optional[str] = None


class ProductSaveRequest(BaseModel):
    """产品保存请求模型"""
    product_name: str = Field(min_length=2, max_length=100, description="产品名称")
    brand: str = Field(min_length=2, max_length=50, description="品牌名称")
    brand_id: Optional[str] = Field(default=None, description="品牌ID")
    product_type: str = Field(default="保健品", description="产品类型")
    sku: Optional[str] = Field(default=None, description="产品SKU")
    ingredients: List[IngredientInfo] = Field(min_items=1, description="成分列表")
    benefits: Optional[str] = Field(default=None, max_length=500, description="功效描述")


# 增强版产品保存模型 - 支持实体链接
class EntityLinkInfo(BaseModel):
    """实体链接信息"""
    name: str = Field(description="实体名称")
    entity_link: Union[str, None] = Field(description="实体链接：'new' 表示创建新实体，否则为现有实体ID")


class IngredientLinkInfo(EntityLinkInfo):
    """成分链接信息"""
    amount: Optional[float] = Field(default=None, description="含量数值")
    unit: Optional[str] = Field(default=None, description="单位")


class EnhancedProductSaveRequest(BaseModel):
    """增强版产品保存请求 - 支持实体链接"""
    # 产品信息
    product: EntityLinkInfo = Field(description="产品信息和链接")
    product_type: str = Field(default="保健品", description="产品类型")
    sku: Optional[str] = Field(default=None, description="产品SKU")
    benefits: Optional[str] = Field(default=None, max_length=500, description="功效描述")
    
    # 品牌链接
    brand: EntityLinkInfo = Field(description="品牌信息和链接")
    
    # 成分链接列表
    ingredients: List[IngredientLinkInfo] = Field(min_items=1, description="成分链接列表")


class ProductSaveResponse(BaseModel):
    """产品保存响应模型"""
    success: bool
    data: Optional[dict] = None
    error: Optional[str] = None