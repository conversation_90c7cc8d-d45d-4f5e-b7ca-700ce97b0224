"""
实体管理数据模型定义
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum

class EntityType(str, Enum):
    """实体类型枚举"""
    BRAND = "Brand"
    INGREDIENT = "Ingredient"
    PRODUCT = "Product"

class SortOrder(str, Enum):
    """排序方向"""
    ASC = "asc"
    DESC = "desc"

class FilterType(str, Enum):
    """筛选类型"""
    ALL = "all"
    ACTIVE = "active"
    UNUSED = "unused"
    DUPLICATE = "duplicate"

# =============================================================================
# 品牌管理模型
# =============================================================================

class BrandInfo(BaseModel):
    """品牌基本信息"""
    uuid: str
    name: str
    created_at: datetime
    updated_at: datetime
    product_count: Optional[int] = 0
    is_active: bool = True
    # 向量化状态信息
    vectorization_status: Optional[str] = "PENDING"  # PENDING/PROCESSING/COMPLETED/FAILED
    vectorization_attempts: Optional[int] = 0
    vectorization_error: Optional[str] = None

class BrandDetail(BrandInfo):
    """品牌详细信息"""
    products: List[str] = []
    related_ingredients: List[str] = []
    similarity_score: Optional[float] = None

class BrandCreateRequest(BaseModel):
    """创建品牌请求"""
    name: str = Field(..., min_length=2, max_length=50, description="品牌名称")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('品牌名称不能为空')
        return v.strip()

class BrandUpdateRequest(BaseModel):
    """更新品牌请求"""
    name: Optional[str] = Field(None, min_length=2, max_length=50)
    is_active: Optional[bool] = None

class BrandListRequest(BaseModel):
    """品牌列表查询请求"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    search: Optional[str] = Field(None, description="搜索关键词")
    filter_type: FilterType = Field(FilterType.ALL, description="筛选类型")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: SortOrder = Field(SortOrder.DESC, description="排序方向")

class BrandListResponse(BaseModel):
    """品牌列表响应"""
    success: bool
    data: List[BrandInfo]
    total: int
    page: int
    size: int
    error: Optional[str] = None

class BrandDetailResponse(BaseModel):
    """品牌详情响应"""
    success: bool
    data: Optional[BrandDetail]
    error: Optional[str] = None

# =============================================================================
# 成分管理模型
# =============================================================================

class IngredientInfo(BaseModel):
    """成分基本信息"""
    uuid: str
    name: str
    created_at: datetime
    updated_at: datetime
    product_count: Optional[int] = 0
    is_active: bool = True
    # 向量化状态信息
    vectorization_status: Optional[str] = "PENDING"
    vectorization_attempts: Optional[int] = 0
    vectorization_error: Optional[str] = None

class IngredientDetail(IngredientInfo):
    """成分详细信息"""
    products: List[str] = []
    related_brands: List[str] = []
    total_amount: Optional[float] = None
    common_unit: Optional[str] = None

class IngredientCreateRequest(BaseModel):
    """创建成分请求"""
    name: str = Field(..., min_length=1, max_length=30, description="成分名称")
    
    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('成分名称不能为空')
        return v.strip()

class IngredientUpdateRequest(BaseModel):
    """更新成分请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=30)
    is_active: Optional[bool] = None

class IngredientListRequest(BaseModel):
    """成分列表查询请求"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    search: Optional[str] = Field(None, description="搜索关键词")
    filter_type: FilterType = Field(FilterType.ALL, description="筛选类型")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: SortOrder = Field(SortOrder.DESC, description="排序方向")

class IngredientListResponse(BaseModel):
    """成分列表响应"""
    success: bool
    data: List[IngredientInfo]
    total: int
    page: int
    size: int
    error: Optional[str] = None

class IngredientDetailResponse(BaseModel):
    """成分详情响应"""
    success: bool
    data: Optional[IngredientDetail]
    error: Optional[str] = None

# =============================================================================
# 产品管理模型
# =============================================================================

class ProductInfo(BaseModel):
    """产品基本信息"""
    uuid: str
    name: str
    sku: Optional[str]
    product_type: str
    brand_name: str
    brand_id: str
    ingredient_count: int = 0
    benefits: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    # 向量化状态信息
    vectorization_status: Optional[str] = "PENDING"  # PENDING/PROCESSING/COMPLETED/FAILED
    vectorization_attempts: Optional[int] = 0
    vectorization_error: Optional[str] = None

class ProductDetail(ProductInfo):
    """产品详细信息"""
    ingredients: List[Dict[str, Any]] = []
    brand_info: Optional[Dict[str, str]] = None

class ProductCreateRequest(BaseModel):
    """创建产品请求"""
    name: str = Field(..., min_length=2, max_length=100, description="产品名称")
    sku: Optional[str] = Field(None, max_length=50, description="产品SKU")
    product_type: str = Field(..., min_length=2, max_length=20, description="产品类型")
    brand_id: str = Field(..., description="品牌ID")
    ingredients: List[Dict[str, Any]] = Field([], description="成分列表")
    benefits: Optional[str] = Field(None, max_length=500, description="产品功效")

class ProductUpdateRequest(BaseModel):
    """更新产品请求"""
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    sku: Optional[str] = Field(None, max_length=50)
    product_type: Optional[str] = Field(None, min_length=2, max_length=20)
    brand_id: Optional[str] = None
    ingredients: Optional[List[Dict[str, Any]]] = None
    benefits: Optional[str] = Field(None, max_length=500)
    is_active: Optional[bool] = None

class ProductListRequest(BaseModel):
    """产品列表查询请求"""
    page: int = Field(1, ge=1, description="页码")
    size: int = Field(20, ge=1, le=100, description="每页数量")
    search: Optional[str] = Field(None, description="搜索关键词")
    brand_filter: Optional[str] = Field(None, description="品牌筛选")
    ingredient_filter: Optional[str] = Field(None, description="成分筛选")
    filter_type: FilterType = Field(FilterType.ALL, description="筛选类型")
    sort_by: str = Field("created_at", description="排序字段")
    sort_order: SortOrder = Field(SortOrder.DESC, description="排序方向")

class ProductListResponse(BaseModel):
    """产品列表响应"""
    success: bool
    data: List[ProductInfo]
    total: int
    page: int
    size: int
    error: Optional[str] = None

class ProductDetailResponse(BaseModel):
    """产品详情响应"""
    success: bool
    data: Optional[ProductDetail]
    error: Optional[str] = None

# =============================================================================
# 批量操作模型
# =============================================================================

class BatchOperationType(str, Enum):
    """批量操作类型"""
    DELETE = "delete"
    MERGE = "merge"
    START_VECTORIZATION = "start_vectorization"

class BatchOperationRequest(BaseModel):
    """批量操作请求"""
    entity_type: EntityType
    operation_type: BatchOperationType
    entity_ids: List[str] = Field(..., min_items=1, max_items=100, description="实体ID列表")
    operation_data: Optional[Dict[str, Any]] = Field(None, description="操作参数")

class BatchOperationResponse(BaseModel):
    """批量操作响应"""
    success: bool
    processed_count: int
    success_count: int
    failed_count: int
    details: List[Dict[str, Any]] = []
    task_ids: Optional[List[str]] = Field(None, description="向量化任务ID列表（仅start_vectorization操作时返回）")
    error: Optional[str] = None

# =============================================================================
# 通用响应模型
# =============================================================================

class StandardResponse(BaseModel):
    """标准API响应"""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    message: Optional[str] = None

class EntityStatsResponse(BaseModel):
    """实体统计响应"""
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None