"""
数据质量监控相关的数据模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum

class QualitySeverity(str, Enum):
    """质量问题严重性"""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"

class QualityIssueType(str, Enum):
    """质量问题类型"""
    DUPLICATE = "duplicate"
    MISSING_VECTORS = "missing_vectors"
    ORPHAN_NODE = "orphan_node"
    MISSING_RELATIONSHIPS = "missing_relationships"
    DATA_COMPLETENESS = "data_completeness"
    NAMING_CONSISTENCY = "naming_consistency"

class DataQualityReportRequest(BaseModel):
    """数据质量报告请求"""
    entity_types: Optional[List[str]] = Field(None, description="检查的实体类型列表")
    check_types: Optional[List[str]] = Field(None, description="检查类型列表")
    
    @validator('entity_types')
    def validate_entity_types(cls, v):
        if v is not None:
            valid_types = {"brand", "ingredient", "product"}
            invalid_types = set(v) - valid_types
            if invalid_types:
                raise ValueError(f'不支持的实体类型: {list(invalid_types)}')
        return v
    
    @validator('check_types')
    def validate_check_types(cls, v):
        if v is not None:
            valid_types = {
                "duplicate_detection", "missing_vectors", "orphan_nodes",
                "missing_relationships", "data_completeness", "naming_consistency"
            }
            invalid_types = set(v) - valid_types
            if invalid_types:
                raise ValueError(f'不支持的检查类型: {list(invalid_types)}')
        return v

class DataQualityIssueModel(BaseModel):
    """数据质量问题模型"""
    issue_type: str = Field(..., description="问题类型")
    entity_type: str = Field(..., description="实体类型")
    entity_id: str = Field(..., description="实体ID")
    entity_name: str = Field(..., description="实体名称")
    severity: QualitySeverity = Field(..., description="严重性")
    description: str = Field(..., description="问题描述")
    suggested_action: str = Field(..., description="建议操作")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")

class DataQualityReportModel(BaseModel):
    """数据质量报告模型"""
    report_id: str = Field(..., description="报告ID")
    generated_at: datetime = Field(..., description="生成时间")
    total_entities: int = Field(..., description="总实体数")
    issues_found: int = Field(..., description="发现的问题数")
    critical_issues: int = Field(..., description="严重问题数")
    warning_issues: int = Field(..., description="警告问题数")
    info_issues: int = Field(..., description="信息问题数")
    overall_score: float = Field(..., description="整体质量评分", ge=0.0, le=100.0)
    issues_by_type: Dict[str, int] = Field(..., description="按类型分组的问题数")
    issues_by_entity: Dict[str, int] = Field(..., description="按实体类型分组的问题数")
    issues: List[DataQualityIssueModel] = Field(..., description="问题列表")
    recommendations: List[str] = Field(..., description="改进建议")

class DataQualityReportResponse(BaseModel):
    """数据质量报告响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[DataQualityReportModel] = Field(None, description="报告数据")
    error: Optional[str] = Field(None, description="错误信息")

class QualityCheckConfig(BaseModel):
    """质量检查配置"""
    check_type: str = Field(..., description="检查类型")
    enabled: bool = Field(True, description="是否启用")
    parameters: Optional[Dict[str, Any]] = Field(None, description="检查参数")

class QuickQualityCheckSummary(BaseModel):
    """快速质量检查摘要"""
    overall_score: float = Field(..., description="整体质量评分")
    total_entities: int = Field(..., description="总实体数")
    issues_found: int = Field(..., description="发现的问题数")
    critical_issues: int = Field(..., description="严重问题数")
    warning_issues: int = Field(..., description="警告问题数")
    info_issues: int = Field(..., description="信息问题数")
    top_recommendations: List[str] = Field(..., description="主要建议")

class QualityCheckResponse(BaseModel):
    """质量检查响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[QuickQualityCheckSummary] = Field(None, description="检查摘要")
    error: Optional[str] = Field(None, description="错误信息")

class EntityTypeStats(BaseModel):
    """实体类型统计"""
    total: int = Field(..., description="总数")
    vectorized: int = Field(..., description="已向量化数")
    missing_vectors: int = Field(..., description="缺少向量数")
    vectorization_rate: float = Field(..., description="向量化率")

class QualityStatsData(BaseModel):
    """质量统计数据"""
    entity_counts: Dict[str, int] = Field(..., description="实体数量统计")
    total_entities: int = Field(..., description="总实体数")
    vectorization_stats: Dict[str, EntityTypeStats] = Field(..., description="向量化统计")
    overall_vectorization_rate: float = Field(..., description="整体向量化率")
    last_updated: Optional[str] = Field(None, description="最后更新时间")

class QualityStatsResponse(BaseModel):
    """质量统计响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[QualityStatsData] = Field(None, description="统计数据")
    error: Optional[str] = Field(None, description="错误信息")

class ServiceHealthData(BaseModel):
    """服务健康数据"""
    service_status: str = Field(..., description="服务状态")
    database_connected: bool = Field(..., description="数据库连接状态")
    vector_service_available: bool = Field(..., description="向量服务可用性")
    last_check: Optional[str] = Field(None, description="最后检查时间")
    error: Optional[str] = Field(None, description="错误信息")

class ServiceHealthResponse(BaseModel):
    """服务健康响应"""
    success: bool = Field(..., description="是否成功")
    data: ServiceHealthData = Field(..., description="健康状态数据")

class CheckTypeInfo(BaseModel):
    """检查类型信息"""
    type: str = Field(..., description="检查类型")
    name: str = Field(..., description="检查名称")
    description: str = Field(..., description="检查描述")

class SeverityLevelInfo(BaseModel):
    """严重性级别信息"""
    level: str = Field(..., description="严重性级别")
    name: str = Field(..., description="级别名称")
    description: str = Field(..., description="级别描述")

class QualityConfigData(BaseModel):
    """质量配置数据"""
    supported_entity_types: List[str] = Field(..., description="支持的实体类型")
    supported_check_types: List[CheckTypeInfo] = Field(..., description="支持的检查类型")
    severity_levels: List[SeverityLevelInfo] = Field(..., description="严重性级别")

class QualityConfigResponse(BaseModel):
    """质量配置响应"""
    success: bool = Field(..., description="是否成功")
    data: QualityConfigData = Field(..., description="配置数据")