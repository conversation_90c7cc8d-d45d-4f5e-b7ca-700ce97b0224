from pydantic import BaseModel, Field
from typing import List, Optional


class EntityInfo(BaseModel):
    """实体信息模型"""
    id: str = Field(description="实体ID")
    name: str = Field(description="实体名称")
    type: str = Field(description="实体类型：Brand/Ingredient/Product")
    match_score: Optional[float] = Field(default=None, description="匹配分数")
    match_type: Optional[str] = Field(default="string", description="匹配类型：vector/string")
    
    # 产品专属属性
    product_type: Optional[str] = Field(default=None, description="产品类型")
    sku: Optional[str] = Field(default=None, description="产品SKU")
    benefits: Optional[str] = Field(default=None, description="产品功效")


class EntitySearchRequest(BaseModel):
    """实体搜索请求模型"""
    type: str = Field(description="实体类型：Brand/Ingredient")
    keyword: str = Field(min_length=2, description="搜索关键词")
    limit: int = Field(default=3, ge=1, le=10, description="返回数量限制")


class EntitySearchResponse(BaseModel):
    """实体搜索响应模型"""
    success: bool
    data: List[EntityInfo] = Field(default_factory=list)
    error: Optional[str] = None