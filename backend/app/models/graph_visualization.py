"""
图数据可视化相关的数据模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime

class GraphNodeModel(BaseModel):
    """图节点模型"""
    id: str = Field(..., description="节点ID")
    label: str = Field(..., description="节点标签")
    type: str = Field(..., description="节点类型")
    properties: Dict[str, Any] = Field(default={}, description="节点属性")
    color: Optional[str] = Field(None, description="节点颜色")
    size: Optional[int] = Field(None, description="节点大小")
    isCenter: Optional[bool] = Field(False, description="是否为中心节点")

class GraphEdgeModel(BaseModel):
    """图边模型"""
    id: str = Field(..., description="边ID")
    source: str = Field(..., description="源节点ID")
    target: str = Field(..., description="目标节点ID")
    label: str = Field(..., description="关系标签")
    properties: Dict[str, Any] = Field(default={}, description="边属性")

class GraphDataModel(BaseModel):
    """图数据模型"""
    nodes: List[GraphNodeModel] = Field(..., description="节点列表")
    edges: List[GraphEdgeModel] = Field(..., description="边列表")
    stats: Dict[str, Any] = Field(..., description="统计信息")

class GraphDataResponse(BaseModel):
    """图数据响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[GraphDataModel] = Field(None, description="图数据")
    error: Optional[str] = Field(None, description="错误信息")

class GraphStatsModel(BaseModel):
    """图统计模型"""
    total_nodes: int = Field(..., description="总节点数")
    total_edges: int = Field(..., description="总边数")
    node_counts_by_type: Dict[str, int] = Field(..., description="按类型分组的节点数")
    edge_counts_by_type: Dict[str, int] = Field(..., description="按类型分组的边数")
    density: float = Field(..., description="图密度", ge=0.0, le=1.0)
    components: int = Field(..., description="连通分量数", ge=1)

class GraphStatsResponse(BaseModel):
    """图统计响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[GraphStatsModel] = Field(None, description="统计数据")
    error: Optional[str] = Field(None, description="错误信息")

class SubgraphRequest(BaseModel):
    """子图请求"""
    entity_id: str = Field(..., description="中心实体ID")
    depth: int = Field(2, description="遍历深度", ge=1, le=5)
    limit: int = Field(50, description="节点数量限制", ge=10, le=200)
    
    @validator('entity_id')
    def validate_entity_id(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('实体ID不能为空')
        return v.strip()

class SubgraphResponse(BaseModel):
    """子图响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[GraphDataModel] = Field(None, description="子图数据")
    error: Optional[str] = Field(None, description="错误信息")

class EntitySearchRequest(BaseModel):
    """实体搜索请求"""
    query: str = Field(..., description="搜索查询")
    entity_types: Optional[List[str]] = Field(None, description="限制的实体类型列表")
    limit: int = Field(20, description="返回数量限制", ge=1, le=100)
    
    @validator('query')
    def validate_query(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('搜索查询不能为空')
        if len(v.strip()) > 200:
            raise ValueError('搜索查询长度不能超过200字符')
        return v.strip()
    
    @validator('entity_types')
    def validate_entity_types(cls, v):
        if v is not None:
            valid_types = {"brand", "ingredient", "product"}
            invalid_types = set(v) - valid_types
            if invalid_types:
                raise ValueError(f'不支持的实体类型: {list(invalid_types)}')
        return v

class EntitySearchResultModel(BaseModel):
    """实体搜索结果模型"""
    id: str = Field(..., description="实体ID")
    name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型")
    label: str = Field(..., description="实体标签")
    properties: Dict[str, Any] = Field(..., description="实体属性")
    degree: int = Field(..., description="节点度数")

class EntitySearchData(BaseModel):
    """实体搜索数据"""
    entities: List[EntitySearchResultModel] = Field(..., description="搜索结果")
    total: int = Field(..., description="结果总数")
    query: str = Field(..., description="搜索查询")

class EntitySearchResponse(BaseModel):
    """实体搜索响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[EntitySearchData] = Field(None, description="搜索数据")
    error: Optional[str] = Field(None, description="错误信息")

class LayoutOption(BaseModel):
    """布局选项"""
    value: str = Field(..., description="布局值")
    label: str = Field(..., description="布局标签")

class EdgeStyle(BaseModel):
    """边样式"""
    color: str = Field(..., description="边颜色")
    width: int = Field(..., description="边宽度", ge=1)

class VisualizationConfig(BaseModel):
    """可视化配置"""
    node_colors: Dict[str, str] = Field(..., description="节点颜色配置")
    node_sizes: Dict[str, int] = Field(..., description="节点大小配置")
    layout_options: List[LayoutOption] = Field(..., description="布局选项")
    edge_styles: Dict[str, EdgeStyle] = Field(..., description="边样式配置")

class VisualizationConfigResponse(BaseModel):
    """可视化配置响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[VisualizationConfig] = Field(None, description="配置数据")
    error: Optional[str] = Field(None, description="错误信息")

class GraphServiceHealth(BaseModel):
    """图服务健康状态"""
    service_status: str = Field(..., description="服务状态")
    database_connected: bool = Field(..., description="数据库连接状态")
    total_nodes: Optional[int] = Field(None, description="总节点数")
    error: Optional[str] = Field(None, description="错误信息")

class GraphServiceHealthResponse(BaseModel):
    """图服务健康响应"""
    success: bool = Field(..., description="是否成功")
    data: GraphServiceHealth = Field(..., description="健康状态数据")