"""
批量向量化相关的数据模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime

class VectorizationTaskStatus(str, Enum):
    """向量化任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed" 
    FAILED = "failed"

class BatchVectorizationRequest(BaseModel):
    """批量向量化请求"""
    entity_types: Optional[List[str]] = Field(None, description="实体类型列表，空则处理所有类型")
    force_update: bool = Field(False, description="是否强制更新已有向量")
    limit_per_type: Optional[int] = Field(None, description="每种类型的处理数量限制", gt=0)
    
    @validator('entity_types')
    def validate_entity_types(cls, v):
        if v is not None:
            valid_types = {"brand", "ingredient", "product"}
            invalid_types = set(v) - valid_types
            if invalid_types:
                raise ValueError(f'不支持的实体类型: {list(invalid_types)}')
        return v

class BatchVectorizationData(BaseModel):
    """批量向量化响应数据"""
    task_id: str = Field(..., description="任务ID")
    message: str = Field(..., description="响应消息")
    status: VectorizationTaskStatus = Field(..., description="任务状态")

class BatchVectorizationResponse(BaseModel):
    """批量向量化响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[BatchVectorizationData] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")

class EntityTypeStats(BaseModel):
    """实体类型统计"""
    total_entities: int = Field(..., description="总实体数")
    vectorized_entities: int = Field(..., description="已向量化实体数")
    missing_vectors: int = Field(..., description="缺少向量的实体数")
    vectorization_rate: float = Field(..., description="向量化率(百分比)")

class VectorizationStatsSummary(BaseModel):
    """向量化统计汇总"""
    total_entities: int = Field(..., description="总实体数")
    vectorized_entities: int = Field(..., description="已向量化实体数")
    missing_vectors: int = Field(..., description="缺少向量的实体数")
    overall_vectorization_rate: float = Field(..., description="整体向量化率(百分比)")

class VectorizationStatsData(BaseModel):
    """向量化统计数据"""
    brand: Optional[EntityTypeStats] = Field(None, description="品牌统计")
    ingredient: Optional[EntityTypeStats] = Field(None, description="成分统计")
    product: Optional[EntityTypeStats] = Field(None, description="产品统计")
    summary: VectorizationStatsSummary = Field(..., description="汇总统计")

class VectorizationStatsResponse(BaseModel):
    """向量化统计响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[VectorizationStatsData] = Field(None, description="统计数据")
    error: Optional[str] = Field(None, description="错误信息")

class TaskStatusData(BaseModel):
    """任务状态数据"""
    task_id: str = Field(..., description="任务ID")
    status: VectorizationTaskStatus = Field(..., description="任务状态")
    progress: int = Field(..., description="进度百分比", ge=0, le=100)
    message: str = Field(..., description="状态消息")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")
    result: Optional[Dict[str, Any]] = Field(None, description="任务结果")
    error: Optional[str] = Field(None, description="错误信息")

class TaskStatusResponse(BaseModel):
    """任务状态响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[TaskStatusData] = Field(None, description="任务状态数据")
    error: Optional[str] = Field(None, description="错误信息")

class TaskSummary(BaseModel):
    """任务摘要"""
    task_id: str = Field(..., description="任务ID")
    status: VectorizationTaskStatus = Field(..., description="任务状态")
    progress: int = Field(..., description="进度百分比")
    message: str = Field(..., description="状态消息")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")

class TaskListData(BaseModel):
    """任务列表数据"""
    tasks: List[TaskSummary] = Field(..., description="任务列表")
    total: int = Field(..., description="总任务数")

class TaskListResponse(BaseModel):
    """任务列表响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[TaskListData] = Field(None, description="任务列表数据")
    error: Optional[str] = Field(None, description="错误信息")

class VectorizationResult(BaseModel):
    """向量化结果"""
    entity_type: str = Field(..., description="实体类型")
    total_found: int = Field(..., description="找到的实体总数")
    success_count: int = Field(..., description="成功向量化数量")
    failed_count: int = Field(..., description="失败数量")
    duration_seconds: float = Field(..., description="耗时秒数")
    average_time_per_entity: Optional[float] = Field(None, description="平均每个实体耗时")
    error: Optional[str] = Field(None, description="错误信息")

class BatchVectorizationSummary(BaseModel):
    """批量向量化汇总结果"""
    total_entities_processed: int = Field(..., description="处理的实体总数")
    total_entities_success: int = Field(..., description="成功的实体数")
    total_entities_failed: int = Field(..., description="失败的实体数")
    total_duration_seconds: Optional[float] = Field(None, description="总耗时")
    success_rate: float = Field(..., description="成功率百分比")
    results_by_type: Dict[str, VectorizationResult] = Field(..., description="按类型分组的结果")