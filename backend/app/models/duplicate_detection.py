"""
智能去重检测相关的数据模型
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime

class DuplicateDetectionRequest(BaseModel):
    """去重检测请求"""
    entity_name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型: brand, ingredient, product")
    limit: Optional[int] = Field(10, description="返回候选数量限制", ge=1, le=50)
    
    @validator('entity_name')
    def validate_entity_name(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('实体名称不能为空')
        if len(v.strip()) > 200:
            raise ValueError('实体名称长度不能超过200字符')
        return v.strip()
    
    @validator('entity_type')
    def validate_entity_type(cls, v):
        if v not in ['brand', 'ingredient', 'product']:
            raise ValueError('实体类型必须是: brand, ingredient, product')
        return v

class DuplicateCandidateModel(BaseModel):
    """重复候选实体模型"""
    entity_id: str = Field(..., description="实体UUID")
    entity_type: str = Field(..., description="实体类型")
    entity_name: str = Field(..., description="实体名称")
    similarity_score: float = Field(..., description="相似度分数", ge=0.0, le=1.0)
    risk_level: str = Field(..., description="风险等级: high, medium, low")
    recommendation: str = Field(..., description="推荐动作: link, create_new, manual_review")

class DuplicateDetectionResultModel(BaseModel):
    """去重检测结果模型"""
    query_text: str = Field(..., description="查询文本")
    entity_type: str = Field(..., description="实体类型")
    has_duplicates: bool = Field(..., description="是否有重复候选")
    candidates: List[DuplicateCandidateModel] = Field(default=[], description="候选实体列表")
    recommendation: str = Field(..., description="整体推荐动作")
    confidence: float = Field(..., description="推荐置信度", ge=0.0, le=1.0)

class DuplicateDetectionResponse(BaseModel):
    """去重检测响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[DuplicateDetectionResultModel] = Field(None, description="检测结果")
    error: Optional[str] = Field(None, description="错误信息")

class BatchEntityRequest(BaseModel):
    """批量实体请求项"""
    entity_name: str = Field(..., description="实体名称")
    entity_type: str = Field(..., description="实体类型")
    
    @validator('entity_name')
    def validate_entity_name(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('实体名称不能为空')
        if len(v.strip()) > 200:
            raise ValueError('实体名称长度不能超过200字符')
        return v.strip()
    
    @validator('entity_type')
    def validate_entity_type(cls, v):
        if v not in ['brand', 'ingredient', 'product']:
            raise ValueError('实体类型必须是: brand, ingredient, product')
        return v

class BatchDuplicateDetectionRequest(BaseModel):
    """批量去重检测请求"""
    entities: List[BatchEntityRequest] = Field(..., description="实体列表")
    
    @validator('entities')
    def validate_entities(cls, v):
        if not v or len(v) == 0:
            raise ValueError('实体列表不能为空')
        if len(v) > 100:
            raise ValueError('批量检测数量不能超过100个')
        return v

class BatchDetectionSummary(BaseModel):
    """批量检测摘要"""
    total_entities: int = Field(..., description="总实体数")
    link_recommended: int = Field(..., description="推荐链接的数量")
    manual_review_recommended: int = Field(..., description="推荐人工审核的数量")
    create_new_recommended: int = Field(..., description="推荐创建新实体的数量")
    total_candidates_found: int = Field(..., description="总候选数量")

class BatchDuplicateDetectionData(BaseModel):
    """批量去重检测数据"""
    results: List[DuplicateDetectionResultModel] = Field(..., description="检测结果列表")
    summary: BatchDetectionSummary = Field(..., description="检测摘要")

class BatchDuplicateDetectionResponse(BaseModel):
    """批量去重检测响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[BatchDuplicateDetectionData] = Field(None, description="检测数据")
    error: Optional[str] = Field(None, description="错误信息")

class EntityDetailsRequest(BaseModel):
    """实体详情请求"""
    entity_id: str = Field(..., description="实体UUID")
    entity_type: str = Field(..., description="实体类型")
    
    @validator('entity_id')
    def validate_entity_id(cls, v):
        if not v or len(v.strip()) < 1:
            raise ValueError('实体ID不能为空')
        return v.strip()
    
    @validator('entity_type')
    def validate_entity_type(cls, v):
        if v not in ['brand', 'ingredient', 'product']:
            raise ValueError('实体类型必须是: brand, ingredient, product')
        return v

class EntityDetailsResponse(BaseModel):
    """实体详情响应"""
    success: bool = Field(..., description="是否成功")
    data: Optional[Dict[str, Any]] = Field(None, description="实体详细信息")
    error: Optional[str] = Field(None, description="错误信息")

class DuplicateStats(BaseModel):
    """去重统计信息"""
    service_status: str = Field(..., description="服务状态")
    supported_entities: List[str] = Field(..., description="支持的实体类型")
    thresholds: Dict[str, Dict[str, float]] = Field(..., description="相似度阈值配置")