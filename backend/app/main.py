from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
from app.core.config import settings
from app.api.endpoints import extract, entities, products, extract_optimized, deduplication, entity_management, duplicate_detection, batch_vectorization, data_quality, graph_visualization, auth, admin_users, vectorization_management
from app.api.deps import check_service_health
from app.db.neo4j import neo4j_service
from app.db.sqlite import init_database, check_database_health
from app.services.vectorization_queue_service import start_queue_service, stop_queue_service

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version="1.0.0",
    description="Phase 1 - MVP核心功能",
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# CORS配置 - 支持局域网访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000", 
        "http://127.0.0.1:3000",
        "http://localhost:5173", 
        "http://127.0.0.1:5173"
    ],
    allow_origin_regex=r"http://.*:5173",  # 允许任何IP的5173端口访问
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(
    extract.router,
    prefix=settings.API_V1_STR,
    tags=["extraction"]
)
app.include_router(
    entities.router,
    prefix=settings.API_V1_STR,
    tags=["entities"]
)
app.include_router(
    products.router,
    prefix=settings.API_V1_STR,
    tags=["products"]
)

# Phase 2: 优化版提取功能路由
app.include_router(
    extract_optimized.router,
    prefix="/api/v2",
    tags=["extraction_optimized"]
)


# Phase 2 Week 2: 向量相似度和去重功能路由
app.include_router(
    deduplication.router,
    prefix="/api/v2",
    tags=["deduplication"]
)

# Phase 2 Entity Management: 实体管理功能路由
app.include_router(
    entity_management.router,
    prefix="/api/v2/entity",
    tags=["entity_management"]
)

# Phase 2 Duplicate Detection: 智能去重检测功能路由
app.include_router(
    duplicate_detection.router,
    prefix="/api/v2/duplicate",
    tags=["duplicate_detection"]
)

# Phase 2 Batch Vectorization: 批量向量化工具路由
app.include_router(
    batch_vectorization.router,
    prefix="/api/v2/batch-vectorization",
    tags=["batch_vectorization"]
)

# Phase 2 Data Quality: 数据质量监控功能路由
app.include_router(
    data_quality.router,
    prefix="/api/v2/quality",
    tags=["data_quality"]
)

# Phase 2 Graph Visualization: 图数据可视化功能路由
app.include_router(
    graph_visualization.router,
    prefix="/api/v2/graph",
    tags=["graph_visualization"]
)

# Authentication: 用户认证路由
app.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

# Admin User Management: 用户管理路由
app.include_router(
    admin_users.router,
    prefix="/admin",
    tags=["user_management"]
)

# Vectorization Management: 向量化2.0任务管理路由
app.include_router(
    vectorization_management.router,
    prefix="/api/v2/vectorization",
    tags=["vectorization_management"]
)

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    try:
        logger.info("Starting application...")
        
        # 初始化SQLite数据库和默认用户
        try:
            init_database()
            logger.info("SQLite database initialized successfully")
        except Exception as e:
            logger.error(f"SQLite database initialization failed: {e}")
            raise
        
        # 初始化Neo4j连接
        try:
            await neo4j_service.connect()
            await neo4j_service.init_database()
            logger.info("Neo4j connected successfully")
        except Exception as e:
            logger.warning(f"Neo4j connection failed: {e}, some features may not work")
        
        # 启动向量化队列服务
        try:
            await start_queue_service()
            logger.info("Vectorization queue service started successfully")
        except Exception as e:
            logger.warning(f"Vectorization queue service failed to start: {e}")
        
        logger.info("Application started successfully")
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    try:
        logger.info("Shutting down application...")
        
        # 停止向量化队列服务
        try:
            await stop_queue_service()
            logger.info("Vectorization queue service stopped")
        except Exception as e:
            logger.error(f"Error stopping queue service: {e}")
        
        # 关闭数据库连接
        await neo4j_service.close()
        
        logger.info("Application shutdown completed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")

@app.get("/health")
async def health_check():
    """健康检查端点"""
    health_status = await check_service_health()
    
    # 添加SQLite数据库健康检查
    sqlite_status = "connected" if check_database_health() else "disconnected"
    health_status["sqlite"] = sqlite_status
    
    return {
        "status": "healthy" if health_status.get("neo4j") == "connected" and sqlite_status == "connected" else "unhealthy",
        "project": settings.PROJECT_NAME,
        "version": "1.0.0",
        "services": health_status
    }

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": f"欢迎使用{settings.PROJECT_NAME}",
        "docs": "/docs",
        "health": "/health",
        "api": settings.API_V1_STR
    }