"""
图数据可视化服务
提供知识图谱的数据查询和可视化支持
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import json

from app.db.neo4j import neo4j_service
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class GraphNode:
    """图节点"""
    id: str
    label: str
    name: str
    entity_type: str
    properties: Dict[str, Any]
    
@dataclass
class GraphEdge:
    """图边"""
    id: str
    source: str
    target: str
    relationship: str
    properties: Dict[str, Any]

@dataclass
class GraphData:
    """图数据"""
    nodes: List[GraphNode]
    edges: List[GraphEdge]
    stats: Dict[str, Any]

@dataclass
class GraphStats:
    """图统计信息"""
    total_nodes: int
    total_edges: int
    node_counts_by_type: Dict[str, int]
    edge_counts_by_type: Dict[str, int]
    density: float
    components: int

class GraphVisualizationService:
    """图数据可视化服务"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        
        # 可视化配置
        self.node_colors = {
            "brand": "#FF6B6B",      # 红色 - 品牌
            "ingredient": "#4ECDC4",  # 青色 - 成分
            "product": "#45B7D1"      # 蓝色 - 产品
        }
        
        self.node_sizes = {
            "brand": 20,
            "ingredient": 15,
            "product": 25
        }
        
        logger.info("GraphVisualizationService initialized")
    
    async def get_graph_overview(self, limit: int = 100) -> GraphData:
        """
        获取知识图谱概览数据
        
        Args:
            limit: 返回节点数量限制
            
        Returns:
            图数据
        """
        try:
            logger.info(f"获取图谱概览数据，限制 {limit} 个节点")
            
            # 查询节点和关系
            query = f"""
            MATCH (n)
            WHERE n:{settings.NEO4J_LABEL_PREFIX}_Brand 
               OR n:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
               OR n:{settings.NEO4J_LABEL_PREFIX}_Product
            WITH n
            LIMIT {limit}
            
            OPTIONAL MATCH (n)-[r]->(m)
            WHERE m:{settings.NEO4J_LABEL_PREFIX}_Brand 
               OR m:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
               OR m:{settings.NEO4J_LABEL_PREFIX}_Product
            
            RETURN 
                n.uuid as node_id, 
                labels(n)[0] as node_label,
                n.name as node_name,
                properties(n) as node_props,
                r.uuid as edge_id,
                type(r) as edge_type,
                properties(r) as edge_props,
                m.uuid as target_id
            """
            
            result = await self.neo4j.run_query(query)
            
            # 处理节点和边
            nodes_dict = {}
            edges_dict = {}
            
            for record in result:
                # 处理源节点
                node_id = record["node_id"]
                if node_id and node_id not in nodes_dict:
                    entity_type = self._get_entity_type_from_label(record["node_label"])
                    
                    node = GraphNode(
                        id=node_id,
                        label=record["node_label"],
                        name=record["node_name"],
                        entity_type=entity_type,
                        properties=record["node_props"] or {}
                    )
                    nodes_dict[node_id] = node
                
                # 处理边
                if record["edge_id"] and record["target_id"]:
                    edge_id = f"{node_id}_{record['target_id']}_{record['edge_type']}"
                    if edge_id not in edges_dict:
                        edge = GraphEdge(
                            id=edge_id,
                            source=node_id,
                            target=record["target_id"],
                            relationship=record["edge_type"],
                            properties=record["edge_props"] or {}
                        )
                        edges_dict[edge_id] = edge
            
            # 确保目标节点也在节点列表中
            target_nodes_query = f"""
            MATCH (n)-[r]->(m)
            WHERE n.uuid IN {list(nodes_dict.keys())[:50]}  // 限制查询数量
            AND (m:{settings.NEO4J_LABEL_PREFIX}_Brand 
                 OR m:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
                 OR m:{settings.NEO4J_LABEL_PREFIX}_Product)
            RETURN DISTINCT 
                m.uuid as node_id,
                labels(m)[0] as node_label,
                m.name as node_name,
                properties(m) as node_props
            """
            
            if nodes_dict:  # 只有当有节点时才查询
                target_result = await self.neo4j.run_query(target_nodes_query)
                
                for record in target_result:
                    node_id = record["node_id"]
                    if node_id and node_id not in nodes_dict:
                        entity_type = self._get_entity_type_from_label(record["node_label"])
                        
                        node = GraphNode(
                            id=node_id,
                            label=record["node_label"],
                            name=record["node_name"],
                            entity_type=entity_type,
                            properties=record["node_props"] or {}
                        )
                        nodes_dict[node_id] = node
            
            # 获取统计信息
            stats = await self._get_basic_stats()
            
            graph_data = GraphData(
                nodes=list(nodes_dict.values()),
                edges=list(edges_dict.values()),
                stats=stats
            )
            
            logger.info(f"图谱概览数据获取完成: {len(graph_data.nodes)} 个节点, {len(graph_data.edges)} 条边")
            return graph_data
            
        except Exception as e:
            logger.error(f"获取图谱概览失败: {e}")
            raise
    
    async def get_entity_subgraph(
        self, 
        entity_id: str, 
        depth: int = 2,
        limit: int = 50
    ) -> GraphData:
        """
        获取以指定实体为中心的子图
        
        Args:
            entity_id: 中心实体ID
            depth: 遍历深度
            limit: 节点数量限制
            
        Returns:
            子图数据
        """
        try:
            logger.info(f"获取实体子图: {entity_id}, 深度: {depth}")
            
            # 查询指定深度的邻居节点
            query = f"""
            MATCH path = (center)-[*1..{depth}]-(neighbor)
            WHERE center.uuid = $entity_id
            AND (center:{settings.NEO4J_LABEL_PREFIX}_Brand 
                 OR center:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
                 OR center:{settings.NEO4J_LABEL_PREFIX}_Product)
            AND (neighbor:{settings.NEO4J_LABEL_PREFIX}_Brand 
                 OR neighbor:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
                 OR neighbor:{settings.NEO4J_LABEL_PREFIX}_Product)
            
            WITH DISTINCT neighbor, center
            LIMIT {limit}
            
            MATCH (neighbor)-[r]-(other)
            WHERE other.uuid = center.uuid 
               OR other.uuid IN [n.uuid | n IN collect(neighbor)]
            
            RETURN DISTINCT
                neighbor.uuid as node_id,
                labels(neighbor)[0] as node_label,
                neighbor.name as node_name,
                properties(neighbor) as node_props,
                r.uuid as edge_id,
                type(r) as edge_type,
                properties(r) as edge_props,
                other.uuid as other_id,
                startNode(r).uuid as start_id,
                endNode(r).uuid as end_id
            """
            
            result = await self.neo4j.run_query(query, {"entity_id": entity_id})
            
            # 处理结果
            nodes_dict = {}
            edges_dict = {}
            
            # 添加中心节点
            center_query = f"""
            MATCH (n)
            WHERE n.uuid = $entity_id
            RETURN n.uuid as node_id, labels(n)[0] as node_label, 
                   n.name as node_name, properties(n) as node_props
            """
            
            center_result = await self.neo4j.run_query(center_query, {"entity_id": entity_id})
            if center_result:
                center_record = center_result[0]
                entity_type = self._get_entity_type_from_label(center_record["node_label"])
                
                center_node = GraphNode(
                    id=center_record["node_id"],
                    label=center_record["node_label"],
                    name=center_record["node_name"],
                    entity_type=entity_type,
                    properties=center_record["node_props"] or {}
                )
                nodes_dict[entity_id] = center_node
            
            # 处理邻居节点和边
            for record in result:
                # 添加邻居节点
                node_id = record["node_id"]
                if node_id and node_id not in nodes_dict:
                    entity_type = self._get_entity_type_from_label(record["node_label"])
                    
                    node = GraphNode(
                        id=node_id,
                        label=record["node_label"],
                        name=record["node_name"],
                        entity_type=entity_type,
                        properties=record["node_props"] or {}
                    )
                    nodes_dict[node_id] = node
                
                # 添加边
                if record["edge_id"] and record["start_id"] and record["end_id"]:
                    edge_id = record["edge_id"] or f"{record['start_id']}_{record['end_id']}_{record['edge_type']}"
                    if edge_id not in edges_dict:
                        edge = GraphEdge(
                            id=edge_id,
                            source=record["start_id"],
                            target=record["end_id"],
                            relationship=record["edge_type"],
                            properties=record["edge_props"] or {}
                        )
                        edges_dict[edge_id] = edge
            
            # 获取子图统计
            subgraph_stats = {
                "center_entity": entity_id,
                "depth": depth,
                "node_count": len(nodes_dict),
                "edge_count": len(edges_dict)
            }
            
            graph_data = GraphData(
                nodes=list(nodes_dict.values()),
                edges=list(edges_dict.values()),
                stats=subgraph_stats
            )
            
            logger.info(f"实体子图获取完成: {len(graph_data.nodes)} 个节点, {len(graph_data.edges)} 条边")
            return graph_data
            
        except Exception as e:
            logger.error(f"获取实体子图失败: {e}")
            raise
    
    async def get_graph_statistics(self) -> GraphStats:
        """获取图统计信息"""
        try:
            logger.info("获取图统计信息")
            
            # 获取节点统计
            node_stats_query = f"""
            MATCH (n)
            WHERE n:{settings.NEO4J_LABEL_PREFIX}_Brand 
               OR n:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
               OR n:{settings.NEO4J_LABEL_PREFIX}_Product
            RETURN labels(n)[0] as label, count(n) as count
            """
            
            node_result = await self.neo4j.run_query(node_stats_query)
            
            node_counts = {}
            total_nodes = 0
            for record in node_result:
                entity_type = self._get_entity_type_from_label(record["label"])
                count = record["count"]
                node_counts[entity_type] = count
                total_nodes += count
            
            # 获取边统计
            edge_stats_query = f"""
            MATCH (n)-[r]->(m)
            WHERE (n:{settings.NEO4J_LABEL_PREFIX}_Brand 
                   OR n:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
                   OR n:{settings.NEO4J_LABEL_PREFIX}_Product)
            AND (m:{settings.NEO4J_LABEL_PREFIX}_Brand 
                 OR m:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
                 OR m:{settings.NEO4J_LABEL_PREFIX}_Product)
            RETURN type(r) as relationship, count(r) as count
            """
            
            edge_result = await self.neo4j.run_query(edge_stats_query)
            
            edge_counts = {}
            total_edges = 0
            for record in edge_result:
                relationship = record["relationship"]
                count = record["count"]
                edge_counts[relationship] = count
                total_edges += count
            
            # 计算图密度
            max_edges = total_nodes * (total_nodes - 1)
            density = (total_edges / max_edges) if max_edges > 0 else 0.0
            
            # 简化连通分量计算（暂时设为1）
            components = 1
            
            stats = GraphStats(
                total_nodes=total_nodes,
                total_edges=total_edges,
                node_counts_by_type=node_counts,
                edge_counts_by_type=edge_counts,
                density=density,
                components=components
            )
            
            logger.info(f"图统计获取完成: {total_nodes} 个节点, {total_edges} 条边")
            return stats
            
        except Exception as e:
            logger.error(f"获取图统计失败: {e}")
            raise
    
    async def search_entities_for_visualization(
        self, 
        query: str, 
        entity_types: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        搜索实体用于可视化
        
        Args:
            query: 搜索查询
            entity_types: 限制的实体类型
            limit: 返回数量限制
            
        Returns:
            实体列表
        """
        try:
            logger.info(f"搜索实体: '{query}', 类型: {entity_types}")
            
            # 构建标签条件
            label_conditions = []
            if not entity_types:
                entity_types = ["brand", "ingredient", "product"]
            
            for entity_type in entity_types:
                label_map = {
                    "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                    "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                    "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
                }
                
                label = label_map.get(entity_type)
                if label:
                    label_conditions.append(f"n:{label}")
            
            if not label_conditions:
                return []
            
            where_condition = " OR ".join(label_conditions)
            
            search_query = f"""
            MATCH (n)
            WHERE ({where_condition})
            AND toLower(n.name) CONTAINS toLower($query)
            
            OPTIONAL MATCH (n)-[r]-()
            WITH n, count(r) as degree
            
            RETURN n.uuid as id, 
                   labels(n)[0] as label,
                   n.name as name,
                   properties(n) as properties,
                   degree
            ORDER BY degree DESC, n.name
            LIMIT $limit
            """
            
            result = await self.neo4j.run_query(search_query, {
                "query": query,
                "limit": limit
            })
            
            entities = []
            for record in result:
                entity_type = self._get_entity_type_from_label(record["label"])
                
                entity = {
                    "id": record["id"],
                    "name": record["name"],
                    "entity_type": entity_type,
                    "label": record["label"],
                    "properties": record["properties"] or {},
                    "degree": record["degree"]
                }
                entities.append(entity)
            
            logger.info(f"搜索完成: 找到 {len(entities)} 个实体")
            return entities
            
        except Exception as e:
            logger.error(f"搜索实体失败: {e}")
            return []
    
    def get_visualization_config(self) -> Dict[str, Any]:
        """获取可视化配置"""
        return {
            "node_colors": self.node_colors,
            "node_sizes": self.node_sizes,
            "layout_options": [
                {"value": "force", "label": "力导向布局"},
                {"value": "hierarchical", "label": "分层布局"},
                {"value": "circular", "label": "环形布局"},
                {"value": "grid", "label": "网格布局"}
            ],
            "edge_styles": {
                "MANUFACTURED_BY": {"color": "#666", "width": 2},
                "CONTAINS": {"color": "#999", "width": 1}
            }
        }
    
    def _get_entity_type_from_label(self, label: str) -> str:
        """从标签获取实体类型"""
        if not label:
            return "unknown"
        
        prefix = settings.NEO4J_LABEL_PREFIX + "_"
        if label.startswith(prefix):
            return label[len(prefix):].lower()
        
        return label.lower()
    
    async def _get_basic_stats(self) -> Dict[str, Any]:
        """获取基础统计信息"""
        try:
            stats_query = f"""
            MATCH (n)
            WHERE n:{settings.NEO4J_LABEL_PREFIX}_Brand 
               OR n:{settings.NEO4J_LABEL_PREFIX}_Ingredient 
               OR n:{settings.NEO4J_LABEL_PREFIX}_Product
            WITH labels(n)[0] as label, count(n) as count
            RETURN label, count
            """
            
            result = await self.neo4j.run_query(stats_query)
            
            stats = {"node_counts": {}, "total_nodes": 0}
            
            for record in result:
                entity_type = self._get_entity_type_from_label(record["label"])
                count = record["count"]
                stats["node_counts"][entity_type] = count
                stats["total_nodes"] += count
            
            return stats
            
        except Exception as e:
            logger.error(f"获取基础统计失败: {e}")
            return {"node_counts": {}, "total_nodes": 0}

# 全局实例
_graph_visualization_service = None

def get_graph_visualization_service() -> GraphVisualizationService:
    """获取图可视化服务实例（单例模式）"""
    global _graph_visualization_service
    if _graph_visualization_service is None:
        _graph_visualization_service = GraphVisualizationService()
    return _graph_visualization_service