"""
数据质量监控服务
监控知识图谱中的数据质量，包括重复检测、完整性检查等
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio

from app.db.neo4j import neo4j_service
from app.services.vector_storage_service import get_vector_storage_service
from app.services.duplicate_detection_service import get_duplicate_detection_service
from app.services.vectorization_monitor_service import get_monitor_service
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class DataQualityIssue:
    """数据质量问题"""
    issue_type: str  # duplicate, missing_data, invalid_format, orphan_node
    entity_type: str
    entity_id: str
    entity_name: str
    severity: str  # critical, warning, info
    description: str
    suggested_action: str
    details: Dict[str, Any] = None

@dataclass 
class DataQualityReport:
    """数据质量报告"""
    report_id: str
    generated_at: datetime
    total_entities: int
    issues_found: int
    critical_issues: int
    warning_issues: int
    info_issues: int
    overall_score: float  # 0-100
    issues_by_type: Dict[str, int]
    issues_by_entity: Dict[str, int]
    issues: List[DataQualityIssue]
    recommendations: List[str]

class DataQualityService:
    """数据质量监控服务"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        self.vector_service = get_vector_storage_service()
        self.duplicate_service = get_duplicate_detection_service()
        self.monitor_service = get_monitor_service()
        
        # 质量检查配置
        self.quality_checks = {
            "duplicate_detection": True,
            "missing_vectors": True,
            "orphan_nodes": True,
            "missing_relationships": True,
            "data_completeness": True,
            "naming_consistency": True,
            "vectorization_status": True,     # 新增：向量化状态检查
            "vectorization_failures": True,   # 新增：向量化失败率检查
            "vector_integrity": True          # 新增：向量数据完整性检查
        }
        
        # 严重性阈值
        self.severity_thresholds = {
            "duplicate_similarity": 0.95,  # 高于此相似度认为是重复
            "missing_vector_ratio": 0.1,   # 缺失向量比例高于此值发出警告
            "orphan_node_ratio": 0.05,     # 孤立节点比例高于此值发出警告
            "vectorization_failure_rate": 0.1,  # 向量化失败率高于此值发出警告
            "vector_coverage_threshold": 0.8     # 向量化覆盖率低于此值发出警告
        }
        
        logger.info("DataQualityService initialized")
    
    async def generate_quality_report(
        self, 
        entity_types: Optional[List[str]] = None,
        check_types: Optional[List[str]] = None
    ) -> DataQualityReport:
        """
        生成数据质量报告
        
        Args:
            entity_types: 检查的实体类型列表
            check_types: 检查类型列表
            
        Returns:
            数据质量报告
        """
        try:
            import uuid
            
            report_id = str(uuid.uuid4())
            start_time = datetime.now()
            
            logger.info(f"开始生成数据质量报告: {report_id}")
            
            # 默认检查所有实体类型和检查项
            if not entity_types:
                entity_types = ["brand", "ingredient", "product"]
            
            if not check_types:
                check_types = list(self.quality_checks.keys())
            
            # 获取实体统计
            entity_stats = await self._get_entity_statistics(entity_types)
            total_entities = sum(entity_stats.values())
            
            # 执行各项检查
            all_issues = []
            
            for check_type in check_types:
                if not self.quality_checks.get(check_type, False):
                    continue
                
                logger.info(f"执行检查: {check_type}")
                issues = await self._run_quality_check(check_type, entity_types)
                all_issues.extend(issues)
                
                # 避免过度调用API
                await asyncio.sleep(0.5)
            
            # 统计问题
            issues_by_type = {}
            issues_by_entity = {}
            severity_counts = {"critical": 0, "warning": 0, "info": 0}
            
            for issue in all_issues:
                # 按类型统计
                issues_by_type[issue.issue_type] = issues_by_type.get(issue.issue_type, 0) + 1
                
                # 按实体类型统计
                issues_by_entity[issue.entity_type] = issues_by_entity.get(issue.entity_type, 0) + 1
                
                # 按严重性统计
                severity_counts[issue.severity] += 1
            
            # 计算质量评分
            overall_score = self._calculate_quality_score(
                total_entities, 
                len(all_issues), 
                severity_counts
            )
            
            # 生成建议
            recommendations = self._generate_recommendations(all_issues, entity_stats)
            
            # 创建报告
            report = DataQualityReport(
                report_id=report_id,
                generated_at=start_time,
                total_entities=total_entities,
                issues_found=len(all_issues),
                critical_issues=severity_counts["critical"],
                warning_issues=severity_counts["warning"],
                info_issues=severity_counts["info"],
                overall_score=overall_score,
                issues_by_type=issues_by_type,
                issues_by_entity=issues_by_entity,
                issues=all_issues,
                recommendations=recommendations
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"数据质量报告生成完成: {report_id}, 耗时 {duration:.2f} 秒")
            logger.info(f"发现 {len(all_issues)} 个问题, 质量评分: {overall_score:.1f}")
            
            return report
            
        except Exception as e:
            logger.error(f"生成数据质量报告失败: {e}")
            raise
    
    async def _get_entity_statistics(self, entity_types: List[str]) -> Dict[str, int]:
        """获取实体统计信息"""
        try:
            stats = {}
            
            for entity_type in entity_types:
                label_map = {
                    "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                    "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                    "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
                }
                
                label = label_map.get(entity_type)
                if not label:
                    continue
                
                query = f"MATCH (n:{label}) RETURN count(n) as count"
                result = await self.neo4j.run_query(query)
                
                stats[entity_type] = result[0]["count"] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"获取实体统计失败: {e}")
            return {}
    
    async def _run_quality_check(
        self, 
        check_type: str, 
        entity_types: List[str]
    ) -> List[DataQualityIssue]:
        """运行指定的质量检查"""
        try:
            issues = []
            
            if check_type == "duplicate_detection":
                issues.extend(await self._check_duplicates(entity_types))
            elif check_type == "missing_vectors":
                issues.extend(await self._check_missing_vectors(entity_types))
            elif check_type == "orphan_nodes":
                issues.extend(await self._check_orphan_nodes(entity_types))
            elif check_type == "missing_relationships":
                issues.extend(await self._check_missing_relationships())
            elif check_type == "data_completeness":
                issues.extend(await self._check_data_completeness(entity_types))
            elif check_type == "naming_consistency":
                issues.extend(await self._check_naming_consistency(entity_types))
            elif check_type == "vectorization_status":
                issues.extend(await self._check_vectorization_status(entity_types))
            elif check_type == "vectorization_failures":
                issues.extend(await self._check_vectorization_failures())
            elif check_type == "vector_integrity":
                issues.extend(await self._check_vector_integrity(entity_types))
            
            return issues
            
        except Exception as e:
            logger.error(f"质量检查失败 {check_type}: {e}")
            return []
    
    async def _check_duplicates(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查重复实体"""
        try:
            issues = []
            
            for entity_type in entity_types:
                # 获取所有实体进行重复检查
                entities = await self._get_all_entities_for_check(entity_type)
                
                # 限制检查数量避免过载
                if len(entities) > 100:
                    entities = entities[:100]
                    logger.warning(f"限制 {entity_type} 重复检查数量为 100")
                
                for entity in entities:
                    try:
                        result = await self.duplicate_service.detect_duplicates(
                            entity_name=entity["name"],
                            entity_type=entity_type,
                            limit=5
                        )
                        
                        # 查找高相似度候选（排除自己）
                        high_similarity_candidates = [
                            c for c in result.candidates 
                            if c.entity_id != entity["id"] and 
                               c.similarity_score >= self.severity_thresholds["duplicate_similarity"]
                        ]
                        
                        if high_similarity_candidates:
                            candidate = high_similarity_candidates[0]
                            
                            issue = DataQualityIssue(
                                issue_type="duplicate",
                                entity_type=entity_type,
                                entity_id=entity["id"],
                                entity_name=entity["name"],
                                severity="critical" if candidate.similarity_score > 0.98 else "warning",
                                description=f"发现疑似重复实体",
                                suggested_action="检查并考虑合并重复实体",
                                details={
                                    "duplicate_candidate": {
                                        "id": candidate.entity_id,
                                        "name": candidate.entity_name,
                                        "similarity_score": candidate.similarity_score
                                    },
                                    "all_candidates": len(result.candidates)
                                }
                            )
                            issues.append(issue)
                        
                        # 避免频繁调用
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.warning(f"检查实体重复失败 {entity['name']}: {e}")
                        continue
            
            logger.info(f"重复检查完成: 发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"重复检查失败: {e}")
            return []
    
    async def _check_missing_vectors(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查缺失向量"""
        try:
            issues = []
            
            for entity_type in entity_types:
                # 获取没有向量的实体
                missing_vector_entities = await self.vector_service.get_entities_without_vectors(entity_type)
                
                # 获取总实体数
                total_count = await self._get_entity_count(entity_type)
                
                if total_count > 0:
                    missing_ratio = len(missing_vector_entities) / total_count
                    
                    if missing_ratio > self.severity_thresholds["missing_vector_ratio"]:
                        severity = "critical" if missing_ratio > 0.5 else "warning"
                        
                        issue = DataQualityIssue(
                            issue_type="missing_vectors",
                            entity_type=entity_type,
                            entity_id="batch",
                            entity_name=f"{entity_type}_batch",
                            severity=severity,
                            description=f"{len(missing_vector_entities)} 个 {entity_type} 实体缺少向量 ({missing_ratio:.1%})",
                            suggested_action="运行批量向量化工具",
                            details={
                                "missing_count": len(missing_vector_entities),
                                "total_count": total_count,
                                "missing_ratio": missing_ratio
                            }
                        )
                        issues.append(issue)
                
                # 为每个缺失向量的实体创建个别问题
                for entity in missing_vector_entities[:10]:  # 限制显示前10个
                    issue = DataQualityIssue(
                        issue_type="missing_vectors",
                        entity_type=entity_type,
                        entity_id=entity["id"],
                        entity_name=entity["name"],
                        severity="info",
                        description="实体缺少向量",
                        suggested_action="生成向量"
                    )
                    issues.append(issue)
            
            logger.info(f"向量检查完成: 发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"向量检查失败: {e}")
            return []
    
    async def _check_orphan_nodes(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查孤立节点"""
        try:
            issues = []
            
            # 检查品牌孤立节点（没有产品关联）
            if "brand" in entity_types:
                query = """
                MATCH (b:KGMS_Brand)
                WHERE NOT EXISTS { (b)<-[:MANUFACTURED_BY]-(:KGMS_Product) }
                RETURN b.uuid as id, b.name as name, b.created_at as created_at
                ORDER BY b.created_at DESC
                LIMIT 20
                """
                result = await self.neo4j.run_query(query)
                
                for record in result:
                    # 检查是否是最近创建的（可能正在使用中）
                    created_at = record.get("created_at")
                    severity = "info"
                    
                    try:
                        if created_at and hasattr(created_at, 'to_native'):
                            created_time = created_at.to_native()
                            if datetime.now() - created_time > timedelta(days=7):
                                severity = "warning"
                    except:
                        severity = "warning"
                    
                    issue = DataQualityIssue(
                        issue_type="orphan_node",
                        entity_type="brand",
                        entity_id=record["id"],
                        entity_name=record["name"],
                        severity=severity,
                        description="品牌没有关联任何产品",
                        suggested_action="检查是否需要删除未使用的品牌或创建相关产品"
                    )
                    issues.append(issue)
            
            # 检查成分孤立节点
            if "ingredient" in entity_types:
                query = """
                MATCH (i:KGMS_Ingredient)
                WHERE NOT EXISTS { (i)<-[:CONTAINS]-(:KGMS_Product) }
                RETURN i.uuid as id, i.name as name, i.created_at as created_at
                ORDER BY i.created_at DESC
                LIMIT 20
                """
                result = await self.neo4j.run_query(query)
                
                for record in result:
                    severity = "info"
                    
                    try:
                        created_at = record.get("created_at")
                        if created_at and hasattr(created_at, 'to_native'):
                            created_time = created_at.to_native()
                            if datetime.now() - created_time > timedelta(days=7):
                                severity = "warning"
                    except:
                        severity = "warning"
                    
                    issue = DataQualityIssue(
                        issue_type="orphan_node",
                        entity_type="ingredient",
                        entity_id=record["id"],
                        entity_name=record["name"],
                        severity=severity,
                        description="成分没有被任何产品包含",
                        suggested_action="检查是否需要删除未使用的成分或创建相关产品"
                    )
                    issues.append(issue)
            
            logger.info(f"孤立节点检查完成: 发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"孤立节点检查失败: {e}")
            return []
    
    async def _check_missing_relationships(self) -> List[DataQualityIssue]:
        """检查缺失关系"""
        try:
            issues = []
            
            # 检查产品没有品牌关系
            query = """
            MATCH (p:KGMS_Product)
            WHERE NOT EXISTS { (p)-[:MANUFACTURED_BY]->(:KGMS_Brand) }
            RETURN p.uuid as id, p.name as name
            LIMIT 10
            """
            result = await self.neo4j.run_query(query)
            
            for record in result:
                issue = DataQualityIssue(
                    issue_type="missing_relationships",
                    entity_type="product",
                    entity_id=record["id"],
                    entity_name=record["name"],
                    severity="critical",
                    description="产品没有关联品牌",
                    suggested_action="为产品添加品牌关系"
                )
                issues.append(issue)
            
            # 检查产品没有成分关系
            query = """
            MATCH (p:KGMS_Product)
            WHERE NOT EXISTS { (p)-[:CONTAINS]->(:KGMS_Ingredient) }
            RETURN p.uuid as id, p.name as name
            LIMIT 10
            """
            result = await self.neo4j.run_query(query)
            
            for record in result:
                issue = DataQualityIssue(
                    issue_type="missing_relationships",
                    entity_type="product",
                    entity_id=record["id"],
                    entity_name=record["name"],
                    severity="warning",
                    description="产品没有关联任何成分",
                    suggested_action="为产品添加成分信息"
                )
                issues.append(issue)
            
            logger.info(f"关系检查完成: 发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"关系检查失败: {e}")
            return []
    
    async def _check_data_completeness(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查数据完整性"""
        try:
            issues = []
            
            # 检查产品缺少SKU
            if "product" in entity_types:
                query = """
                MATCH (p:KGMS_Product)
                WHERE p.sku IS NULL OR p.sku = ""
                RETURN p.uuid as id, p.name as name
                LIMIT 10
                """
                result = await self.neo4j.run_query(query)
                
                for record in result:
                    issue = DataQualityIssue(
                        issue_type="data_completeness",
                        entity_type="product",
                        entity_id=record["id"],
                        entity_name=record["name"],
                        severity="info",
                        description="产品缺少SKU信息",
                        suggested_action="补充产品SKU信息"
                    )
                    issues.append(issue)
                
                # 检查产品缺少功效描述
                query = """
                MATCH (p:KGMS_Product)
                WHERE p.benefits IS NULL OR p.benefits = ""
                RETURN p.uuid as id, p.name as name
                LIMIT 10
                """
                result = await self.neo4j.run_query(query)
                
                for record in result:
                    issue = DataQualityIssue(
                        issue_type="data_completeness",
                        entity_type="product",
                        entity_id=record["id"],
                        entity_name=record["name"],
                        severity="info",
                        description="产品缺少功效描述",
                        suggested_action="补充产品功效信息"
                    )
                    issues.append(issue)
            
            logger.info(f"完整性检查完成: 发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"完整性检查失败: {e}")
            return []
    
    async def _check_naming_consistency(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查命名一致性"""
        try:
            issues = []
            
            for entity_type in entity_types:
                label_map = {
                    "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                    "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                    "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
                }
                
                label = label_map.get(entity_type)
                if not label:
                    continue
                
                # 检查名称中的特殊字符或格式问题
                query = f"""
                MATCH (n:{label})
                WHERE n.name =~ '.*[\\s]{{2,}}.*'  // 多个连续空格
                   OR n.name =~ '^\\s.*'          // 开头空格
                   OR n.name =~ '.*\\s$'          // 结尾空格
                   OR size(n.name) < 2            // 名称过短
                   OR size(n.name) > 100          // 名称过长
                RETURN n.uuid as id, n.name as name
                LIMIT 10
                """
                
                result = await self.neo4j.run_query(query)
                
                for record in result:
                    name = record["name"]
                    problem = ""
                    
                    if len(name) < 2:
                        problem = "名称过短"
                    elif len(name) > 100:
                        problem = "名称过长"
                    elif name.startswith(" ") or name.endswith(" "):
                        problem = "名称包含前导或尾随空格"
                    elif "  " in name:
                        problem = "名称包含多个连续空格"
                    
                    issue = DataQualityIssue(
                        issue_type="naming_consistency",
                        entity_type=entity_type,
                        entity_id=record["id"],
                        entity_name=name,
                        severity="info",
                        description=f"命名格式问题: {problem}",
                        suggested_action="修正实体名称格式"
                    )
                    issues.append(issue)
            
            logger.info(f"命名一致性检查完成: 发现 {len(issues)} 个问题")
            return issues
            
        except Exception as e:
            logger.error(f"命名一致性检查失败: {e}")
            return []
    
    def _calculate_quality_score(
        self, 
        total_entities: int, 
        total_issues: int, 
        severity_counts: Dict[str, int]
    ) -> float:
        """计算质量评分"""
        try:
            if total_entities == 0:
                return 100.0
            
            # 基础分数
            base_score = 100.0
            
            # 按严重性扣分
            critical_penalty = severity_counts["critical"] * 10
            warning_penalty = severity_counts["warning"] * 5
            info_penalty = severity_counts["info"] * 1
            
            total_penalty = critical_penalty + warning_penalty + info_penalty
            
            # 计算最终分数
            final_score = max(0.0, base_score - (total_penalty / total_entities * 100))
            
            return round(final_score, 1)
            
        except Exception as e:
            logger.error(f"计算质量评分失败: {e}")
            return 0.0
    
    def _generate_recommendations(
        self, 
        issues: List[DataQualityIssue], 
        entity_stats: Dict[str, int]
    ) -> List[str]:
        """生成改进建议"""
        try:
            recommendations = []
            
            # 统计问题类型
            issue_counts = {}
            for issue in issues:
                issue_counts[issue.issue_type] = issue_counts.get(issue.issue_type, 0) + 1
            
            # 生成针对性建议
            if issue_counts.get("duplicate", 0) > 0:
                recommendations.append(f"发现 {issue_counts['duplicate']} 个重复实体问题，建议使用去重检测工具处理")
            
            if issue_counts.get("missing_vectors", 0) > 0:
                recommendations.append(f"发现 {issue_counts['missing_vectors']} 个缺失向量问题，建议运行批量向量化工具")
            
            if issue_counts.get("orphan_node", 0) > 0:
                recommendations.append(f"发现 {issue_counts['orphan_node']} 个孤立节点，建议清理未使用的实体")
            
            if issue_counts.get("missing_relationships", 0) > 0:
                recommendations.append(f"发现 {issue_counts['missing_relationships']} 个缺失关系问题，建议完善实体关联")
            
            if issue_counts.get("data_completeness", 0) > 0:
                recommendations.append(f"发现 {issue_counts['data_completeness']} 个数据完整性问题，建议补充缺失信息")
            
            # 通用建议
            total_issues = len(issues)
            if total_issues == 0:
                recommendations.append("数据质量良好，建议继续保持")
            elif total_issues < 10:
                recommendations.append("数据质量总体良好，建议修复少量问题")
            elif total_issues < 50:
                recommendations.append("存在一定数量的数据质量问题，建议优先处理严重问题")
            else:
                recommendations.append("数据质量问题较多，建议制定系统的数据清理计划")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            return ["建议定期检查数据质量"]
    
    async def _get_all_entities_for_check(self, entity_type: str) -> List[Dict[str, str]]:
        """获取用于检查的实体列表"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return []
            
            query = f"""
            MATCH (n:{label})
            RETURN n.uuid as id, n.name as name
            ORDER BY n.created_at DESC
            LIMIT 50
            """
            
            result = await self.neo4j.run_query(query)
            
            entities = []
            for record in result:
                entities.append({
                    "id": record["id"],
                    "name": record["name"]
                })
            
            return entities
            
        except Exception as e:
            logger.error(f"获取实体列表失败: {e}")
            return []
    
    async def _get_entity_count(self, entity_type: str) -> int:
        """获取实体总数"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return 0
            
            query = f"MATCH (n:{label}) RETURN count(n) as count"
            result = await self.neo4j.run_query(query)
            
            return result[0]["count"] if result else 0
            
        except Exception as e:
            logger.error(f"获取实体数量失败: {e}")
            return 0
    
    async def _check_vectorization_status(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查向量化状态相关问题"""
        issues = []
        
        try:
            # 获取向量化覆盖率统计
            coverage_stats = await self.monitor_service.get_vectorization_coverage()
            
            for coverage in coverage_stats:
                if coverage.entity_type in [et.lower() for et in entity_types]:
                    # 检查覆盖率是否过低
                    if coverage.coverage_percentage < self.severity_thresholds["vector_coverage_threshold"] * 100:
                        severity = "critical" if coverage.coverage_percentage < 50 else "warning"
                        
                        issue = DataQualityIssue(
                            issue_type="vectorization_coverage",
                            entity_type=coverage.entity_type,
                            entity_id="overall",
                            entity_name=f"{coverage.entity_type} entities",
                            severity=severity,
                            description=f"{coverage.entity_type} 向量化覆盖率仅为 {coverage.coverage_percentage}%，低于期望的 {self.severity_thresholds['vector_coverage_threshold'] * 100}%",
                            suggested_action=f"运行批量向量化任务处理 {coverage.pending_entities} 个待处理的 {coverage.entity_type} 实体",
                            details={
                                "total_entities": coverage.total_entities,
                                "vectorized_entities": coverage.vectorized_entities,
                                "pending_entities": coverage.pending_entities,
                                "failed_entities": coverage.failed_entities,
                                "coverage_percentage": coverage.coverage_percentage
                            }
                        )
                        issues.append(issue)
                    
                    # 检查失败的实体数量
                    if coverage.failed_entities > 0:
                        failure_rate = coverage.failed_entities / coverage.total_entities
                        if failure_rate > self.severity_thresholds["vectorization_failure_rate"]:
                            issue = DataQualityIssue(
                                issue_type="vectorization_failures",
                                entity_type=coverage.entity_type,
                                entity_id="overall",
                                entity_name=f"{coverage.entity_type} entities",
                                severity="warning",
                                description=f"{coverage.entity_type} 有 {coverage.failed_entities} 个实体向量化失败 ({failure_rate:.1%})",
                                suggested_action="检查失败原因并重试失败的向量化任务",
                                details={"failed_entities": coverage.failed_entities, "failure_rate": failure_rate}
                            )
                            issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"向量化状态检查失败: {e}")
            return []
    
    async def _check_vectorization_failures(self) -> List[DataQualityIssue]:
        """检查向量化失败任务"""
        issues = []
        
        try:
            # 获取失败任务分析
            failure_analyses = await self.monitor_service.analyze_failures()
            
            for analysis in failure_analyses:
                if analysis.total_failures > 0:
                    # 检查失败率是否过高
                    if analysis.retry_success_rate < 50:  # 重试成功率低于50%
                        severity = "critical" if analysis.retry_success_rate < 20 else "warning"
                        
                        issue = DataQualityIssue(
                            issue_type="vectorization_failure_rate",
                            entity_type=analysis.entity_type,
                            entity_id="overall",
                            entity_name=f"{analysis.entity_type} entities",
                            severity=severity,
                            description=f"{analysis.entity_type} 向量化重试成功率仅为 {analysis.retry_success_rate}%，存在 {analysis.total_failures} 个持续失败的任务",
                            suggested_action="检查向量化服务配置，分析常见错误原因并修复",
                            details={
                                "total_failures": analysis.total_failures,
                                "retry_success_rate": analysis.retry_success_rate,
                                "common_errors": analysis.common_errors[:3]  # 取前3个常见错误
                            }
                        )
                        issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"向量化失败检查失败: {e}")
            return []
    
    async def _check_vector_integrity(self, entity_types: List[str]) -> List[DataQualityIssue]:
        """检查向量数据完整性"""
        issues = []
        
        try:
            for entity_type in entity_types:
                label_map = {
                    "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                    "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                    "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
                }
                
                label = label_map.get(entity_type.lower())
                if not label:
                    continue
                
                # 检查向量完整性问题
                integrity_query = f"""
                MATCH (n:{label})
                WHERE n.embedding IS NOT NULL
                WITH n,
                     CASE WHEN n.embedding_dim IS NULL THEN 'missing_dimension'
                          WHEN n.embedding_dim = 0 THEN 'zero_dimension'  
                          WHEN size(n.embedding) <> n.embedding_dim THEN 'dimension_mismatch'
                          ELSE 'ok' END as issue_type
                WHERE issue_type <> 'ok'
                RETURN n.uuid as entity_id, n.name as entity_name, issue_type,
                       n.embedding_dim as stored_dim, size(n.embedding) as actual_size
                LIMIT 100
                """
                
                result = await self.neo4j.run_query(integrity_query)
                
                for record in result:
                    issue_type_map = {
                        "missing_dimension": "向量维度信息缺失",
                        "zero_dimension": "向量维度为零",
                        "dimension_mismatch": "向量维度不匹配"
                    }
                    
                    issue_desc = issue_type_map.get(record["issue_type"], "未知向量完整性问题")
                    
                    issue = DataQualityIssue(
                        issue_type="vector_integrity",
                        entity_type=entity_type.lower(),
                        entity_id=record["entity_id"],
                        entity_name=record["entity_name"],
                        severity="warning",
                        description=f"{issue_desc}: 存储维度={record['stored_dim']}, 实际大小={record['actual_size']}",
                        suggested_action="重新生成向量数据以修复完整性问题",
                        details={
                            "stored_dimension": record["stored_dim"],
                            "actual_size": record["actual_size"],
                            "issue_type": record["issue_type"]
                        }
                    )
                    issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"向量完整性检查失败: {e}")
            return []

# 全局实例
_data_quality_service = None

def get_data_quality_service() -> DataQualityService:
    """获取数据质量服务实例（单例模式）"""
    global _data_quality_service
    if _data_quality_service is None:
        _data_quality_service = DataQualityService()
    return _data_quality_service