"""
Phase 2 Week 2: 向量相似度服务
集成火山引擎embedding API实现文本向量化和相似度计算
"""

import logging
import asyncio
import hashlib
import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import httpx

from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class EmbeddingResult:
    """向量化结果"""
    text: str
    vector: List[float]
    dimension: int
    model: str
    created_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "vector": self.vector,
            "dimension": self.dimension,
            "model": self.model,
            "created_at": self.created_at.isoformat()
        }

@dataclass
class SimilarityResult:
    """相似度计算结果"""
    text1: str
    text2: str
    similarity: float
    method: str
    threshold_passed: bool
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "text1": self.text1,
            "text2": self.text2,
            "similarity": self.similarity,
            "method": self.method,
            "threshold_passed": self.threshold_passed
        }

class EmbeddingService:
    """火山引擎向量化服务"""
    
    def __init__(self):
        """初始化向量化服务"""
        self.base_url = settings.BASE_URL or "https://ark.cn-beijing.volces.com/api/v3"
        self.api_key = settings.ARK_API_KEY
        self.model = "doubao-embedding-large-text-250515"  # 火山引擎官方embedding模型
        self.timeout = 30
        self.cache = {}  # 简单内存缓存
        self.cache_ttl = timedelta(hours=24)  # 缓存24小时
        
        # HTTP客户端
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=httpx.Timeout(self.timeout)
        )
        
        # 相似度阈值配置
        self.similarity_thresholds = {
            "brand": 0.85,      # 品牌相似度阈值
            "ingredient": 0.80,  # 成分相似度阈值
            "product": 0.75,     # 产品相似度阈值
            "general": 0.70      # 通用相似度阈值
        }
        
        logger.info(f"EmbeddingService initialized with model: {self.model}")
    
    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(f"{self.model}:{text}".encode()).hexdigest()
    
    def _is_cache_valid(self, cached_item: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        if not cached_item:
            return False
        
        created_at = datetime.fromisoformat(cached_item.get("created_at", ""))
        return datetime.now() - created_at < self.cache_ttl
    
    async def get_embedding(self, text: str, use_cache: bool = True) -> EmbeddingResult:
        """
        获取文本向量
        
        Args:
            text: 输入文本
            use_cache: 是否使用缓存
            
        Returns:
            EmbeddingResult: 向量化结果
        """
        try:
            # 文本预处理
            cleaned_text = self._preprocess_text(text)
            
            # 检查缓存
            if use_cache:
                cache_key = self._get_cache_key(cleaned_text)
                cached = self.cache.get(cache_key)
                if cached and self._is_cache_valid(cached):
                    logger.debug(f"Cache hit for text: {cleaned_text[:50]}...")
                    return EmbeddingResult(
                        text=cleaned_text,
                        vector=cached["vector"],
                        dimension=cached["dimension"],
                        model=cached["model"],
                        created_at=datetime.fromisoformat(cached["created_at"])
                    )
            
            # 调用火山引擎API
            response = await self._call_embedding_api([cleaned_text])
            
            # 解析响应
            vector_data = response["data"][0]["embedding"]
            dimension = len(vector_data)
            
            result = EmbeddingResult(
                text=cleaned_text,
                vector=vector_data,
                dimension=dimension,
                model=self.model,
                created_at=datetime.now()
            )
            
            # 更新缓存
            if use_cache:
                self.cache[cache_key] = result.to_dict()
            
            logger.debug(f"Generated embedding for text: {cleaned_text[:50]}... (dim: {dimension})")
            return result
            
        except Exception as e:
            logger.error(f"Failed to get embedding for text '{text}': {e}")
            raise
    
    async def get_batch_embeddings(self, texts: List[str], use_cache: bool = True) -> List[EmbeddingResult]:
        """
        批量获取文本向量
        
        Args:
            texts: 输入文本列表
            use_cache: 是否使用缓存
            
        Returns:
            List[EmbeddingResult]: 向量化结果列表
        """
        try:
            if not texts:
                return []
            
            # 预处理文本
            cleaned_texts = [self._preprocess_text(text) for text in texts]
            
            # 检查缓存
            cached_results = []
            uncached_texts = []
            uncached_indices = []
            
            if use_cache:
                for i, text in enumerate(cleaned_texts):
                    cache_key = self._get_cache_key(text)
                    cached = self.cache.get(cache_key)
                    if cached and self._is_cache_valid(cached):
                        cached_results.append((i, EmbeddingResult(
                            text=text,
                            vector=cached["vector"],
                            dimension=cached["dimension"],
                            model=cached["model"],
                            created_at=datetime.fromisoformat(cached["created_at"])
                        )))
                    else:
                        uncached_texts.append(text)
                        uncached_indices.append(i)
            else:
                uncached_texts = cleaned_texts
                uncached_indices = list(range(len(texts)))
            
            # 批量调用API获取未缓存的向量
            new_results = []
            if uncached_texts:
                response = await self._call_embedding_api(uncached_texts)
                
                for i, data in enumerate(response["data"]):
                    text = uncached_texts[i]
                    vector_data = data["embedding"]
                    dimension = len(vector_data)
                    
                    result = EmbeddingResult(
                        text=text,
                        vector=vector_data,
                        dimension=dimension,
                        model=self.model,
                        created_at=datetime.now()
                    )
                    
                    # 更新缓存
                    if use_cache:
                        cache_key = self._get_cache_key(text)
                        self.cache[cache_key] = result.to_dict()
                    
                    new_results.append((uncached_indices[i], result))
            
            # 合并结果并按原始顺序排序
            all_results = cached_results + new_results
            all_results.sort(key=lambda x: x[0])  # 按索引排序
            
            final_results = [result for _, result in all_results]
            
            logger.info(f"Batch embedding completed: {len(cached_results)} from cache, {len(new_results)} from API")
            return final_results
            
        except Exception as e:
            logger.error(f"Failed to get batch embeddings: {e}")
            raise
    
    async def _call_embedding_api(self, texts: List[str]) -> Dict[str, Any]:
        """调用火山引擎embedding API"""
        try:
            payload = {
                "model": self.model,
                "input": texts,
                "encoding_format": "float"
            }
            
            response = await self.client.post("/embeddings", json=payload)
            response.raise_for_status()
            
            return response.json()
            
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error calling embedding API: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Error calling embedding API: {e}")
            raise
    
    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if not text:
            return ""
        
        # 清理空格和换行
        cleaned = text.strip().replace('\n', ' ').replace('\r', '')
        
        # 限制长度避免超过token限制
        if len(cleaned) > 1000:  # 保守估计，避免超过token限制
            cleaned = cleaned[:1000] + "..."
            logger.warning(f"Text truncated to avoid token limit: {len(text)} -> 1000 chars")
        
        return cleaned
    
    async def calculate_similarity(
        self, 
        text1: str, 
        text2: str, 
        category: str = "general",
        method: str = "cosine"
    ) -> SimilarityResult:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            category: 相似度类别 (brand/ingredient/product/general)
            method: 计算方法 (cosine/euclidean/dot)
            
        Returns:
            SimilarityResult: 相似度结果
        """
        try:
            # 批量获取向量（利用批处理优势）
            embeddings = await self.get_batch_embeddings([text1, text2])
            embedding1, embedding2 = embeddings[0], embeddings[1]
            
            # 计算相似度
            if method == "cosine":
                similarity = self._cosine_similarity(embedding1.vector, embedding2.vector)
            elif method == "euclidean":
                similarity = self._euclidean_similarity(embedding1.vector, embedding2.vector)
            elif method == "dot":
                similarity = self._dot_product_similarity(embedding1.vector, embedding2.vector)
            else:
                raise ValueError(f"Unsupported similarity method: {method}")
            
            # 检查阈值
            threshold = self.similarity_thresholds.get(category, self.similarity_thresholds["general"])
            threshold_passed = similarity >= threshold
            
            result = SimilarityResult(
                text1=text1,
                text2=text2,
                similarity=similarity,
                method=method,
                threshold_passed=threshold_passed
            )
            
            logger.debug(f"Similarity calculated: {similarity:.3f} ({'PASS' if threshold_passed else 'FAIL'}) for '{text1}' vs '{text2}'")
            return result
            
        except Exception as e:
            logger.error(f"Failed to calculate similarity between '{text1}' and '{text2}': {e}")
            raise
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """余弦相似度"""
        try:
            v1 = np.array(vec1)
            v2 = np.array(vec2)
            
            # 计算余弦相似度
            dot_product = np.dot(v1, v2)
            norm_v1 = np.linalg.norm(v1)
            norm_v2 = np.linalg.norm(v2)
            
            if norm_v1 == 0 or norm_v2 == 0:
                return 0.0
            
            return float(dot_product / (norm_v1 * norm_v2))
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    def _euclidean_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """欧式距离转相似度 (1 / (1 + distance))"""
        try:
            v1 = np.array(vec1)
            v2 = np.array(vec2)
            
            distance = np.linalg.norm(v1 - v2)
            return float(1.0 / (1.0 + distance))
            
        except Exception as e:
            logger.error(f"Error calculating euclidean similarity: {e}")
            return 0.0
    
    def _dot_product_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """点积相似度 (归一化)"""
        try:
            v1 = np.array(vec1)
            v2 = np.array(vec2)
            
            # 归一化向量
            v1_norm = v1 / np.linalg.norm(v1) if np.linalg.norm(v1) > 0 else v1
            v2_norm = v2 / np.linalg.norm(v2) if np.linalg.norm(v2) > 0 else v2
            
            return float(np.dot(v1_norm, v2_norm))
            
        except Exception as e:
            logger.error(f"Error calculating dot product similarity: {e}")
            return 0.0
    
    async def batch_similarity(
        self, 
        query_text: str, 
        candidate_texts: List[str],
        category: str = "general",
        top_k: int = 5
    ) -> List[SimilarityResult]:
        """
        批量计算相似度，返回top-k结果
        
        Args:
            query_text: 查询文本
            candidate_texts: 候选文本列表
            category: 相似度类别
            top_k: 返回前k个结果
            
        Returns:
            List[SimilarityResult]: 按相似度排序的结果列表
        """
        try:
            if not candidate_texts:
                return []
            
            logger.info(f"Batch similarity calculation: 1 query vs {len(candidate_texts)} candidates")
            
            # 批量获取所有向量
            all_texts = [query_text] + candidate_texts
            embeddings = await self.get_batch_embeddings(all_texts)
            query_embedding = embeddings[0]
            candidate_embeddings = embeddings[1:]
            
            # 计算相似度
            threshold = self.similarity_thresholds.get(category, self.similarity_thresholds["general"])
            results = []
            
            for i, candidate_embedding in enumerate(candidate_embeddings):
                similarity = self._cosine_similarity(query_embedding.vector, candidate_embedding.vector)
                threshold_passed = similarity >= threshold
                
                result = SimilarityResult(
                    text1=query_text,
                    text2=candidate_texts[i],
                    similarity=similarity,
                    method="cosine",
                    threshold_passed=threshold_passed
                )
                results.append(result)
            
            # 按相似度排序
            results.sort(key=lambda x: x.similarity, reverse=True)
            
            # 返回top-k
            top_results = results[:top_k]
            
            logger.info(f"Batch similarity completed: returning top {len(top_results)} results")
            return top_results
            
        except Exception as e:
            logger.error(f"Batch similarity calculation failed: {e}")
            return []
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        valid_cache = 0
        expired_cache = 0
        
        for cached_item in self.cache.values():
            if self._is_cache_valid(cached_item):
                valid_cache += 1
            else:
                expired_cache += 1
        
        return {
            "total_cached": len(self.cache),
            "valid_cached": valid_cache,
            "expired_cached": expired_cache,
            "model": self.model,
            "cache_ttl_hours": self.cache_ttl.total_seconds() / 3600
        }
    
    async def clear_expired_cache(self):
        """清理过期缓存"""
        expired_keys = []
        for key, cached_item in self.cache.items():
            if not self._is_cache_valid(cached_item):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        logger.info(f"Cleared {len(expired_keys)} expired cache entries")
    
    async def close(self):
        """关闭服务"""
        if self.client:
            await self.client.aclose()
        logger.info("EmbeddingService closed")

# 全局向量化服务实例
_embedding_service = None

def get_embedding_service() -> EmbeddingService:
    """获取向量化服务实例（单例模式）"""
    global _embedding_service
    if _embedding_service is None:
        _embedding_service = EmbeddingService()
    return _embedding_service