"""
向量存储服务
负责实体向量的存储、检索和相似度计算
"""

import asyncio
import logging
import uuid as uuid_lib
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from app.db.neo4j import neo4j_service
from app.services.embedding_service import get_embedding_service, EmbeddingResult, SimilarityResult
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class EntityVector:
    """实体向量信息"""
    entity_id: str
    entity_type: str  # brand, ingredient, product
    entity_name: str
    embedding: List[float]
    embedding_dim: int
    embedding_created_at: datetime
    similarity_score: Optional[float] = None  # 用于相似度查询结果

@dataclass
class SimilarityMatch:
    """相似度匹配结果"""
    entity_id: str
    entity_type: str
    entity_name: str
    similarity_score: float
    threshold_passed: bool

class VectorStorageService:
    """向量存储服务"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        self.embedding_service = get_embedding_service()
        
        # 相似度阈值配置
        self.similarity_thresholds = {
            "brand": 0.85,      # 品牌相似度阈值
            "ingredient": 0.80,  # 成分相似度阈值
            "product": 0.75,     # 产品相似度阈值
        }
        
        logger.info("VectorStorageService initialized")
    
    def queue_entity_vectorization(
        self, 
        entity_id: str, 
        entity_type: str, 
        text: str,
        priority: str = "NORMAL",
        delay_seconds: int = 0,
        force_update: bool = False
    ) -> str:
        """
        将实体向量化任务加入队列（异步处理）
        
        Args:
            entity_id: 实体UUID
            entity_type: 实体类型 (brand/ingredient/product)
            text: 要向量化的文本
            priority: 任务优先级 (HIGH/NORMAL/LOW)
            delay_seconds: 延迟执行秒数
            force_update: 是否强制更新已有向量
            
        Returns:
            str: 任务ID
        """
        try:
            from app.services.vectorization_queue_service import get_queue_service, TaskPriority
            
            # 转换优先级
            priority_map = {
                "HIGH": TaskPriority.HIGH,
                "NORMAL": TaskPriority.NORMAL, 
                "LOW": TaskPriority.LOW
            }
            task_priority = priority_map.get(priority, TaskPriority.NORMAL)
            
            # 添加任务到队列
            queue_service = get_queue_service()
            task_id = queue_service.add_task(
                entity_id=entity_id,
                entity_type=entity_type,
                entity_name=text[:50],  # 截取文本作为名称
                text=text,
                priority=task_priority,
                delay_seconds=delay_seconds,
                force_update=force_update
            )
            
            logger.debug(f"Queued vectorization task {task_id} for {entity_type} {entity_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to queue vectorization task for {entity_type} {entity_id}: {e}")
            raise
    
    async def store_entity_vector(
        self, 
        entity_id: str, 
        entity_type: str, 
        text: str,
        force_update: bool = False
    ) -> bool:
        """
        存储实体向量到Neo4j
        
        Args:
            entity_id: 实体UUID
            entity_type: 实体类型 (brand/ingredient/product)
            text: 要向量化的文本
            force_update: 是否强制更新已有向量
            
        Returns:
            bool: 是否成功存储
        """
        try:
            # 检查是否已有向量
            if not force_update:
                existing_vector = await self._get_entity_vector(entity_id, entity_type)
                if existing_vector:
                    logger.debug(f"Entity {entity_id} already has vector, skipping")
                    return True
            
            # 生成向量
            embedding_result = await self.embedding_service.get_embedding(text)
            
            # 存储到Neo4j
            success = await self._save_vector_to_neo4j(
                entity_id, entity_type, embedding_result
            )
            
            if success:
                logger.info(f"Stored vector for {entity_type} '{text}' (ID: {entity_id})")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to store vector for {entity_type} {entity_id}: {e}")
            # 更新向量化状态为失败
            await self._update_vectorization_status(entity_id, entity_type, "FAILED", str(e))
            return False
    
    async def batch_store_vectors(
        self, 
        entities: List[Dict[str, Any]],
        force_update: bool = False
    ) -> Tuple[int, int]:
        """
        批量存储实体向量
        
        Args:
            entities: 实体列表，每个包含 {id, type, text}
            force_update: 是否强制更新已有向量
            
        Returns:
            Tuple[成功数量, 总数量]
        """
        try:
            total_count = len(entities)
            success_count = 0
            
            logger.info(f"Starting batch vector storage for {total_count} entities")
            
            # 批量获取向量（利用embedding服务的批处理能力）
            texts = [entity["text"] for entity in entities]
            embedding_results = await self.embedding_service.get_batch_embeddings(texts)
            
            # 逐个存储到Neo4j
            for i, entity in enumerate(entities):
                entity_name = entity.get("name", entity.get("text", "Unknown"))
                entity_id = entity["id"]
                entity_type = entity["type"]
                
                try:
                    logger.info(f"Processing entity {i+1}/{total_count}: {entity_type} '{entity_name}' (ID: {entity_id})")
                    
                    # 检查是否已有向量
                    if not force_update:
                        existing_vector = await self._get_entity_vector(
                            entity_id, entity_type
                        )
                        if existing_vector:
                            logger.info(f"Entity '{entity_name}' already has vector, skipping")
                            success_count += 1
                            continue
                    
                    # 存储向量
                    success = await self._save_vector_to_neo4j(
                        entity_id, entity_type, embedding_results[i]
                    )
                    
                    if success:
                        success_count += 1
                        logger.info(f"Successfully stored vector for {entity_type} '{entity_name}'")
                    else:
                        logger.warning(f"Failed to store vector for {entity_type} '{entity_name}'")
                    
                except Exception as e:
                    logger.error(f"Exception processing entity '{entity_name}' (ID: {entity_id}): {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")
                    continue
            
            logger.info(f"Batch vector storage completed: {success_count}/{total_count} successful")
            return success_count, total_count
            
        except Exception as e:
            logger.error(f"Batch vector storage failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return 0, len(entities) if entities else 0
    
    async def find_similar_entities(
        self, 
        text: str, 
        entity_type: str,
        limit: int = 5,
        threshold: Optional[float] = None
    ) -> List[SimilarityMatch]:
        """
        查找相似实体
        
        Args:
            text: 查询文本
            entity_type: 实体类型
            limit: 返回结果数量限制
            threshold: 相似度阈值，None则使用默认值
            
        Returns:
            List[SimilarityMatch]: 相似实体列表，按相似度降序
        """
        try:
            # 使用默认阈值
            if threshold is None:
                threshold = self.similarity_thresholds.get(entity_type, 0.75)
            
            # 获取查询文本的向量
            query_embedding = await self.embedding_service.get_embedding(text)
            
            # 获取所有同类型实体的向量
            entity_vectors = await self._get_all_entity_vectors(entity_type)
            
            if not entity_vectors:
                return []
            
            # 计算相似度
            similarity_matches = []
            
            for entity_vector in entity_vectors:
                try:
                    # 计算余弦相似度
                    similarity = self._calculate_cosine_similarity(
                        query_embedding.vector, entity_vector.embedding
                    )
                    
                    threshold_passed = similarity >= threshold
                    
                    match = SimilarityMatch(
                        entity_id=entity_vector.entity_id,
                        entity_type=entity_vector.entity_type,
                        entity_name=entity_vector.entity_name,
                        similarity_score=similarity,
                        threshold_passed=threshold_passed
                    )
                    
                    similarity_matches.append(match)
                    
                except Exception as e:
                    logger.error(f"Error calculating similarity for entity {entity_vector.entity_id}: {e}")
                    continue
            
            # 按相似度排序并限制返回数量
            similarity_matches.sort(key=lambda x: x.similarity_score, reverse=True)
            result = similarity_matches[:limit]
            
            logger.debug(f"Found {len(result)} similar entities for '{text}' (type: {entity_type})")
            return result
            
        except Exception as e:
            logger.error(f"Failed to find similar entities for '{text}': {e}")
            return []
    
    async def get_entities_without_vectors(self, entity_type: str) -> List[Dict[str, Any]]:
        """
        获取没有向量的实体列表
        
        Args:
            entity_type: 实体类型
            
        Returns:
            List[Dict]: 实体信息列表
        """
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient", 
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                raise ValueError(f"Unsupported entity type: {entity_type}")
            
            query = f"""
            MATCH (n:{label})
            WHERE n.embedding IS NULL OR n.embedding_dim IS NULL
            RETURN n.uuid as id, n.name as name, '{entity_type}' as type
            """
            
            result = await self.neo4j.run_query(query)
            
            entities = []
            for record in result:
                # 跳过没有UUID的实体
                if record["id"] is None:
                    logger.warning(f"Skipping {entity_type} '{record['name']}' - no UUID found")
                    continue
                    
                entities.append({
                    "id": record["id"],
                    "name": record["name"],
                    "type": record["type"],
                    "text": record["name"]  # 使用名称作为向量化文本
                })
            
            logger.info(f"Found {len(entities)} {entity_type} entities without vectors")
            return entities
            
        except Exception as e:
            logger.error(f"Failed to get entities without vectors: {e}")
            return []
    
    async def _get_entity_vector(self, entity_id: str, entity_type: str) -> Optional[EntityVector]:
        """获取实体向量"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return None
            
            query = f"""
            MATCH (n:{label} {{uuid: $entity_id}})
            WHERE n.embedding IS NOT NULL
            RETURN n.uuid as entity_id, n.name as entity_name, 
                   n.embedding as embedding, n.embedding_dim as embedding_dim,
                   n.embedding_created_at as embedding_created_at
            """
            
            result = await self.neo4j.run_query(query, {"entity_id": entity_id})
            
            if not result:
                return None
            
            record = result[0]
            return EntityVector(
                entity_id=record["entity_id"],
                entity_type=entity_type,
                entity_name=record["entity_name"],
                embedding=record["embedding"],
                embedding_dim=record["embedding_dim"],
                embedding_created_at=record["embedding_created_at"]
            )
            
        except Exception as e:
            logger.error(f"Failed to get entity vector: {e}")
            return None
    
    async def _get_all_entity_vectors(self, entity_type: str) -> List[EntityVector]:
        """获取所有同类型实体的向量"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return []
            
            query = f"""
            MATCH (n:{label})
            WHERE n.embedding IS NOT NULL AND n.embedding_dim IS NOT NULL
            RETURN n.uuid as entity_id, n.name as entity_name,
                   n.embedding as embedding, n.embedding_dim as embedding_dim,
                   n.embedding_created_at as embedding_created_at
            """
            
            result = await self.neo4j.run_query(query)
            
            vectors = []
            for record in result:
                vector = EntityVector(
                    entity_id=record["entity_id"],
                    entity_type=entity_type,
                    entity_name=record["entity_name"],
                    embedding=record["embedding"],
                    embedding_dim=record["embedding_dim"],
                    embedding_created_at=record["embedding_created_at"]
                )
                vectors.append(vector)
            
            return vectors
            
        except Exception as e:
            logger.error(f"Failed to get entity vectors: {e}")
            return []
    
    async def _save_vector_to_neo4j(
        self, 
        entity_id: str, 
        entity_type: str, 
        embedding_result: EmbeddingResult
    ) -> bool:
        """将向量保存到Neo4j"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                logger.error(f"Unknown entity type: {entity_type}")
                return False
            
            # 验证向量数据
            if not embedding_result.vector or embedding_result.dimension <= 0:
                logger.error(f"Invalid embedding result for entity {entity_id}")
                return False
            
            query = f"""
            MATCH (n:{label} {{uuid: $entity_id}})
            SET n.embedding = $embedding,
                n.embedding_dim = $embedding_dim,
                n.embedding_created_at = $embedding_created_at,
                n.vectorization_status = 'COMPLETED',
                n.vectorization_completed_at = $embedding_created_at,
                n.vectorization_attempts = COALESCE(n.vectorization_attempts, 0),
                n.vectorization_error = null,
                n.updated_at = $updated_at
            RETURN count(n) as updated_count
            """
            
            params = {
                "entity_id": entity_id,
                "embedding": embedding_result.vector,
                "embedding_dim": embedding_result.dimension,
                "embedding_created_at": embedding_result.created_at,
                "updated_at": datetime.utcnow()
            }
            
            logger.debug(f"Executing Neo4j query for entity {entity_id} with {len(embedding_result.vector)} dimensions")
            
            # 确保Neo4j连接是活跃的
            if not hasattr(self.neo4j, 'driver') or self.neo4j.driver is None:
                logger.info("Neo4j connection not established, attempting to connect...")
                await self.neo4j.connect()
            
            # 优化超时控制（降到10秒）
            result = await asyncio.wait_for(
                self.neo4j.run_query(query, params),
                timeout=10.0  # 10秒超时，正常Neo4j操作应该在几秒内完成
            )
            
            if result and len(result) > 0:
                updated_count = result[0]["updated_count"]
                logger.debug(f"Updated {updated_count} entities for ID {entity_id}")
                return updated_count > 0
            else:
                logger.warning(f"No entities found with ID {entity_id}")
                return False
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout saving vector for entity {entity_id}")
            return False
        except Exception as e:
            logger.error(f"Failed to save vector to Neo4j for entity {entity_id}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def _calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            import numpy as np
            
            v1 = np.array(vec1)
            v2 = np.array(vec2)
            
            # 计算余弦相似度
            dot_product = np.dot(v1, v2)
            norm_v1 = np.linalg.norm(v1)
            norm_v2 = np.linalg.norm(v2)
            
            if norm_v1 == 0 or norm_v2 == 0:
                return 0.0
            
            return float(dot_product / (norm_v1 * norm_v2))
            
        except Exception as e:
            logger.error(f"Error calculating cosine similarity: {e}")
            return 0.0
    
    async def _update_vectorization_status(
        self, 
        entity_id: str, 
        entity_type: str, 
        status: str, 
        error_message: str = None
    ) -> bool:
        """更新实体的向量化状态"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return False
            
            query = f"""
            MATCH (n:{label} {{uuid: $entity_id}})
            SET n.vectorization_status = $status,
                n.vectorization_last_attempt = datetime(),
                n.vectorization_attempts = COALESCE(n.vectorization_attempts, 0) + 1,
                n.vectorization_error = $error_message,
                n.updated_at = datetime()
            RETURN count(n) as updated
            """
            
            params = {
                "entity_id": entity_id,
                "status": status,
                "error_message": error_message
            }
            
            result = await asyncio.wait_for(
                self.neo4j.run_query(query, params),
                timeout=5.0  # 状态更新应该很快
            )
            
            return result and len(result) > 0 and result[0]["updated"] > 0
            
        except Exception as e:
            logger.error(f"Failed to update vectorization status for {entity_type} {entity_id}: {e}")
            return False
    
    async def close(self):
        """关闭服务"""
        await self.embedding_service.close()
        logger.info("VectorStorageService closed")

# 全局向量存储服务实例
_vector_storage_service = None

def get_vector_storage_service() -> VectorStorageService:
    """获取向量存储服务实例（单例模式）"""
    global _vector_storage_service
    if _vector_storage_service is None:
        _vector_storage_service = VectorStorageService()
    return _vector_storage_service