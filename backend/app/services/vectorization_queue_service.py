"""
向量化2.0 - 异步任务队列服务
负责管理向量化任务的异步调度、状态跟踪、重试机制等
"""

import asyncio
import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor
import threading
import queue

from app.core.config import settings

logger = logging.getLogger(__name__)

class VectorizationStatus(str, Enum):
    """向量化状态枚举"""
    PENDING = "PENDING"         # 等待处理
    PROCESSING = "PROCESSING"   # 处理中
    COMPLETED = "COMPLETED"     # 已完成
    FAILED = "FAILED"           # 失败
    RETRY = "RETRY"             # 重试中

class TaskPriority(int, Enum):
    """任务优先级"""
    LOW = 1      # 低优先级（批量任务）
    NORMAL = 5   # 普通优先级（用户操作）
    HIGH = 10    # 高优先级（新创建实体）

@dataclass
class VectorizationTask:
    """向量化任务数据模型"""
    task_id: str
    entity_id: str
    entity_type: str  # brand/ingredient/product
    entity_name: str
    text: str        # 用于向量化的文本
    status: VectorizationStatus
    priority: TaskPriority
    created_at: datetime
    updated_at: datetime
    scheduled_at: datetime      # 预定执行时间
    attempts: int
    max_attempts: int
    error_message: Optional[str] = None
    retry_at: Optional[datetime] = None
    force_update: bool = False  # 是否强制更新现有向量
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 处理枚举和日期时间
        data['status'] = self.status.value
        data['priority'] = self.priority.value
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        data['scheduled_at'] = self.scheduled_at.isoformat()
        if self.retry_at:
            data['retry_at'] = self.retry_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VectorizationTask':
        """从字典创建任务"""
        # 处理枚举和日期时间
        data['status'] = VectorizationStatus(data['status'])
        data['priority'] = TaskPriority(data['priority'])
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        data['scheduled_at'] = datetime.fromisoformat(data['scheduled_at'])
        if data.get('retry_at'):
            data['retry_at'] = datetime.fromisoformat(data['retry_at'])
        return cls(**data)

class VectorizationQueueService:
    """向量化队列服务"""
    
    def __init__(self):
        """初始化队列服务"""
        self.tasks: Dict[str, VectorizationTask] = {}  # 任务存储
        self.pending_queue: queue.PriorityQueue = queue.PriorityQueue()  # 待处理队列
        self.processing_tasks: Set[str] = set()  # 处理中任务集合
        self.worker_pool = ThreadPoolExecutor(max_workers=3, thread_name_prefix="VectorWorker")
        
        # 配置参数
        self.max_retry_attempts = 3
        self.retry_delay_seconds = [10, 30, 120]  # 递增延迟: 10s, 30s, 2min
        self.task_timeout_seconds = 300  # 5分钟任务超时
        self.cleanup_interval_seconds = 3600  # 1小时清理一次
        
        # 统计信息
        self.stats = {
            "total_created": 0,
            "total_completed": 0,
            "total_failed": 0,
            "processing_count": 0,
            "queue_size": 0,
            "last_cleanup": datetime.utcnow()
        }
        
        # 启动后台任务
        self.running = True
        self.worker_task = None
        self.cleanup_task = None
        
        logger.info("VectorizationQueueService initialized")
    
    async def start(self):
        """启动队列服务"""
        if self.worker_task is None:
            self.worker_task = asyncio.create_task(self._worker_loop())
            logger.info("Queue worker started")
        
        if self.cleanup_task is None:
            self.cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("Queue cleanup task started")
    
    async def stop(self):
        """停止队列服务"""
        self.running = False
        
        if self.worker_task:
            self.worker_task.cancel()
            try:
                await self.worker_task
            except asyncio.CancelledError:
                pass
        
        if self.cleanup_task:
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        self.worker_pool.shutdown(wait=True)
        logger.info("VectorizationQueueService stopped")
    
    def add_task(
        self,
        entity_id: str,
        entity_type: str,
        entity_name: str,
        text: str,
        priority: TaskPriority = TaskPriority.NORMAL,
        delay_seconds: int = 0,
        force_update: bool = False
    ) -> str:
        """
        添加向量化任务
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型
            entity_name: 实体名称
            text: 用于向量化的文本
            priority: 任务优先级
            delay_seconds: 延迟执行秒数
            force_update: 是否强制更新
            
        Returns:
            str: 任务ID
        """
        try:
            # 检查是否已有相同实体的待处理任务
            existing_task = self._find_existing_task(entity_id, entity_type)
            if existing_task and not force_update:
                # 更新现有任务的文本和优先级
                existing_task.text = text
                existing_task.entity_name = entity_name
                if priority.value > existing_task.priority.value:
                    existing_task.priority = priority
                existing_task.updated_at = datetime.utcnow()
                logger.debug(f"Updated existing task {existing_task.task_id} for entity {entity_id}")
                return existing_task.task_id
            
            # 创建新任务
            task_id = str(uuid.uuid4())
            now = datetime.utcnow()
            scheduled_at = now + timedelta(seconds=delay_seconds)
            
            task = VectorizationTask(
                task_id=task_id,
                entity_id=entity_id,
                entity_type=entity_type,
                entity_name=entity_name,
                text=text,
                status=VectorizationStatus.PENDING,
                priority=priority,
                created_at=now,
                updated_at=now,
                scheduled_at=scheduled_at,
                attempts=0,
                max_attempts=self.max_retry_attempts,
                force_update=force_update
            )
            
            # 存储任务
            self.tasks[task_id] = task
            
            # 添加到队列（优先级队列：负数表示高优先级）
            queue_item = (-priority.value, scheduled_at.timestamp(), task_id)
            self.pending_queue.put(queue_item)
            
            # 更新统计
            self.stats["total_created"] += 1
            self.stats["queue_size"] = self.pending_queue.qsize()
            
            logger.info(f"Added vectorization task {task_id} for {entity_type} '{entity_name}' (priority: {priority.name})")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to add vectorization task: {e}")
            raise
    
    def get_task(self, task_id: str) -> Optional[VectorizationTask]:
        """获取任务详情"""
        return self.tasks.get(task_id)
    
    def get_tasks(
        self,
        status: Optional[VectorizationStatus] = None,
        entity_type: Optional[str] = None,
        limit: int = 100
    ) -> List[VectorizationTask]:
        """
        获取任务列表
        
        Args:
            status: 过滤状态
            entity_type: 过滤实体类型
            limit: 限制数量
            
        Returns:
            List[VectorizationTask]: 任务列表
        """
        tasks = list(self.tasks.values())
        
        # 过滤条件
        if status:
            tasks = [t for t in tasks if t.status == status]
        if entity_type:
            tasks = [t for t in tasks if t.entity_type == entity_type]
        
        # 按更新时间倒序排序
        tasks.sort(key=lambda t: t.updated_at, reverse=True)
        
        return tasks[:limit]
    
    def retry_task(self, task_id: str) -> bool:
        """
        重试失败的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功重试
        """
        try:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status != VectorizationStatus.FAILED:
                logger.warning(f"Task {task_id} is not in FAILED status, current: {task.status}")
                return False
            
            # 重置任务状态
            task.status = VectorizationStatus.PENDING
            task.attempts = 0
            task.error_message = None
            task.retry_at = None
            task.updated_at = datetime.utcnow()
            task.scheduled_at = datetime.utcnow()
            
            # 重新入队
            queue_item = (-task.priority.value, task.scheduled_at.timestamp(), task_id)
            self.pending_queue.put(queue_item)
            
            # 更新统计
            self.stats["queue_size"] = self.pending_queue.qsize()
            
            logger.info(f"Task {task_id} retried manually")
            return True
            
        except Exception as e:
            logger.error(f"Failed to retry task {task_id}: {e}")
            return False
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status == VectorizationStatus.PROCESSING:
                logger.warning(f"Cannot cancel processing task {task_id}")
                return False
            
            # 删除任务
            del self.tasks[task_id]
            
            logger.info(f"Task {task_id} cancelled")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        # 实时统计
        status_counts = {}
        for status in VectorizationStatus:
            status_counts[status.value] = len([t for t in self.tasks.values() if t.status == status])
        
        self.stats.update({
            "processing_count": len(self.processing_tasks),
            "queue_size": self.pending_queue.qsize(),
            "total_tasks": len(self.tasks),
            "status_counts": status_counts
        })
        
        return self.stats.copy()
    
    def _find_existing_task(self, entity_id: str, entity_type: str) -> Optional[VectorizationTask]:
        """查找现有的待处理任务"""
        for task in self.tasks.values():
            if (task.entity_id == entity_id and 
                task.entity_type == entity_type and 
                task.status in [VectorizationStatus.PENDING, VectorizationStatus.PROCESSING]):
                return task
        return None
    
    async def _worker_loop(self):
        """工作线程循环"""
        logger.info("Vectorization worker loop started")
        
        while self.running:
            try:
                # 检查队列中的任务
                await self._process_pending_tasks()
                
                # 检查需要重试的任务
                await self._process_retry_tasks()
                
                # 短暂休眠
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in worker loop: {e}")
                await asyncio.sleep(5)
        
        logger.info("Vectorization worker loop stopped")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while self.running:
            try:
                await asyncio.sleep(self.cleanup_interval_seconds)
                await self._cleanup_old_tasks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    async def _process_pending_tasks(self):
        """处理待执行任务"""
        now = datetime.utcnow()
        
        # 最多同时处理3个任务
        while len(self.processing_tasks) < 3 and not self.pending_queue.empty():
            try:
                # 非阻塞获取任务
                priority, scheduled_timestamp, task_id = self.pending_queue.get_nowait()
                
                task = self.tasks.get(task_id)
                if not task or task.status != VectorizationStatus.PENDING:
                    continue
                
                # 检查是否到了执行时间
                if datetime.fromtimestamp(scheduled_timestamp) > now:
                    # 重新放回队列
                    self.pending_queue.put((priority, scheduled_timestamp, task_id))
                    break
                
                # 标记为处理中
                task.status = VectorizationStatus.PROCESSING
                task.updated_at = now
                self.processing_tasks.add(task_id)
                
                # 异步执行任务
                asyncio.create_task(self._execute_task(task))
                
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"Error processing pending task: {e}")
    
    async def _process_retry_tasks(self):
        """处理重试任务"""
        now = datetime.utcnow()
        
        for task in self.tasks.values():
            if (task.status == VectorizationStatus.FAILED and 
                task.retry_at and 
                task.retry_at <= now and
                task.attempts < task.max_attempts):
                
                # 重置为待处理状态
                task.status = VectorizationStatus.RETRY
                task.updated_at = now
                task.retry_at = None
                
                # 重新入队
                queue_item = (-task.priority.value, now.timestamp(), task.task_id)
                self.pending_queue.put(queue_item)
                
                logger.info(f"Task {task.task_id} scheduled for retry (attempt {task.attempts + 1})")
    
    async def _execute_task(self, task: VectorizationTask):
        """执行向量化任务"""
        try:
            logger.info(f"Executing vectorization task {task.task_id} for {task.entity_type} '{task.entity_name}'")
            
            # 导入向量服务（避免循环导入）
            from app.services.vector_storage_service import get_vector_storage_service
            vector_service = get_vector_storage_service()
            
            # 执行向量化
            success = await vector_service.store_entity_vector(
                entity_id=task.entity_id,
                entity_type=task.entity_type,
                text=task.text,
                force_update=task.force_update
            )
            
            if success:
                # 任务成功
                task.status = VectorizationStatus.COMPLETED
                task.error_message = None
                self.stats["total_completed"] += 1
                logger.info(f"Task {task.task_id} completed successfully")
            else:
                # 任务失败
                await self._handle_task_failure(task, "Vector storage returned False")
                
        except Exception as e:
            # 任务异常
            await self._handle_task_failure(task, str(e))
            logger.error(f"Task {task.task_id} failed with exception: {e}")
        finally:
            # 清理处理中状态
            task.updated_at = datetime.utcnow()
            self.processing_tasks.discard(task.task_id)
            self.stats["processing_count"] = len(self.processing_tasks)
    
    async def _handle_task_failure(self, task: VectorizationTask, error_message: str):
        """处理任务失败"""
        task.attempts += 1
        task.error_message = error_message
        
        if task.attempts < task.max_attempts:
            # 计算重试延迟
            delay_index = min(task.attempts - 1, len(self.retry_delay_seconds) - 1)
            delay_seconds = self.retry_delay_seconds[delay_index]
            
            task.status = VectorizationStatus.FAILED
            task.retry_at = datetime.utcnow() + timedelta(seconds=delay_seconds)
            
            logger.warning(f"Task {task.task_id} failed (attempt {task.attempts}), will retry in {delay_seconds}s")
        else:
            # 达到最大重试次数
            task.status = VectorizationStatus.FAILED
            task.retry_at = None
            self.stats["total_failed"] += 1
            
            logger.error(f"Task {task.task_id} failed permanently after {task.attempts} attempts")
    
    async def _cleanup_old_tasks(self):
        """清理旧任务"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(days=7)  # 清理7天前的任务
            
            old_task_ids = [
                task_id for task_id, task in self.tasks.items()
                if (task.status in [VectorizationStatus.COMPLETED, VectorizationStatus.FAILED] and
                    task.updated_at < cutoff_time)
            ]
            
            for task_id in old_task_ids:
                del self.tasks[task_id]
            
            if old_task_ids:
                logger.info(f"Cleaned up {len(old_task_ids)} old tasks")
            
            self.stats["last_cleanup"] = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Error in cleanup: {e}")

# 全局队列服务实例
_queue_service = None
_queue_lock = threading.Lock()

def get_queue_service() -> VectorizationQueueService:
    """获取队列服务实例（单例模式）"""
    global _queue_service
    if _queue_service is None:
        with _queue_lock:
            if _queue_service is None:
                _queue_service = VectorizationQueueService()
    return _queue_service

async def start_queue_service():
    """启动队列服务"""
    service = get_queue_service()
    await service.start()

async def stop_queue_service():
    """停止队列服务"""
    global _queue_service
    if _queue_service:
        await _queue_service.stop()
        _queue_service = None