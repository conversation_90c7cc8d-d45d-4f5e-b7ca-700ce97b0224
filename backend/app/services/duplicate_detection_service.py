"""
智能去重检测服务
使用向量相似度检测潜在的重复实体
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from app.services.vector_storage_service import get_vector_storage_service, SimilarityMatch
from app.db.neo4j import neo4j_service

logger = logging.getLogger(__name__)

@dataclass
class DuplicateCandidate:
    """重复候选实体"""
    entity_id: str
    entity_type: str
    entity_name: str
    similarity_score: float
    risk_level: str  # high, medium, low
    recommendation: str  # link, create_new, manual_review
    
@dataclass
class DuplicateDetectionResult:
    """去重检测结果"""
    query_text: str
    entity_type: str
    has_duplicates: bool
    candidates: List[DuplicateCandidate]
    recommendation: str
    confidence: float

class DuplicateDetectionService:
    """智能去重检测服务"""
    
    def __init__(self):
        self.vector_service = get_vector_storage_service()
        self.neo4j = neo4j_service
        
        # 相似度阈值配置
        self.similarity_thresholds = {
            "brand": {
                "high_risk": 0.95,     # 高风险阈值，建议链接现有
                "medium_risk": 0.85,   # 中等风险阈值，建议人工审核
                "low_risk": 0.75       # 低风险阈值，可以创建新实体
            },
            "ingredient": {
                "high_risk": 0.90,     # 成分名称通常更标准化
                "medium_risk": 0.80,
                "low_risk": 0.70
            },
            "product": {
                "high_risk": 0.85,     # 产品名称变化较大
                "medium_risk": 0.75,
                "low_risk": 0.65
            }
        }
        
        logger.info("DuplicateDetectionService initialized")
    
    async def detect_duplicates(
        self, 
        entity_name: str, 
        entity_type: str,
        limit: int = 10
    ) -> DuplicateDetectionResult:
        """
        检测潜在重复实体
        
        Args:
            entity_name: 实体名称
            entity_type: 实体类型 (brand/ingredient/product)
            limit: 返回候选数量限制
            
        Returns:
            DuplicateDetectionResult: 检测结果
        """
        try:
            logger.info(f"开始去重检测: {entity_type} '{entity_name}'")
            
            # 使用向量相似度查找相似实体
            similar_entities = await self.vector_service.find_similar_entities(
                text=entity_name,
                entity_type=entity_type,
                limit=limit,
                threshold=0.5  # 使用较低的阈值获取更多候选
            )
            
            if not similar_entities:
                return DuplicateDetectionResult(
                    query_text=entity_name,
                    entity_type=entity_type,
                    has_duplicates=False,
                    candidates=[],
                    recommendation="create_new",
                    confidence=1.0
                )
            
            # 分析候选实体并评估风险
            candidates = []
            thresholds = self.similarity_thresholds.get(entity_type, self.similarity_thresholds["product"])
            
            for match in similar_entities:
                risk_level, recommendation = self._evaluate_duplicate_risk(
                    match.similarity_score, thresholds
                )
                
                candidate = DuplicateCandidate(
                    entity_id=match.entity_id,
                    entity_type=match.entity_type,
                    entity_name=match.entity_name,
                    similarity_score=match.similarity_score,
                    risk_level=risk_level,
                    recommendation=recommendation
                )
                candidates.append(candidate)
            
            # 确定整体推荐
            overall_recommendation, confidence = self._determine_overall_recommendation(candidates)
            
            result = DuplicateDetectionResult(
                query_text=entity_name,
                entity_type=entity_type,
                has_duplicates=len(candidates) > 0,
                candidates=candidates,
                recommendation=overall_recommendation,
                confidence=confidence
            )
            
            logger.info(f"去重检测完成: 发现 {len(candidates)} 个候选, 推荐: {overall_recommendation}")
            return result
            
        except Exception as e:
            logger.error(f"去重检测失败: {e}")
            # 失败时返回安全的结果
            return DuplicateDetectionResult(
                query_text=entity_name,
                entity_type=entity_type,
                has_duplicates=False,
                candidates=[],
                recommendation="manual_review",
                confidence=0.0
            )
    
    def _evaluate_duplicate_risk(
        self, 
        similarity_score: float, 
        thresholds: Dict[str, float]
    ) -> Tuple[str, str]:
        """
        评估重复风险等级
        
        Args:
            similarity_score: 相似度分数
            thresholds: 阈值配置
            
        Returns:
            Tuple[风险等级, 推荐动作]
        """
        if similarity_score >= thresholds["high_risk"]:
            return "high", "link"
        elif similarity_score >= thresholds["medium_risk"]:
            return "medium", "manual_review"
        elif similarity_score >= thresholds["low_risk"]:
            return "low", "create_new"
        else:
            return "low", "create_new"
    
    def _determine_overall_recommendation(
        self, 
        candidates: List[DuplicateCandidate]
    ) -> Tuple[str, float]:
        """
        确定整体推荐动作
        
        Args:
            candidates: 候选实体列表
            
        Returns:
            Tuple[推荐动作, 置信度]
        """
        if not candidates:
            return "create_new", 1.0
        
        # 找到最高相似度的候选
        highest_candidate = max(candidates, key=lambda c: c.similarity_score)
        
        # 统计各风险等级数量
        risk_counts = {"high": 0, "medium": 0, "low": 0}
        for candidate in candidates:
            risk_counts[candidate.risk_level] += 1
        
        # 决策逻辑
        if risk_counts["high"] > 0:
            # 有高风险候选，建议链接
            return "link", highest_candidate.similarity_score
        elif risk_counts["medium"] > 0:
            # 有中等风险候选，建议人工审核
            return "manual_review", highest_candidate.similarity_score * 0.8
        else:
            # 都是低风险，可以创建新实体
            return "create_new", 1.0 - highest_candidate.similarity_score
    
    async def get_entity_details(self, entity_id: str, entity_type: str) -> Optional[Dict[str, Any]]:
        """
        获取实体详细信息用于比较
        
        Args:
            entity_id: 实体ID
            entity_type: 实体类型
            
        Returns:
            实体详细信息
        """
        try:
            from app.core.config import settings
            
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return None
            
            # 根据实体类型获取不同的详细信息
            if entity_type == "brand":
                query = f"""
                MATCH (b:{label} {{uuid: $entity_id}})
                OPTIONAL MATCH (b)<-[:MANUFACTURED_BY]-(p:KGMS_Product)
                RETURN b.name as name, b.created_at as created_at,
                       count(p) as product_count,
                       collect(p.name)[..5] as sample_products
                """
            elif entity_type == "ingredient":
                query = f"""
                MATCH (i:{label} {{uuid: $entity_id}})
                OPTIONAL MATCH (i)<-[:CONTAINS]-(p:KGMS_Product)
                RETURN i.name as name, i.created_at as created_at,
                       count(p) as product_count,
                       collect(p.name)[..5] as sample_products
                """
            else:  # product
                query = f"""
                MATCH (p:{label} {{uuid: $entity_id}})
                OPTIONAL MATCH (p)-[:MANUFACTURED_BY]->(b:KGMS_Brand)
                OPTIONAL MATCH (p)-[:CONTAINS]->(i:KGMS_Ingredient)
                RETURN p.name as name, p.sku as sku, p.benefits as benefits,
                       p.created_at as created_at, b.name as brand_name,
                       count(i) as ingredient_count
                """
            
            result = await self.neo4j.run_query(query, {"entity_id": entity_id})
            
            if result:
                return result[0]
            return None
            
        except Exception as e:
            logger.error(f"获取实体详情失败: {e}")
            return None
    
    async def batch_detect_duplicates(
        self, 
        entities: List[Dict[str, str]]
    ) -> List[DuplicateDetectionResult]:
        """
        批量检测重复实体
        
        Args:
            entities: 实体列表，格式: [{"name": "...", "type": "..."}]
            
        Returns:
            检测结果列表
        """
        try:
            results = []
            logger.info(f"开始批量去重检测: {len(entities)} 个实体")
            
            for entity in entities:
                result = await self.detect_duplicates(
                    entity_name=entity["name"],
                    entity_type=entity["type"]
                )
                results.append(result)
            
            # 统计结果
            stats = {"link": 0, "manual_review": 0, "create_new": 0}
            for result in results:
                stats[result.recommendation] += 1
            
            logger.info(f"批量去重检测完成: {stats}")
            return results
            
        except Exception as e:
            logger.error(f"批量去重检测失败: {e}")
            return []

# 全局实例
_duplicate_detection_service = None

def get_duplicate_detection_service() -> DuplicateDetectionService:
    """获取去重检测服务实例（单例模式）"""
    global _duplicate_detection_service
    if _duplicate_detection_service is None:
        _duplicate_detection_service = DuplicateDetectionService()
    return _duplicate_detection_service