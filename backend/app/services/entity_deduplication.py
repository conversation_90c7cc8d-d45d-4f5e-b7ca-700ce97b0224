"""
Phase 2 Week 2: 实体去重服务
基于向量相似度的智能实体去重和合并推荐系统
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import asyncio

from app.services.embedding_service import get_embedding_service, SimilarityResult
from app.db.neo4j import neo4j_service

logger = logging.getLogger(__name__)

class EntityType(Enum):
    """实体类型"""
    BRAND = "brand"
    INGREDIENT = "ingredient"
    PRODUCT = "product"

class DuplicationLevel(Enum):
    """重复程度等级"""
    HIGH = "high"      # 0.9+ 几乎确定重复
    MEDIUM = "medium"  # 0.8-0.9 可能重复
    LOW = "low"        # 0.7-0.8 轻微相似
    NONE = "none"      # <0.7 不重复

@dataclass
class EntityCandidate:
    """实体候选项"""
    uuid: str
    name: str
    entity_type: EntityType
    source: str  # "neo4j" | "new"
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "uuid": self.uuid,
            "name": self.name,
            "entity_type": self.entity_type.value,
            "source": self.source,
            "metadata": self.metadata or {}
        }

@dataclass
class DuplicationResult:
    """重复检测结果"""
    query_entity: EntityCandidate
    similar_entities: List[EntityCandidate]
    similarity_scores: List[float]
    duplication_level: DuplicationLevel
    merge_recommendation: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "query_entity": self.query_entity.to_dict(),
            "similar_entities": [entity.to_dict() for entity in self.similar_entities],
            "similarity_scores": self.similarity_scores,
            "duplication_level": self.duplication_level.value,
            "merge_recommendation": self.merge_recommendation
        }

class EntityDeduplicationService:
    """实体去重服务"""
    
    def __init__(self):
        """初始化去重服务"""
        self.embedding_service = get_embedding_service()
        
        # 重复程度阈值
        self.duplication_thresholds = {
            DuplicationLevel.HIGH: 0.90,
            DuplicationLevel.MEDIUM: 0.80, 
            DuplicationLevel.LOW: 0.70,
            DuplicationLevel.NONE: 0.0
        }
        
        # 不同实体类型的相似度权重
        self.entity_weights = {
            EntityType.BRAND: {
                "vector_similarity": 0.6,
                "name_similarity": 0.3,
                "category_match": 0.1
            },
            EntityType.INGREDIENT: {
                "vector_similarity": 0.7,
                "name_similarity": 0.2,
                "category_match": 0.1
            },
            EntityType.PRODUCT: {
                "vector_similarity": 0.5,
                "name_similarity": 0.3,
                "brand_match": 0.2
            }
        }
        
        logger.info("EntityDeduplicationService initialized")
    
    async def detect_duplicates(
        self, 
        entity_name: str, 
        entity_type: EntityType,
        candidate_limit: int = 20
    ) -> DuplicationResult:
        """
        检测实体重复
        
        Args:
            entity_name: 实体名称
            entity_type: 实体类型
            candidate_limit: 候选实体数量限制
            
        Returns:
            DuplicationResult: 重复检测结果
        """
        try:
            logger.info(f"Detecting duplicates for {entity_type.value}: {entity_name}")
            
            # 创建查询实体
            query_entity = EntityCandidate(
                uuid="new",
                name=entity_name,
                entity_type=entity_type,
                source="new"
            )
            
            # 从Neo4j获取候选实体
            candidates = await self._get_candidates_from_neo4j(entity_type, candidate_limit)
            
            if not candidates:
                logger.info(f"No existing {entity_type.value} entities found in Neo4j")
                return DuplicationResult(
                    query_entity=query_entity,
                    similar_entities=[],
                    similarity_scores=[],
                    duplication_level=DuplicationLevel.NONE,
                    merge_recommendation={"action": "create_new", "confidence": 1.0}
                )
            
            # 计算向量相似度
            candidate_names = [candidate.name for candidate in candidates]
            similarity_results = await self.embedding_service.batch_similarity(
                entity_name, 
                candidate_names,
                category=entity_type.value,
                top_k=min(10, len(candidates))  # 取前10个最相似的
            )
            
            # 过滤和排序结果
            filtered_candidates = []
            similarity_scores = []
            
            for sim_result in similarity_results:
                # 找到对应的候选实体
                matching_candidate = None
                for candidate in candidates:
                    if candidate.name == sim_result.text2:
                        matching_candidate = candidate
                        break
                
                if matching_candidate and sim_result.similarity >= 0.5:  # 最低相似度阈值
                    filtered_candidates.append(matching_candidate)
                    similarity_scores.append(sim_result.similarity)
            
            # 确定重复程度
            duplication_level = self._determine_duplication_level(similarity_scores)
            
            # 生成合并建议
            merge_recommendation = self._generate_merge_recommendation(
                query_entity, 
                filtered_candidates, 
                similarity_scores,
                duplication_level
            )
            
            result = DuplicationResult(
                query_entity=query_entity,
                similar_entities=filtered_candidates,
                similarity_scores=similarity_scores,
                duplication_level=duplication_level,
                merge_recommendation=merge_recommendation
            )
            
            logger.info(f"Duplication detection completed: found {len(filtered_candidates)} similar entities, level: {duplication_level.value}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to detect duplicates for {entity_name}: {e}")
            # 返回默认结果，建议创建新实体
            return DuplicationResult(
                query_entity=EntityCandidate("new", entity_name, entity_type, "new"),
                similar_entities=[],
                similarity_scores=[],
                duplication_level=DuplicationLevel.NONE,
                merge_recommendation={"action": "create_new", "confidence": 0.5, "error": str(e)}
            )
    
    async def _get_candidates_from_neo4j(
        self, 
        entity_type: EntityType, 
        limit: int = 20
    ) -> List[EntityCandidate]:
        """从Neo4j获取候选实体"""
        try:
            # 确保Neo4j连接
            if not neo4j_service.driver:
                await neo4j_service.connect()
            if entity_type == EntityType.BRAND:
                label = "KGMS_Brand"
            elif entity_type == EntityType.INGREDIENT:
                label = "KGMS_Ingredient"
            elif entity_type == EntityType.PRODUCT:
                label = "KGMS_Product"
            else:
                raise ValueError(f"Unsupported entity type: {entity_type}")
            
            # 查询现有实体
            query = f"""
            MATCH (n:{label})
            RETURN n.uuid as uuid, n.name as name, n.created_at as created_at
            ORDER BY n.created_at DESC
            LIMIT $limit
            """
            
            candidates = []
            
            # 使用Neo4j驱动执行查询
            if neo4j_service.driver:
                async with neo4j_service.driver.session() as session:
                    result = await session.run(query, {"limit": limit})
                    async for record in result:
                        # 处理Neo4j DateTime对象
                        created_at = record["created_at"]
                        if hasattr(created_at, 'isoformat'):
                            created_at_str = created_at.isoformat()
                        else:
                            created_at_str = str(created_at)
                        
                        candidate = EntityCandidate(
                            uuid=record["uuid"],
                            name=record["name"], 
                            entity_type=entity_type,
                            source="neo4j",
                            metadata={
                                "created_at": created_at_str
                            }
                        )
                        candidates.append(candidate)
            else:
                logger.warning("Neo4j driver not available, cannot retrieve candidates")
            
            logger.debug(f"Retrieved {len(candidates)} {entity_type.value} candidates from Neo4j")
            return candidates
            
        except Exception as e:
            logger.error(f"Failed to get candidates from Neo4j: {e}")
            return []
    
    def _determine_duplication_level(self, similarity_scores: List[float]) -> DuplicationLevel:
        """确定重复程度等级"""
        if not similarity_scores:
            return DuplicationLevel.NONE
        
        max_similarity = max(similarity_scores)
        
        if max_similarity >= self.duplication_thresholds[DuplicationLevel.HIGH]:
            return DuplicationLevel.HIGH
        elif max_similarity >= self.duplication_thresholds[DuplicationLevel.MEDIUM]:
            return DuplicationLevel.MEDIUM
        elif max_similarity >= self.duplication_thresholds[DuplicationLevel.LOW]:
            return DuplicationLevel.LOW
        else:
            return DuplicationLevel.NONE
    
    def _generate_merge_recommendation(
        self,
        query_entity: EntityCandidate,
        similar_entities: List[EntityCandidate],
        similarity_scores: List[float],
        duplication_level: DuplicationLevel
    ) -> Dict[str, Any]:
        """生成合并建议"""
        
        if duplication_level == DuplicationLevel.NONE or not similar_entities:
            return {
                "action": "create_new",
                "confidence": 0.9,
                "reason": "No similar entities found"
            }
        
        best_match_idx = 0 if similarity_scores else -1
        best_similarity = similarity_scores[0] if similarity_scores else 0
        
        if duplication_level == DuplicationLevel.HIGH:
            return {
                "action": "use_existing",
                "confidence": 0.95,
                "target_entity": similar_entities[best_match_idx].to_dict(),
                "similarity": best_similarity,
                "reason": f"High similarity ({best_similarity:.3f}) suggests same entity"
            }
        
        elif duplication_level == DuplicationLevel.MEDIUM:
            return {
                "action": "confirm_merge",
                "confidence": 0.7,
                "target_entity": similar_entities[best_match_idx].to_dict(),
                "similarity": best_similarity,
                "alternatives": [entity.to_dict() for entity in similar_entities[1:3]],
                "reason": f"Medium similarity ({best_similarity:.3f}) requires confirmation"
            }
        
        else:  # DuplicationLevel.LOW
            return {
                "action": "create_new_with_note",
                "confidence": 0.6,
                "similar_entities": [entity.to_dict() for entity in similar_entities[:3]],
                "reason": f"Low similarity ({best_similarity:.3f}) suggests different entity but worth noting"
            }
    
    async def batch_duplicate_detection(
        self,
        entities: List[Dict[str, Any]],
        entity_type: EntityType
    ) -> List[DuplicationResult]:
        """
        批量重复检测
        
        Args:
            entities: 实体列表 [{"name": "entity_name"}, ...]
            entity_type: 实体类型
            
        Returns:
            List[DuplicationResult]: 检测结果列表
        """
        try:
            logger.info(f"Batch duplicate detection for {len(entities)} {entity_type.value} entities")
            
            tasks = []
            for entity in entities:
                entity_name = entity.get("name", "")
                if entity_name:
                    task = self.detect_duplicates(entity_name, entity_type)
                    tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 过滤异常结果
            valid_results = []
            for result in results:
                if isinstance(result, DuplicationResult):
                    valid_results.append(result)
                else:
                    logger.warning(f"Batch detection failed for one entity: {result}")
            
            logger.info(f"Batch detection completed: {len(valid_results)} valid results")
            return valid_results
            
        except Exception as e:
            logger.error(f"Batch duplicate detection failed: {e}")
            return []
    
    def analyze_duplication_stats(self, results: List[DuplicationResult]) -> Dict[str, Any]:
        """分析重复统计信息"""
        if not results:
            return {"error": "No results to analyze"}
        
        total = len(results)
        level_counts = {level.value: 0 for level in DuplicationLevel}
        action_counts = {}
        
        high_confidence_duplicates = 0
        
        for result in results:
            # 重复等级统计
            level_counts[result.duplication_level.value] += 1
            
            # 动作统计
            action = result.merge_recommendation.get("action", "unknown")
            action_counts[action] = action_counts.get(action, 0) + 1
            
            # 高置信度重复统计
            if result.duplication_level in [DuplicationLevel.HIGH, DuplicationLevel.MEDIUM]:
                high_confidence_duplicates += 1
        
        duplication_rate = high_confidence_duplicates / total if total > 0 else 0
        
        return {
            "total_entities": total,
            "duplication_rate": duplication_rate,
            "duplication_levels": level_counts,
            "recommended_actions": action_counts,
            "high_confidence_duplicates": high_confidence_duplicates,
            "reduction_potential": f"{duplication_rate:.1%} entities could be merged"
        }
    
    async def close(self):
        """关闭服务"""
        await self.embedding_service.close()
        logger.info("EntityDeduplicationService closed")

# 全局去重服务实例
_deduplication_service = None

def get_deduplication_service() -> EntityDeduplicationService:
    """获取去重服务实例（单例模式）"""
    global _deduplication_service
    if _deduplication_service is None:
        _deduplication_service = EntityDeduplicationService()
    return _deduplication_service