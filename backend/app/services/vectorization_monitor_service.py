"""
向量化2.0 - 监控服务
提供向量化覆盖率统计、失败任务分析、性能指标监控等功能
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict

from app.db.neo4j import neo4j_service
from app.services.vectorization_queue_service import get_queue_service
from app.core.config import settings

logger = logging.getLogger(__name__)

@dataclass
class VectorizationCoverage:
    """向量化覆盖率统计"""
    entity_type: str
    total_entities: int
    vectorized_entities: int
    pending_entities: int
    failed_entities: int
    coverage_percentage: float
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class FailureAnalysis:
    """失败任务分析"""
    entity_type: str
    total_failures: int
    common_errors: List[Dict[str, Any]]  # [{"error": "...", "count": 5}]
    retry_success_rate: float
    avg_failure_time: float  # 平均失败时间(小时)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    avg_vectorization_time: float  # 平均向量化时间(秒)
    queue_throughput: float  # 队列吞吐量(任务/小时)
    success_rate: float  # 成功率
    active_workers: int  # 活跃工作线程数
    queue_backlog: int  # 队列积压数量

@dataclass
class VectorizationHealth:
    """向量化健康状态"""
    overall_status: str  # "healthy" | "warning" | "critical"
    issues: List[str]
    recommendations: List[str]
    last_check: datetime

class VectorizationMonitorService:
    """向量化监控服务"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        
    async def get_vectorization_coverage(self) -> List[VectorizationCoverage]:
        """获取向量化覆盖率统计"""
        try:
            coverage_stats = []
            entity_types = ["Brand", "Ingredient", "Product"]
            
            for entity_type in entity_types:
                label = f"{settings.NEO4J_LABEL_PREFIX}_{entity_type}"
                
                # 统计查询
                query = f"""
                MATCH (n:{label})
                WITH count(n) as total_count,
                     count(CASE WHEN n.vectorization_status = 'COMPLETED' THEN 1 END) as completed_count,
                     count(CASE WHEN n.vectorization_status = 'PENDING' THEN 1 END) as pending_count,
                     count(CASE WHEN n.vectorization_status = 'FAILED' THEN 1 END) as failed_count
                RETURN total_count, completed_count, pending_count, failed_count
                """
                
                result = await self.neo4j.run_query(query)
                if result:
                    record = result[0]
                    total = record["total_count"]
                    completed = record["completed_count"]
                    pending = record["pending_count"]
                    failed = record["failed_count"]
                    
                    coverage_percentage = (completed / total * 100) if total > 0 else 0
                    
                    coverage = VectorizationCoverage(
                        entity_type=entity_type.lower(),
                        total_entities=total,
                        vectorized_entities=completed,
                        pending_entities=pending,
                        failed_entities=failed,
                        coverage_percentage=round(coverage_percentage, 1)
                    )
                    coverage_stats.append(coverage)
            
            return coverage_stats
            
        except Exception as e:
            logger.error(f"获取向量化覆盖率失败: {e}")
            return []
    
    async def analyze_failures(self) -> List[FailureAnalysis]:
        """分析失败任务"""
        try:
            failure_analyses = []
            entity_types = ["Brand", "Ingredient", "Product"]
            
            for entity_type in entity_types:
                label = f"{settings.NEO4J_LABEL_PREFIX}_{entity_type}"
                
                # 获取失败统计
                failure_query = f"""
                MATCH (n:{label})
                WHERE n.vectorization_status = 'FAILED' OR n.vectorization_attempts > 0
                RETURN count(CASE WHEN n.vectorization_status = 'FAILED' THEN 1 END) as total_failures,
                       collect(DISTINCT n.vectorization_error) as errors,
                       avg(n.vectorization_attempts) as avg_attempts,
                       count(CASE WHEN n.vectorization_status = 'COMPLETED' AND n.vectorization_attempts > 0 THEN 1 END) as retry_successes
                """
                
                result = await self.neo4j.run_query(failure_query)
                if result:
                    record = result[0]
                    total_failures = record["total_failures"]
                    errors = [e for e in record["errors"] if e is not None]
                    retry_successes = record["retry_successes"]
                    
                    # 分析常见错误
                    error_counts = {}
                    for error in errors:
                        if error:
                            # 简化错误信息
                            simplified_error = self._simplify_error(error)
                            error_counts[simplified_error] = error_counts.get(simplified_error, 0) + 1
                    
                    common_errors = [
                        {"error": error, "count": count}
                        for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
                    ][:5]  # 取前5个常见错误
                    
                    # 计算重试成功率
                    total_retries = retry_successes + total_failures
                    retry_success_rate = (retry_successes / total_retries * 100) if total_retries > 0 else 0
                    
                    analysis = FailureAnalysis(
                        entity_type=entity_type.lower(),
                        total_failures=total_failures,
                        common_errors=common_errors,
                        retry_success_rate=round(retry_success_rate, 1),
                        avg_failure_time=0.0  # 暂时设为0，后续可以基于时间戳计算
                    )
                    failure_analyses.append(analysis)
            
            return failure_analyses
            
        except Exception as e:
            logger.error(f"分析失败任务失败: {e}")
            return []
    
    async def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        try:
            queue_service = get_queue_service()
            stats = queue_service.get_stats()
            
            # 计算成功率
            total_processed = stats["total_completed"] + stats["total_failed"]
            success_rate = (stats["total_completed"] / total_processed * 100) if total_processed > 0 else 0
            
            # 估算吞吐量（基于最近的统计）
            # 这里简化处理，实际应该基于时间窗口统计
            queue_throughput = stats["total_completed"]  # 简化：总完成数作为吞吐量
            
            metrics = PerformanceMetrics(
                avg_vectorization_time=3.0,  # 估算：平均3秒
                queue_throughput=queue_throughput,
                success_rate=round(success_rate, 1),
                active_workers=stats["processing_count"],
                queue_backlog=stats["queue_size"]
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return PerformanceMetrics(
                avg_vectorization_time=0.0,
                queue_throughput=0.0,
                success_rate=0.0,
                active_workers=0,
                queue_backlog=0
            )
    
    async def check_health(self) -> VectorizationHealth:
        """检查向量化系统健康状态"""
        try:
            issues = []
            recommendations = []
            
            # 获取基础统计
            queue_service = get_queue_service()
            stats = queue_service.get_stats()
            coverage_stats = await self.get_vectorization_coverage()
            
            # 检查1: 队列积压
            if stats["queue_size"] > 50:
                issues.append(f"队列积压严重: {stats['queue_size']} 个待处理任务")
                recommendations.append("考虑增加工作线程数量或优化向量化性能")
            
            # 检查2: 失败率
            total_processed = stats["total_completed"] + stats["total_failed"]
            if total_processed > 0:
                failure_rate = stats["total_failed"] / total_processed
                if failure_rate > 0.1:  # 失败率 > 10%
                    issues.append(f"失败率过高: {failure_rate:.1%}")
                    recommendations.append("检查向量化服务配置和网络连接")
            
            # 检查3: 向量化覆盖率
            for coverage in coverage_stats:
                if coverage.coverage_percentage < 80:  # 覆盖率 < 80%
                    issues.append(f"{coverage.entity_type} 向量化覆盖率偏低: {coverage.coverage_percentage}%")
                    recommendations.append(f"运行批量向量化来处理 {coverage.entity_type} 实体")
            
            # 检查4: 长时间处理中的任务
            if stats["processing_count"] > 10:
                issues.append(f"处理中任务过多: {stats['processing_count']}")
                recommendations.append("检查是否有任务卡死，考虑重启服务")
            
            # 确定整体状态
            if not issues:
                overall_status = "healthy"
            elif len(issues) <= 2:
                overall_status = "warning"
            else:
                overall_status = "critical"
            
            health = VectorizationHealth(
                overall_status=overall_status,
                issues=issues,
                recommendations=recommendations,
                last_check=datetime.utcnow()
            )
            
            return health
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return VectorizationHealth(
                overall_status="critical",
                issues=[f"健康检查失败: {str(e)}"],
                recommendations=["检查监控服务配置"],
                last_check=datetime.utcnow()
            )
    
    def _simplify_error(self, error_message: str) -> str:
        """简化错误信息，提取主要错误类型"""
        error_lower = error_message.lower()
        
        if "timeout" in error_lower:
            return "连接超时"
        elif "connection" in error_lower:
            return "连接错误"
        elif "api key" in error_lower or "unauthorized" in error_lower:
            return "API认证错误"
        elif "rate limit" in error_lower:
            return "API限流"
        elif "vector" in error_lower and ("dimension" in error_lower or "invalid" in error_lower):
            return "向量数据错误"
        elif "neo4j" in error_lower or "database" in error_lower:
            return "数据库错误"
        else:
            # 截取前50个字符作为简化错误
            return error_message[:50] + "..." if len(error_message) > 50 else error_message
    
    async def generate_report(self) -> Dict[str, Any]:
        """生成完整的向量化监控报告"""
        try:
            # 并行获取所有监控数据
            coverage_task = self.get_vectorization_coverage()
            failures_task = self.analyze_failures()
            performance_task = self.get_performance_metrics()
            health_task = self.check_health()
            
            coverage_stats, failure_analyses, performance_metrics, health_status = await asyncio.gather(
                coverage_task, failures_task, performance_task, health_task
            )
            
            # 生成报告
            report = {
                "generated_at": datetime.utcnow().isoformat(),
                "overall_health": health_status.overall_status,
                "summary": {
                    "total_entities": sum(c.total_entities for c in coverage_stats),
                    "vectorized_entities": sum(c.vectorized_entities for c in coverage_stats),
                    "overall_coverage": round(
                        sum(c.vectorized_entities for c in coverage_stats) / 
                        sum(c.total_entities for c in coverage_stats) * 100, 1
                    ) if sum(c.total_entities for c in coverage_stats) > 0 else 0,
                    "success_rate": performance_metrics.success_rate,
                    "queue_backlog": performance_metrics.queue_backlog
                },
                "coverage_by_type": [c.to_dict() for c in coverage_stats],
                "failure_analysis": [asdict(f) for f in failure_analyses],
                "performance_metrics": asdict(performance_metrics),
                "health_status": {
                    "status": health_status.overall_status,
                    "issues": health_status.issues,
                    "recommendations": health_status.recommendations,
                    "last_check": health_status.last_check.isoformat()
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
            return {
                "generated_at": datetime.utcnow().isoformat(),
                "error": str(e),
                "overall_health": "critical"
            }

# 全局监控服务实例
_monitor_service = None

def get_monitor_service() -> VectorizationMonitorService:
    """获取监控服务实例（单例模式）"""
    global _monitor_service
    if _monitor_service is None:
        _monitor_service = VectorizationMonitorService()
    return _monitor_service