"""
实体管理服务层
负责实体的CRUD操作和业务逻辑处理
"""

import logging
import uuid as uuid_lib
from datetime import datetime
from typing import List, Optional, Dict, Any, Tuple
from neo4j.time import DateTime as Neo4jDateTime
from app.db.neo4j import neo4j_service
from app.services.vector_storage_service import get_vector_storage_service
from app.models.entity_management import (
    EntityType, FilterType, SortOrder,
    BrandInfo, BrandDetail, BrandCreateRequest, BrandUpdateRequest, BrandListRequest,
    IngredientInfo, IngredientDetail, IngredientCreateRequest, IngredientUpdateRequest, IngredientListRequest,
    ProductInfo, ProductDetail, ProductCreateRequest, ProductUpdateRequest, ProductListRequest,
    BatchOperationType, BatchOperationRequest, BatchOperationResponse
)

logger = logging.getLogger(__name__)

class EntityManagementService:
    """实体管理服务类"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        self.vector_service = get_vector_storage_service()
    
    def _convert_neo4j_datetime(self, neo4j_dt) -> datetime:
        """转换Neo4j DateTime到Python datetime"""
        if neo4j_dt is None:
            return datetime.utcnow()  # 默认使用当前时间
        if isinstance(neo4j_dt, Neo4jDateTime):
            return neo4j_dt.to_native()
        if isinstance(neo4j_dt, datetime):
            return neo4j_dt
        return datetime.utcnow()  # 兜底处理
    
    # =============================================================================
    # 品牌管理方法
    # =============================================================================
    
    async def get_brands(self, request: BrandListRequest) -> Tuple[List[BrandInfo], int]:
        """获取品牌列表"""
        try:
            # 构建查询条件
            where_clauses = ["1=1"]
            params = {}
            
            # 搜索条件
            if request.search:
                where_clauses.append("toLower(b.name) CONTAINS toLower($search)")
                params["search"] = request.search
            
            # 筛选条件
            if request.filter_type == FilterType.ACTIVE:
                # 有产品关联的品牌
                where_clauses.append("EXISTS { (b)<-[:MANUFACTURED_BY]-(:KGMS_Product) }")
            elif request.filter_type == FilterType.UNUSED:
                # 无产品关联的品牌
                where_clauses.append("NOT EXISTS { (b)<-[:MANUFACTURED_BY]-(:KGMS_Product) }")
            elif request.filter_type == FilterType.DUPLICATE:
                # 这里可以添加相似度检测逻辑，暂时用名称相似
                where_clauses.append("EXISTS { (other:KGMS_Brand) WHERE b.uuid <> other.uuid AND apoc.text.distance(b.name, other.name) < 3 }")
            
            where_clause = " AND ".join(where_clauses)
            
            # 排序字段映射
            sort_field_map = {
                "name": "b.name",
                "created_at": "b.created_at",
                "updated_at": "b.updated_at",
                "product_count": "product_count"
            }
            sort_field = sort_field_map.get(request.sort_by, "b.created_at")
            sort_direction = "DESC" if request.sort_order == SortOrder.DESC else "ASC"
            
            # 分页参数
            skip = (request.page - 1) * request.size
            params["skip"] = skip
            params["limit"] = request.size
            
            # 查询品牌列表
            query = f"""
            MATCH (b:KGMS_Brand)
            WHERE {where_clause}
            OPTIONAL MATCH (b)<-[:MANUFACTURED_BY]-(p:KGMS_Product)
            WITH b, count(p) as product_count
            ORDER BY {sort_field} {sort_direction}
            SKIP $skip LIMIT $limit
            RETURN b.uuid as uuid, b.name as name, 
                   b.created_at as created_at, 
                   COALESCE(b.updated_at, b.created_at) as updated_at,
                   product_count,
                   (product_count > 0) as is_active,
                   COALESCE(b.vectorization_status, 'PENDING') as vectorization_status,
                   COALESCE(b.vectorization_attempts, 0) as vectorization_attempts,
                   b.vectorization_error as vectorization_error
            """
            
            result = await self.neo4j.run_query(query, params)
            
            # 转换为模型
            brands = []
            for record in result:
                brand = BrandInfo(
                    uuid=record["uuid"],
                    name=record["name"],
                    created_at=self._convert_neo4j_datetime(record["created_at"]),
                    updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                    product_count=record["product_count"],
                    is_active=record["is_active"],
                    vectorization_status=record.get("vectorization_status", "PENDING"),
                    vectorization_attempts=record.get("vectorization_attempts", 0),
                    vectorization_error=record.get("vectorization_error")
                )
                brands.append(brand)
            
            # 获取总数
            count_query = f"""
            MATCH (b:KGMS_Brand)
            WHERE {where_clause}
            RETURN count(b) as total
            """
            count_result = await self.neo4j.run_query(count_query, {k: v for k, v in params.items() if k not in ["skip", "limit"]})
            total = count_result[0]["total"] if count_result else 0
            
            return brands, total
            
        except Exception as e:
            logger.error(f"获取品牌列表失败: {e}")
            raise
    
    async def get_brand_detail(self, brand_id: str) -> Optional[BrandDetail]:
        """获取品牌详情"""
        try:
            query = """
            MATCH (b:KGMS_Brand {uuid: $brand_id})
            OPTIONAL MATCH (b)<-[:MANUFACTURED_BY]-(p:KGMS_Product)
            OPTIONAL MATCH (p)-[:CONTAINS]->(i:KGMS_Ingredient)
            WITH b, collect(DISTINCT p.name) as products, 
                 collect(DISTINCT i.name) as ingredients,
                 count(DISTINCT p) as product_count
            RETURN b.uuid as uuid, b.name as name,
                   b.created_at as created_at, 
                   COALESCE(b.updated_at, b.created_at) as updated_at,
                   products, ingredients, product_count,
                   (product_count > 0) as is_active
            """
            
            result = await self.neo4j.run_query(query, {"brand_id": brand_id})
            
            if not result:
                return None
            
            record = result[0]
            return BrandDetail(
                uuid=record["uuid"],
                name=record["name"],
                created_at=self._convert_neo4j_datetime(record["created_at"]),
                updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                product_count=record["product_count"],
                is_active=record["is_active"],
                products=record["products"],
                related_ingredients=record["ingredients"]
            )
            
        except Exception as e:
            logger.error(f"获取品牌详情失败: {e}")
            raise
    
    async def create_brand(self, request: BrandCreateRequest) -> BrandDetail:
        """创建品牌"""
        try:
            # 检查名称是否已存在
            existing_query = "MATCH (b:KGMS_Brand {name: $name}) RETURN b.uuid as uuid"
            existing = await self.neo4j.run_query(existing_query, {"name": request.name})
            
            if existing:
                raise ValueError(f"品牌名称 '{request.name}' 已存在")
            
            # 创建品牌
            brand_uuid = str(uuid_lib.uuid4())
            now = datetime.utcnow()
            
            create_query = """
            CREATE (b:KGMS_Brand {
                uuid: $uuid,
                name: $name,
                created_at: $created_at,
                updated_at: $updated_at,
                vectorization_status: 'PENDING',
                vectorization_attempts: 0
            })
            RETURN b.uuid as uuid, b.name as name,
                   b.created_at as created_at, 
                   COALESCE(b.updated_at, b.created_at) as updated_at,
                   COALESCE(b.vectorization_status, 'PENDING') as vectorization_status,
                   COALESCE(b.vectorization_attempts, 0) as vectorization_attempts,
                   b.vectorization_error as vectorization_error
            """
            
            result = await self.neo4j.run_query(create_query, {
                "uuid": brand_uuid,
                "name": request.name,
                "created_at": now,
                "updated_at": now
            })
            
            if not result:
                raise Exception("创建品牌失败")
            
            record = result[0]
            brand_detail = BrandDetail(
                uuid=record["uuid"],
                name=record["name"],
                created_at=self._convert_neo4j_datetime(record["created_at"]),
                updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                product_count=0,
                is_active=True,
                products=[],
                related_ingredients=[],
                vectorization_status=record.get("vectorization_status", "PENDING"),
                vectorization_attempts=record.get("vectorization_attempts", 0),
                vectorization_error=record.get("vectorization_error")
            )
            
            # 异步向量化处理，不阻塞品牌创建
            try:
                task_id = self.vector_service.queue_entity_vectorization(
                    entity_id=brand_uuid,
                    entity_type="brand",
                    text=request.name,
                    priority="HIGH"  # 新创建的实体使用高优先级
                )
                logger.info(f"品牌向量化任务已排队: {task_id}")
            except Exception as e:
                logger.warning(f"品牌向量化任务排队失败: {e}")
                # 向量化失败不影响品牌创建成功
            
            return brand_detail
            
        except Exception as e:
            logger.error(f"创建品牌失败: {e}")
            raise
    
    async def update_brand(self, brand_id: str, request: BrandUpdateRequest) -> Optional[BrandDetail]:
        """更新品牌"""
        try:
            # 首先获取现有品牌信息用于比较
            existing_brand = await self.get_brand_detail(brand_id)
            if not existing_brand:
                return None
            
            # 构建更新字段
            set_clauses = ["b.updated_at = $updated_at"]
            params = {
                "brand_id": brand_id,
                "updated_at": datetime.utcnow()
            }
            
            if request.name is not None:
                # 检查新名称是否已存在（排除当前品牌）
                existing_query = """
                MATCH (b:KGMS_Brand {name: $name}) 
                WHERE b.uuid <> $brand_id
                RETURN b.uuid as uuid
                """
                existing = await self.neo4j.run_query(existing_query, {
                    "name": request.name,
                    "brand_id": brand_id
                })
                
                if existing:
                    raise ValueError(f"品牌名称 '{request.name}' 已存在")
                
                set_clauses.append("b.name = $name")
                params["name"] = request.name
            
            set_clause = ", ".join(set_clauses)
            
            # 更新品牌
            update_query = f"""
            MATCH (b:KGMS_Brand {{uuid: $brand_id}})
            SET {set_clause}
            RETURN b.uuid as uuid
            """
            
            result = await self.neo4j.run_query(update_query, params)
            
            if not result:
                return None
            
            # 如果名称真正更新了，异步重新生成向量（防抖处理）
            if request.name is not None and request.name != existing_brand.name:
                try:
                    task_id = self.vector_service.queue_entity_vectorization(
                        entity_id=brand_id,
                        entity_type="brand",
                        text=request.name,
                        priority="NORMAL",
                        delay_seconds=2,  # 延迟2秒执行
                        force_update=True
                    )
                    logger.info(f"品牌向量化更新任务已排队: {task_id} (名称: {existing_brand.name} -> {request.name})")
                except Exception as e:
                    logger.warning(f"品牌向量化更新任务排队失败: {e}")
            
            # 返回更新后的详情
            return await self.get_brand_detail(brand_id)
            
        except Exception as e:
            logger.error(f"更新品牌失败: {e}")
            raise
    
    async def delete_brand(self, brand_id: str) -> bool:
        """删除品牌"""
        try:
            # 检查是否有关联产品
            check_query = """
            MATCH (b:KGMS_Brand {uuid: $brand_id})<-[:MANUFACTURED_BY]-(p:KGMS_Product)
            RETURN count(p) as product_count
            """
            
            check_result = await self.neo4j.run_query(check_query, {"brand_id": brand_id})
            
            if check_result and check_result[0]["product_count"] > 0:
                raise ValueError("无法删除有关联产品的品牌")
            
            # 删除品牌
            delete_query = """
            MATCH (b:KGMS_Brand {uuid: $brand_id})
            DELETE b
            RETURN count(b) as deleted_count
            """
            
            result = await self.neo4j.run_query(delete_query, {"brand_id": brand_id})
            
            return result and result[0]["deleted_count"] > 0
            
        except Exception as e:
            logger.error(f"删除品牌失败: {e}")
            raise
    
    # =============================================================================
    # 成分管理方法 (类似品牌管理的实现)
    # =============================================================================
    
    async def get_ingredients(self, request: IngredientListRequest) -> Tuple[List[IngredientInfo], int]:
        """获取成分列表"""
        try:
            # 构建查询条件
            where_clauses = ["1=1"]
            params = {}
            
            if request.search:
                where_clauses.append("toLower(i.name) CONTAINS toLower($search)")
                params["search"] = request.search
            
            if request.filter_type == FilterType.ACTIVE:
                where_clauses.append("EXISTS { (i)<-[:CONTAINS]-(:KGMS_Product) }")
            elif request.filter_type == FilterType.UNUSED:
                where_clauses.append("NOT EXISTS { (i)<-[:CONTAINS]-(:KGMS_Product) }")
            
            where_clause = " AND ".join(where_clauses)
            
            sort_field_map = {
                "name": "i.name",
                "created_at": "i.created_at",
                "updated_at": "i.updated_at",
                "product_count": "product_count"
            }
            sort_field = sort_field_map.get(request.sort_by, "i.created_at")
            sort_direction = "DESC" if request.sort_order == SortOrder.DESC else "ASC"
            
            skip = (request.page - 1) * request.size
            params.update({"skip": skip, "limit": request.size})
            
            query = f"""
            MATCH (i:KGMS_Ingredient)
            WHERE {where_clause}
            OPTIONAL MATCH (i)<-[:CONTAINS]-(p:KGMS_Product)
            WITH i, count(p) as product_count
            ORDER BY {sort_field} {sort_direction}
            SKIP $skip LIMIT $limit
            RETURN i.uuid as uuid, i.name as name,
                   i.created_at as created_at, 
                   COALESCE(i.updated_at, i.created_at) as updated_at,
                   product_count,
                   (product_count > 0) as is_active,
                   COALESCE(i.vectorization_status, 'PENDING') as vectorization_status,
                   COALESCE(i.vectorization_attempts, 0) as vectorization_attempts,
                   i.vectorization_error as vectorization_error
            """
            
            result = await self.neo4j.run_query(query, params)
            
            ingredients = []
            for record in result:
                ingredient = IngredientInfo(
                    uuid=record["uuid"],
                    name=record["name"],
                    created_at=self._convert_neo4j_datetime(record["created_at"]),
                    updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                    product_count=record["product_count"],
                    is_active=record["is_active"],
                    vectorization_status=record.get("vectorization_status", "PENDING"),
                    vectorization_attempts=record.get("vectorization_attempts", 0),
                    vectorization_error=record.get("vectorization_error")
                )
                ingredients.append(ingredient)
            
            # 获取总数
            count_query = f"""
            MATCH (i:KGMS_Ingredient)
            WHERE {where_clause}
            RETURN count(i) as total
            """
            count_result = await self.neo4j.run_query(count_query, {k: v for k, v in params.items() if k not in ["skip", "limit"]})
            total = count_result[0]["total"] if count_result else 0
            
            return ingredients, total
            
        except Exception as e:
            logger.error(f"获取成分列表失败: {e}")
            raise
    
    async def get_ingredient_detail(self, ingredient_id: str) -> Optional[IngredientDetail]:
        """获取成分详情"""
        try:
            query = """
            MATCH (i:KGMS_Ingredient {uuid: $ingredient_id})
            OPTIONAL MATCH (i)<-[c:CONTAINS]-(p:KGMS_Product)-[:MANUFACTURED_BY]->(b:KGMS_Brand)
            WITH i, collect(DISTINCT p.name) as products,
                 collect(DISTINCT b.name) as brands,
                 sum(c.amount) as total_amount,
                 collect(DISTINCT c.unit) as units,
                 count(DISTINCT p) as product_count
            RETURN i.uuid as uuid, i.name as name,
                   i.created_at as created_at, 
                   COALESCE(i.updated_at, i.created_at) as updated_at,
                   products, brands, total_amount, units, product_count,
                   (product_count > 0) as is_active
            """
            
            result = await self.neo4j.run_query(query, {"ingredient_id": ingredient_id})
            
            if not result:
                return None
            
            record = result[0]
            # 选择最常用的单位
            units = record["units"]
            common_unit = units[0] if units else None
            
            return IngredientDetail(
                uuid=record["uuid"],
                name=record["name"],
                created_at=self._convert_neo4j_datetime(record["created_at"]),
                updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                product_count=record["product_count"],
                is_active=record["is_active"],
                products=record["products"],
                related_brands=record["brands"],
                total_amount=record["total_amount"],
                common_unit=common_unit
            )
            
        except Exception as e:
            logger.error(f"获取成分详情失败: {e}")
            raise
    
    async def create_ingredient(self, request: IngredientCreateRequest) -> IngredientDetail:
        """创建成分"""
        try:
            # 检查名称是否已存在
            existing_query = "MATCH (i:KGMS_Ingredient {name: $name}) RETURN i.uuid as uuid"
            existing = await self.neo4j.run_query(existing_query, {"name": request.name})
            
            if existing:
                raise ValueError(f"成分名称 '{request.name}' 已存在")
            
            # 创建成分
            ingredient_uuid = str(uuid_lib.uuid4())
            now = datetime.utcnow()
            
            create_query = """
            CREATE (i:KGMS_Ingredient {
                uuid: $uuid,
                name: $name,
                created_at: $created_at,
                updated_at: $updated_at,
                vectorization_status: 'PENDING',
                vectorization_attempts: 0
            })
            RETURN i.uuid as uuid, i.name as name,
                   i.created_at as created_at, 
                   COALESCE(i.updated_at, i.created_at) as updated_at,
                   COALESCE(i.vectorization_status, 'PENDING') as vectorization_status,
                   COALESCE(i.vectorization_attempts, 0) as vectorization_attempts,
                   i.vectorization_error as vectorization_error
            """
            
            result = await self.neo4j.run_query(create_query, {
                "uuid": ingredient_uuid,
                "name": request.name,
                "created_at": now,
                "updated_at": now
            })
            
            if not result:
                raise Exception("创建成分失败")
            
            record = result[0]
            ingredient_detail = IngredientDetail(
                uuid=record["uuid"],
                name=record["name"],
                created_at=self._convert_neo4j_datetime(record["created_at"]),
                updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                product_count=0,
                is_active=True,
                products=[],
                related_brands=[],
                vectorization_status=record.get("vectorization_status", "PENDING"),
                vectorization_attempts=record.get("vectorization_attempts", 0),
                vectorization_error=record.get("vectorization_error")
            )
            
            # 异步向量化处理，不阻塞成分创建
            try:
                task_id = self.vector_service.queue_entity_vectorization(
                    entity_id=ingredient_uuid,
                    entity_type="ingredient",
                    text=request.name,
                    priority="HIGH"  # 新创建的实体使用高优先级
                )
                logger.info(f"成分向量化任务已排队: {task_id}")
            except Exception as e:
                logger.warning(f"成分向量化任务排队失败: {e}")
                # 向量化失败不影响成分创建成功
                # 向量化失败不影响成分创建成功
            
            return ingredient_detail
            
        except Exception as e:
            logger.error(f"创建成分失败: {e}")
            raise
    
    async def update_ingredient(self, ingredient_id: str, request: IngredientUpdateRequest) -> Optional[IngredientDetail]:
        """更新成分"""
        try:
            # 首先获取现有成分信息用于比较
            existing_ingredient = await self.get_ingredient_detail(ingredient_id)
            if not existing_ingredient:
                return None
            
            # 构建更新字段
            set_clauses = ["i.updated_at = $updated_at"]
            params = {
                "ingredient_id": ingredient_id,
                "updated_at": datetime.utcnow()
            }
            
            if request.name is not None:
                # 检查新名称是否已存在（排除当前成分）
                existing_query = """
                MATCH (i:KGMS_Ingredient {name: $name}) 
                WHERE i.uuid <> $ingredient_id
                RETURN i.uuid as uuid
                """
                existing = await self.neo4j.run_query(existing_query, {
                    "name": request.name,
                    "ingredient_id": ingredient_id
                })
                
                if existing:
                    raise ValueError(f"成分名称 '{request.name}' 已存在")
                
                set_clauses.append("i.name = $name")
                params["name"] = request.name
            
            set_clause = ", ".join(set_clauses)
            
            # 更新成分
            update_query = f"""
            MATCH (i:KGMS_Ingredient {{uuid: $ingredient_id}})
            SET {set_clause}
            RETURN i.uuid as uuid
            """
            
            result = await self.neo4j.run_query(update_query, params)
            
            if not result:
                return None
            
            # 如果名称真正更新了，异步重新生成向量（防抖处理）
            if request.name is not None and request.name != existing_ingredient.name:
                try:
                    task_id = self.vector_service.queue_entity_vectorization(
                        entity_id=ingredient_id,
                        entity_type="ingredient",
                        text=request.name,
                        priority="NORMAL",
                        delay_seconds=2,  # 延迟2秒执行
                        force_update=True
                    )
                    logger.info(f"成分向量化更新任务已排队: {task_id} (名称: {existing_ingredient.name} -> {request.name})")
                except Exception as e:
                    logger.warning(f"成分向量化更新任务排队失败: {e}")
            
            # 返回更新后的详情
            return await self.get_ingredient_detail(ingredient_id)
            
        except Exception as e:
            logger.error(f"更新成分失败: {e}")
            raise
    
    async def delete_ingredient(self, ingredient_id: str) -> bool:
        """删除成分"""
        try:
            # 检查是否有关联产品
            check_query = """
            MATCH (i:KGMS_Ingredient {uuid: $ingredient_id})<-[:CONTAINS]-(p:KGMS_Product)
            RETURN count(p) as product_count
            """
            
            check_result = await self.neo4j.run_query(check_query, {"ingredient_id": ingredient_id})
            
            if check_result and check_result[0]["product_count"] > 0:
                raise ValueError("无法删除有关联产品的成分")
            
            # 删除成分
            delete_query = """
            MATCH (i:KGMS_Ingredient {uuid: $ingredient_id})
            DELETE i
            RETURN count(i) as deleted_count
            """
            
            result = await self.neo4j.run_query(delete_query, {"ingredient_id": ingredient_id})
            
            return result and result[0]["deleted_count"] > 0
            
        except Exception as e:
            logger.error(f"删除成分失败: {e}")
            raise
    
    # =============================================================================
    # 产品管理方法
    # =============================================================================
    
    async def get_products(self, request: ProductListRequest) -> Tuple[List[ProductInfo], int]:
        """获取产品列表"""
        try:
            where_clauses = ["1=1"]
            params = {}
            
            if request.search:
                where_clauses.append("toLower(p.name) CONTAINS toLower($search)")
                params["search"] = request.search
            
            if request.brand_filter:
                where_clauses.append("toLower(b.name) CONTAINS toLower($brand_filter)")
                params["brand_filter"] = request.brand_filter
            
            where_clause = " AND ".join(where_clauses)
            
            sort_field_map = {
                "name": "p.name",
                "created_at": "p.created_at",
                "updated_at": "p.updated_at",
                "brand_name": "b.name"
            }
            sort_field = sort_field_map.get(request.sort_by, "p.created_at")
            sort_direction = "DESC" if request.sort_order == SortOrder.DESC else "ASC"
            
            skip = (request.page - 1) * request.size
            params.update({"skip": skip, "limit": request.size})
            
            query = f"""
            MATCH (p:KGMS_Product)
            OPTIONAL MATCH (p)-[:MANUFACTURED_BY]->(b:KGMS_Brand)
            OPTIONAL MATCH (p)-[:CONTAINS]->(i:KGMS_Ingredient)
            WHERE {where_clause}
            WITH p, b, count(i) as ingredient_count
            ORDER BY {sort_field} {sort_direction}
            SKIP $skip LIMIT $limit
            RETURN p.uuid as uuid, p.name as name, p.sku as sku,
                   p.product_type as product_type, p.benefits as benefits,
                   p.created_at as created_at, 
                   COALESCE(p.updated_at, p.created_at) as updated_at,
                   COALESCE(b.name, '无品牌') as brand_name, 
                   COALESCE(b.uuid, '') as brand_id,
                   ingredient_count, true as is_active,
                   COALESCE(p.vectorization_status, 'PENDING') as vectorization_status,
                   COALESCE(p.vectorization_attempts, 0) as vectorization_attempts,
                   p.vectorization_error as vectorization_error
            """
            
            result = await self.neo4j.run_query(query, params)
            
            products = []
            for record in result:
                product = ProductInfo(
                    uuid=record["uuid"],
                    name=record["name"],
                    sku=record["sku"],
                    product_type=record["product_type"],
                    brand_name=record["brand_name"],
                    brand_id=record["brand_id"],
                    ingredient_count=record["ingredient_count"],
                    benefits=record["benefits"],
                    created_at=self._convert_neo4j_datetime(record["created_at"]),
                    updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                    is_active=record["is_active"],
                    vectorization_status=record.get("vectorization_status", "PENDING"),
                    vectorization_attempts=record.get("vectorization_attempts", 0),
                    vectorization_error=record.get("vectorization_error")
                )
                products.append(product)
            
            # 获取总数
            count_query = f"""
            MATCH (p:KGMS_Product)
            OPTIONAL MATCH (p)-[:MANUFACTURED_BY]->(b:KGMS_Brand)
            WHERE {where_clause}
            RETURN count(DISTINCT p) as total
            """
            count_result = await self.neo4j.run_query(count_query, {k: v for k, v in params.items() if k not in ["skip", "limit"]})
            total = count_result[0]["total"] if count_result else 0
            
            return products, total
            
        except Exception as e:
            logger.error(f"获取产品列表失败: {e}")
            raise
    
    async def get_product_detail(self, product_id: str) -> Optional[ProductDetail]:
        """获取产品详情"""
        try:
            query = """
            MATCH (p:KGMS_Product {uuid: $product_id})
            OPTIONAL MATCH (p)-[:MANUFACTURED_BY]->(b:KGMS_Brand)
            OPTIONAL MATCH (p)-[c:CONTAINS]->(i:KGMS_Ingredient)
            WITH p, b, [x IN collect({
                ingredient_id: i.uuid,
                name: i.name,
                standard: i.name,
                amount: c.amount,
                unit: c.unit
            }) WHERE x.ingredient_id IS NOT NULL] as ingredients
            RETURN p.uuid as uuid, p.name as name, p.sku as sku,
                   p.product_type as product_type, p.benefits as benefits,
                   p.created_at as created_at, 
                   COALESCE(p.updated_at, p.created_at) as updated_at,
                   COALESCE(b.name, '无品牌') as brand_name, 
                   COALESCE(b.uuid, '') as brand_id,
                   size(ingredients) as ingredient_count,
                   ingredients, true as is_active,
                   {name: COALESCE(b.name, '无品牌'), uuid: COALESCE(b.uuid, '')} as brand_info,
                   COALESCE(p.vectorization_status, 'PENDING') as vectorization_status,
                   COALESCE(p.vectorization_attempts, 0) as vectorization_attempts,
                   p.vectorization_error as vectorization_error
            """
            
            result = await self.neo4j.run_query(query, {"product_id": product_id})
            
            if not result:
                return None
            
            record = result[0]
            return ProductDetail(
                uuid=record["uuid"],
                name=record["name"],
                sku=record["sku"],
                product_type=record["product_type"],
                brand_name=record["brand_name"],
                brand_id=record["brand_id"],
                ingredient_count=record["ingredient_count"],
                benefits=record["benefits"],
                created_at=self._convert_neo4j_datetime(record["created_at"]),
                updated_at=self._convert_neo4j_datetime(record["updated_at"]),
                is_active=record["is_active"],
                ingredients=record["ingredients"],
                brand_info=record["brand_info"],
                vectorization_status=record.get("vectorization_status", "PENDING"),
                vectorization_attempts=record.get("vectorization_attempts", 0),
                vectorization_error=record.get("vectorization_error")
            )
            
        except Exception as e:
            logger.error(f"获取产品详情失败: {e}")
            raise
    
    async def delete_product(self, product_id: str) -> bool:
        """删除产品"""
        try:
            # 删除产品及其所有关系
            delete_query = """
            MATCH (p:KGMS_Product {uuid: $product_id})
            DETACH DELETE p
            RETURN count(p) as deleted_count
            """
            
            result = await self.neo4j.run_query(delete_query, {"product_id": product_id})
            
            return result and result[0]["deleted_count"] > 0
            
        except Exception as e:
            logger.error(f"删除产品失败: {e}")
            raise
    
    async def create_product(self, request: ProductCreateRequest) -> ProductDetail:
        """创建产品"""
        try:
            product_id = str(uuid_lib.uuid4())
            now = datetime.utcnow()
            
            # 创建产品节点
            create_query = """
            CREATE (p:KGMS_Product {
                uuid: $uuid,
                name: $name,
                sku: $sku,
                product_type: $product_type,
                benefits: $benefits,
                created_at: $created_at,
                updated_at: $updated_at,
                vectorization_status: 'PENDING',
                vectorization_attempts: 0
            })
            RETURN p
            """
            
            result = await self.neo4j.run_query(create_query, {
                "uuid": product_id,
                "name": request.name,
                "sku": request.sku,
                "product_type": request.product_type,
                "benefits": request.benefits,
                "created_at": now,
                "updated_at": now
            })
            
            if not result:
                raise ValueError("创建产品失败")
            
            # 创建品牌关系（如果指定了品牌）
            if request.brand_id and request.brand_id.strip():
                try:
                    await self.neo4j.run_query("""
                    MATCH (p:KGMS_Product {uuid: $product_id})
                    MATCH (b:KGMS_Brand {uuid: $brand_id})
                    MERGE (p)-[:MANUFACTURED_BY]->(b)
                    """, {"product_id": product_id, "brand_id": request.brand_id})
                except Exception as e:
                    logger.warning(f"创建品牌关系失败: {e}")
            
            # 创建成分关系（如果指定了成分）
            if request.ingredients:
                for ingredient_data in request.ingredients:
                    ingredient_id = ingredient_data.get("ingredient_id")
                    amount = ingredient_data.get("amount") 
                    unit = ingredient_data.get("unit")
                    
                    if ingredient_id:
                        try:
                            rel_props = {}
                            if amount is not None:
                                rel_props["amount"] = amount
                            if unit:
                                rel_props["unit"] = unit
                            
                            await self.neo4j.run_query("""
                            MATCH (p:KGMS_Product {uuid: $product_id})
                            MATCH (i:KGMS_Ingredient {uuid: $ingredient_id})
                            MERGE (p)-[r:CONTAINS]->(i)
                            SET r += $props
                            """, {
                                "product_id": product_id, 
                                "ingredient_id": ingredient_id,
                                "props": rel_props
                            })
                        except Exception as e:
                            logger.warning(f"创建成分关系失败: {e}")
            
            # 异步向量化（不阻塞主流程）
            try:
                task_id = self.vector_service.queue_entity_vectorization(
                    entity_id=product_id,
                    entity_type="product", 
                    text=request.name,
                    priority="HIGH"  # 新创建的实体使用高优先级
                )
                logger.info(f"产品向量化任务已排队: {task_id}")
            except Exception as e:
                logger.warning(f"产品向量化任务排队失败: {e}")
            
            # 返回创建的产品详情
            return await self.get_product_detail(product_id)
            
        except Exception as e:
            logger.error(f"创建产品失败: {e}")
            raise
    
    async def update_product(self, product_id: str, request: ProductUpdateRequest) -> Optional[ProductDetail]:
        """更新产品"""
        try:
            # 检查产品是否存在
            existing = await self.get_product_detail(product_id)
            if not existing:
                return None
            
            # 构建更新字段
            update_fields = []
            params = {"product_id": product_id, "updated_at": datetime.utcnow()}
            
            if request.name is not None:
                update_fields.append("p.name = $name")
                params["name"] = request.name
            
            if request.sku is not None:
                update_fields.append("p.sku = $sku")
                params["sku"] = request.sku
                
            if request.product_type is not None:
                update_fields.append("p.product_type = $product_type")
                params["product_type"] = request.product_type
                
            if request.benefits is not None:
                update_fields.append("p.benefits = $benefits")
                params["benefits"] = request.benefits
            
            # 执行基本字段更新（如果有的话）
            if update_fields:
                update_query = f"""
                MATCH (p:KGMS_Product {{uuid: $product_id}})
                SET {', '.join(update_fields)}, p.updated_at = $updated_at
                RETURN p
                """
                
                result = await self.neo4j.run_query(update_query, params)
                
                if not result:
                    return None
            
            # 处理品牌关系更新
            if request.brand_id is not None and request.brand_id != "":
                # 删除旧的品牌关系
                await self.neo4j.run_query("""
                MATCH (p:KGMS_Product {uuid: $product_id})-[r:MANUFACTURED_BY]->(:KGMS_Brand)
                DELETE r
                """, {"product_id": product_id})
                
                # 创建新的品牌关系
                await self.neo4j.run_query("""
                MATCH (p:KGMS_Product {uuid: $product_id})
                MATCH (b:KGMS_Brand {uuid: $brand_id})
                MERGE (p)-[:MANUFACTURED_BY]->(b)
                """, {"product_id": product_id, "brand_id": request.brand_id})
            
            # 处理成分关系更新
            if request.ingredients is not None:
                # 删除所有旧的成分关系
                await self.neo4j.run_query("""
                MATCH (p:KGMS_Product {uuid: $product_id})-[r:CONTAINS]->(:KGMS_Ingredient)
                DELETE r
                """, {"product_id": product_id})
                
                # 创建新的成分关系
                for ingredient_data in request.ingredients:
                    ingredient_id = ingredient_data.get("ingredient_id")
                    amount = ingredient_data.get("amount")
                    unit = ingredient_data.get("unit")
                    
                    if ingredient_id:
                        rel_props = {}
                        if amount is not None:
                            rel_props["amount"] = amount
                        if unit:
                            rel_props["unit"] = unit
                        
                        await self.neo4j.run_query("""
                        MATCH (p:KGMS_Product {uuid: $product_id})
                        MATCH (i:KGMS_Ingredient {uuid: $ingredient_id})
                        MERGE (p)-[r:CONTAINS]->(i)
                        SET r += $props
                        """, {
                            "product_id": product_id, 
                            "ingredient_id": ingredient_id, 
                            "props": rel_props
                        })
            
            # 如果名称更新了，异步重新向量化（防抖处理）
            if request.name is not None and request.name != existing.name:
                try:
                    task_id = self.vector_service.queue_entity_vectorization(
                        entity_id=product_id,
                        entity_type="product",
                        text=request.name,
                        priority="NORMAL",
                        delay_seconds=5,  # 延迟5秒执行，防止频繁更新
                        force_update=True
                    )
                    logger.info(f"产品向量化更新任务已排队: {task_id}")
                except Exception as e:
                    logger.warning(f"产品向量化更新任务排队失败: {e}")
            
            # 返回更新后的产品详情
            return await self.get_product_detail(product_id)
            
        except Exception as e:
            logger.error(f"更新产品失败: {e}")
            raise
    
    # =============================================================================
    # 统计方法
    # =============================================================================
    
    async def get_entity_stats(self) -> Dict[str, Any]:
        """获取实体统计信息"""
        try:
            query = """
            MATCH (b:KGMS_Brand)
            WITH count(b) as brand_count
            MATCH (i:KGMS_Ingredient)
            WITH brand_count, count(i) as ingredient_count
            MATCH (p:KGMS_Product)
            WITH brand_count, ingredient_count, count(p) as product_count
            MATCH (p2:KGMS_Product)-[:MANUFACTURED_BY]->(b2:KGMS_Brand)
            WITH brand_count, ingredient_count, product_count, count(DISTINCT b2) as active_brand_count
            MATCH (p3:KGMS_Product)-[:CONTAINS]->(i2:KGMS_Ingredient)
            RETURN brand_count, ingredient_count, product_count, 
                   active_brand_count, count(DISTINCT i2) as active_ingredient_count
            """
            
            result = await self.neo4j.run_query(query)
            
            if not result:
                return {
                    "total_brands": 0,
                    "total_ingredients": 0,
                    "total_products": 0,
                    "active_brands": 0,
                    "active_ingredients": 0,
                    "unused_brands": 0,
                    "unused_ingredients": 0
                }
            
            record = result[0]
            total_brands = record["brand_count"]
            total_ingredients = record["ingredient_count"]
            active_brands = record["active_brand_count"]
            active_ingredients = record["active_ingredient_count"]
            
            return {
                "total_brands": total_brands,
                "total_ingredients": total_ingredients,
                "total_products": record["product_count"],
                "active_brands": active_brands,
                "active_ingredients": active_ingredients,
                "unused_brands": total_brands - active_brands,
                "unused_ingredients": total_ingredients - active_ingredients
            }
            
        except Exception as e:
            logger.error(f"获取实体统计失败: {e}")
            raise
    
    # =============================================================================
    # 批量操作方法
    # =============================================================================
    
    async def batch_operation(self, request: BatchOperationRequest) -> BatchOperationResponse:
        """执行批量操作"""
        try:
            logger.info(f"执行批量操作: {request.operation_type} {request.entity_type} (数量: {len(request.entity_ids)})")
            
            processed_count = len(request.entity_ids)
            success_count = 0
            failed_count = 0
            details = []
            task_ids = None
            
            # 根据操作类型执行不同操作
            if request.operation_type == BatchOperationType.DELETE:
                success_count, failed_count, details = await self._batch_delete(
                    request.entity_type, request.entity_ids
                )
            elif request.operation_type == BatchOperationType.MERGE:
                success_count, failed_count, details = await self._batch_merge(
                    request.entity_type, request.entity_ids, request.operation_data or {}
                )
            elif request.operation_type == BatchOperationType.START_VECTORIZATION:
                success_count, failed_count, details, task_ids = await self._batch_start_vectorization(
                    request.entity_type, request.entity_ids
                )
            else:
                raise ValueError(f"不支持的批量操作类型: {request.operation_type}")
            
            return BatchOperationResponse(
                success=failed_count == 0,
                processed_count=processed_count,
                success_count=success_count,
                failed_count=failed_count,
                details=details,
                task_ids=task_ids
            )
            
        except Exception as e:
            logger.error(f"批量操作失败: {e}")
            return BatchOperationResponse(
                success=False,
                processed_count=len(request.entity_ids),
                success_count=0,
                failed_count=len(request.entity_ids),
                details=[],
                error=str(e)
            )
    
    async def _batch_delete(self, entity_type: EntityType, entity_ids: List[str]) -> Tuple[int, int, List[Dict[str, Any]]]:
        """批量删除实体"""
        success_count = 0
        failed_count = 0
        details = []
        
        label = f"KGMS_{entity_type.value}"
        
        for entity_id in entity_ids:
            try:
                # 检查实体是否存在
                check_query = f"MATCH (n:{label} {{uuid: $uuid}}) RETURN n.name as name"
                check_result = await self.neo4j.run_query(check_query, {"uuid": entity_id})
                
                if not check_result:
                    details.append({
                        "entity_id": entity_id,
                        "status": "skipped",
                        "message": "实体不存在"
                    })
                    continue
                
                entity_name = check_result[0]["name"]
                
                # 检查实体是否被使用（仅针对品牌和成分）
                if entity_type in [EntityType.BRAND, EntityType.INGREDIENT]:
                    if entity_type == EntityType.BRAND:
                        usage_query = f"MATCH (:{label} {{uuid: $uuid}})<-[:MANUFACTURED_BY]-() RETURN count(*) as usage_count"
                    else:  # INGREDIENT
                        usage_query = f"MATCH (:{label} {{uuid: $uuid}})<-[:CONTAINS]-() RETURN count(*) as usage_count"
                    
                    usage_result = await self.neo4j.run_query(usage_query, {"uuid": entity_id})
                    usage_count = usage_result[0]["usage_count"] if usage_result else 0
                    
                    if usage_count > 0:
                        details.append({
                            "entity_id": entity_id,
                            "entity_name": entity_name,
                            "status": "skipped",
                            "message": f"实体被 {usage_count} 个产品使用，不能删除"
                        })
                        continue
                
                # 执行删除
                delete_query = f"MATCH (n:{label} {{uuid: $uuid}}) DETACH DELETE n RETURN count(n) as deleted_count"
                delete_result = await self.neo4j.run_query(delete_query, {"uuid": entity_id})
                
                if delete_result and delete_result[0]["deleted_count"] > 0:
                    success_count += 1
                    details.append({
                        "entity_id": entity_id,
                        "entity_name": entity_name,
                        "status": "success",
                        "message": "删除成功"
                    })
                else:
                    failed_count += 1
                    details.append({
                        "entity_id": entity_id,
                        "entity_name": entity_name,
                        "status": "failed",
                        "message": "删除失败"
                    })
                    
            except Exception as e:
                failed_count += 1
                details.append({
                    "entity_id": entity_id,
                    "status": "error",
                    "message": f"删除异常: {str(e)}"
                })
        
        return success_count, failed_count, details
    
    async def _batch_merge(self, entity_type: EntityType, entity_ids: List[str], operation_data: Dict[str, Any]) -> Tuple[int, int, List[Dict[str, Any]]]:
        """批量合并实体"""
        success_count = 0
        failed_count = 0
        details = []
        
        # 获取目标实体ID
        target_entity_id = operation_data.get("target_entity_id")
        if not target_entity_id:
            return 0, len(entity_ids), [{"message": "缺少target_entity_id参数"}]
        
        # 验证目标实体存在
        label = f"KGMS_{entity_type.value}"
        target_query = f"MATCH (target:{label} {{uuid: $uuid}}) RETURN target.name as name"
        target_result = await self.neo4j.run_query(target_query, {"uuid": target_entity_id})
        
        if not target_result:
            return 0, len(entity_ids), [{"message": "目标实体不存在"}]
        
        target_name = target_result[0]["name"]
        
        for entity_id in entity_ids:
            if entity_id == target_entity_id:
                continue  # 跳过目标实体自身
            
            try:
                # 检查源实体存在
                source_query = f"MATCH (source:{label} {{uuid: $uuid}}) RETURN source.name as name"
                source_result = await self.neo4j.run_query(source_query, {"uuid": entity_id})
                
                if not source_result:
                    details.append({
                        "entity_id": entity_id,
                        "status": "skipped",
                        "message": "源实体不存在"
                    })
                    continue
                
                source_name = source_result[0]["name"]
                
                # 执行合并操作（将源实体的所有关系转移到目标实体）
                if entity_type == EntityType.BRAND:
                    # 合并品牌：将产品关系转移
                    merge_query = """
                    MATCH (source:KGMS_Brand {uuid: $source_id})<-[old_rel:MANUFACTURED_BY]-(p:KGMS_Product)
                    MATCH (target:KGMS_Brand {uuid: $target_id})
                    MERGE (p)-[:MANUFACTURED_BY]->(target)
                    DELETE old_rel
                    WITH source, target, count(p) as moved_count
                    DETACH DELETE source
                    RETURN moved_count
                    """
                elif entity_type == EntityType.INGREDIENT:
                    # 合并成分：将产品关系转移（保留属性）
                    merge_query = """
                    MATCH (source:KGMS_Ingredient {uuid: $source_id})<-[old_rel:CONTAINS]-(p:KGMS_Product)
                    MATCH (target:KGMS_Ingredient {uuid: $target_id})
                    MERGE (p)-[new_rel:CONTAINS]->(target)
                    SET new_rel.amount = old_rel.amount, new_rel.unit = old_rel.unit
                    DELETE old_rel
                    WITH source, target, count(p) as moved_count
                    DETACH DELETE source
                    RETURN moved_count
                    """
                else:
                    # 产品不支持合并
                    details.append({
                        "entity_id": entity_id,
                        "entity_name": source_name,
                        "status": "skipped",
                        "message": "产品不支持合并操作"
                    })
                    continue
                
                merge_result = await self.neo4j.run_query(merge_query, {
                    "source_id": entity_id,
                    "target_id": target_entity_id
                })
                
                moved_count = merge_result[0]["moved_count"] if merge_result else 0
                success_count += 1
                details.append({
                    "entity_id": entity_id,
                    "entity_name": source_name,
                    "target_name": target_name,
                    "status": "success",
                    "message": f"合并成功，转移了 {moved_count} 个关系"
                })
                
            except Exception as e:
                failed_count += 1
                details.append({
                    "entity_id": entity_id,
                    "status": "error",
                    "message": f"合并异常: {str(e)}"
                })
        
        return success_count, failed_count, details
    
    async def _batch_start_vectorization(self, entity_type: EntityType, entity_ids: List[str]) -> Tuple[int, int, List[Dict[str, Any]], List[str]]:
        """批量开始向量化 - 只处理PENDING状态的实体"""
        success_count = 0
        failed_count = 0
        details = []
        task_ids = []
        
        label = f"KGMS_{entity_type.value}"
        
        for entity_id in entity_ids:
            try:
                # 检查实体存在和状态
                check_query = f"""
                MATCH (n:{label} {{uuid: $uuid}}) 
                RETURN n.name as name, 
                       COALESCE(n.vectorization_status, 'PENDING') as status
                """
                check_result = await self.neo4j.run_query(check_query, {"uuid": entity_id})
                
                if not check_result:
                    failed_count += 1
                    details.append({
                        "entity_id": entity_id,
                        "status": "error",
                        "message": "实体不存在"
                    })
                    continue
                
                entity_name = check_result[0]["name"]
                current_status = check_result[0]["status"]
                
                # 只处理PENDING状态的实体
                if current_status != "PENDING":
                    details.append({
                        "entity_id": entity_id,
                        "entity_name": entity_name,
                        "status": "skipped",
                        "message": f"实体状态为 {current_status}，跳过处理"
                    })
                    continue
                
                # 调用向量化队列服务
                try:
                    task_id = self.vector_service.queue_entity_vectorization(
                        entity_id=entity_id,
                        entity_type=entity_type.value.lower(),
                        text=entity_name,  # 使用实体名称作为向量化文本
                        priority="NORMAL",  # 批量处理使用普通优先级
                        delay_seconds=0,
                        force_update=False
                    )
                    
                    success_count += 1
                    task_ids.append(task_id)
                    details.append({
                        "entity_id": entity_id,
                        "entity_name": entity_name,
                        "status": "success",
                        "message": f"向量化任务已加入队列，任务ID: {task_id}"
                    })
                    
                    logger.info(f"实体 {entity_name} ({entity_type.value}) 向量化任务已排队: {task_id}")
                    
                except Exception as e:
                    failed_count += 1
                    details.append({
                        "entity_id": entity_id,
                        "entity_name": entity_name,
                        "status": "error",
                        "message": f"向量化任务排队失败: {str(e)}"
                    })
                    logger.error(f"实体 {entity_name} 向量化任务排队失败: {e}")
                    
            except Exception as e:
                failed_count += 1
                details.append({
                    "entity_id": entity_id,
                    "status": "error",
                    "message": f"处理异常: {str(e)}"
                })
                logger.error(f"批量向量化处理实体 {entity_id} 时异常: {e}")
        
        return success_count, failed_count, details, task_ids

# 全局实例
entity_management_service = EntityManagementService()