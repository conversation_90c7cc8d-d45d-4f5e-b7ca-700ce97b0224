from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime

# 用户登录请求
class UserLogin(BaseModel):
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=1, description="密码")

# 访问令牌响应
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: "UserInfo"

# 用户信息
class UserInfo(BaseModel):
    id: int
    username: str
    email: Optional[str] = None
    full_name: Optional[str] = None
    is_active: bool
    is_admin: bool
    last_login_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

# 用户创建请求
class UserCreate(BaseModel):
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    email: Optional[str] = Field(None, max_length=100, description="邮箱")
    password: str = Field(..., min_length=6, description="密码")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_admin: bool = Field(False, description="是否为管理员")

# 用户更新请求
class UserUpdate(BaseModel):
    email: Optional[str] = Field(None, max_length=100, description="邮箱")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: Optional[bool] = Field(None, description="是否启用")
    is_admin: Optional[bool] = Field(None, description="是否为管理员")
    password: Optional[str] = Field(None, min_length=6, description="新密码")

# 用户状态更新
class UserStatusUpdate(BaseModel):
    is_active: bool = Field(..., description="是否启用")

# 用户列表响应
class UserListResponse(BaseModel):
    users: list[UserInfo]
    total: int
    page: int
    size: int

# 密码重置（预留）
class PasswordReset(BaseModel):
    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=6, description="新密码")