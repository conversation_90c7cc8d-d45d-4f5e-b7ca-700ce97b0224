from neo4j import AsyncGraphDatabase
from typing import List, Dict, Optional
import uuid
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)


class Neo4jService:
    """Neo4j数据库服务类"""
    
    def __init__(self):
        self.driver = None
        self._vector_service = None
    
    def _get_vector_service(self):
        """懒加载向量服务，避免循环导入"""
        if self._vector_service is None:
            from app.services.vector_storage_service import get_vector_storage_service
            self._vector_service = get_vector_storage_service()
        return self._vector_service
    
    async def connect(self):
        """建立数据库连接"""
        try:
            # 优化连接配置，添加合理的超时设置
            self.driver = AsyncGraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USERNAME, settings.NEO4J_PASSWORD),
                max_connection_lifetime=3600,  # 1小时连接生命周期
                connection_timeout=15,         # 15秒连接超时
                max_transaction_retry_time=10, # 10秒重试时间
                max_connection_pool_size=50,   # 连接池大小
                connection_acquisition_timeout=60  # 获取连接超时
            )
            # 测试连接
            await self.driver.verify_connectivity()
            logger.info(f"Neo4j connection established successfully with optimized config (using label prefix: {settings.NEO4J_LABEL_PREFIX})")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise
    
    async def close(self):
        """关闭数据库连接"""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j connection closed")
    
    async def save_product(self, product_data: dict) -> str:
        """保存产品到知识图谱"""
        product_id = str(uuid.uuid4())
        
        try:
            async with self.driver.session() as session:
                # 使用事务确保原子性
                tx = await session.begin_transaction()
                try:
                    logger.info(f"开始保存产品: {product_data.get('product_name')}")
                    
                    # 创建或匹配品牌 - 修复UUID处理
                    brand_uuid = str(uuid.uuid4())
                    brand_query = f"""
                        MERGE (b:{settings.NEO4J_LABEL_PREFIX}_Brand {{name: $brand_name}})
                        ON CREATE SET b.uuid = $brand_uuid, 
                                      b.created_at = datetime()
                        ON MATCH SET b.uuid = COALESCE(b.uuid, $brand_uuid),
                                     b.updated_at = datetime()
                        RETURN b.uuid as brand_uuid
                    """
                    brand_result = await tx.run(
                        brand_query,
                        brand_name=product_data['brand'],
                        brand_uuid=brand_uuid
                    )
                    brand_record = await brand_result.single()
                    if not brand_record:
                        raise Exception(f"Failed to create/find brand: {product_data['brand']}")
                    actual_brand_uuid = brand_record['brand_uuid']
                    logger.info(f"品牌处理完成: {product_data['brand']}, UUID: {actual_brand_uuid}")
                    
                    # 异步向量化品牌（如果是新创建的品牌）
                    try:
                        vector_service = self._get_vector_service()
                        task_id = vector_service.queue_entity_vectorization(
                            entity_id=actual_brand_uuid,
                            entity_type="brand",
                            text=product_data['brand'],
                            priority="HIGH"
                        )
                        logger.info(f"品牌向量化任务已排队: {task_id}")
                    except Exception as e:
                        logger.warning(f"品牌向量化任务排队失败: {e}")
                        # 向量化失败不影响产品保存
                    
                    # 创建或匹配产品节点 - 避免重复创建相同名称的产品
                    product_query = f"""
                        MERGE (p:{settings.NEO4J_LABEL_PREFIX}_Product {{name: $name}})
                        ON CREATE SET p.uuid = $uuid,
                                      p.sku = $sku,
                                      p.product_type = $product_type,
                                      p.benefits = $benefits,
                                      p.created_at = datetime(),
                                      p.updated_at = datetime()
                        ON MATCH SET p.uuid = COALESCE(p.uuid, $uuid),
                                     p.sku = $sku,
                                     p.product_type = $product_type, 
                                     p.benefits = $benefits,
                                     p.updated_at = datetime()
                        RETURN p.uuid as product_uuid
                    """
                    product_result = await tx.run(
                        product_query,
                        uuid=product_id,
                        name=product_data['product_name'],
                        sku=product_data.get('sku'),
                        product_type=product_data.get('product_type', '保健品'),
                        benefits=product_data.get('benefits')
                    )
                    product_record = await product_result.single()
                    if not product_record:
                        raise Exception(f"Failed to create/find product: {product_data['product_name']}")
                    
                    # 使用实际返回的UUID（可能是现有的或新创建的）
                    product_id = product_record['product_uuid']
                    logger.info(f"产品节点处理完成: {product_data['product_name']}, UUID: {product_id}")
                    
                    # 异步向量化产品
                    try:
                        vector_service = self._get_vector_service()
                        # 使用产品名称+品牌+功效的组合文本进行向量化
                        product_text = f"{product_data['product_name']} {product_data['brand']}"
                        if product_data.get('benefits'):
                            product_text += f" {product_data['benefits']}"
                        
                        task_id = vector_service.queue_entity_vectorization(
                            entity_id=product_id,
                            entity_type="product",
                            text=product_text,
                            priority="HIGH"
                        )
                        logger.info(f"产品向量化任务已排队: {task_id}")
                    except Exception as e:
                        logger.warning(f"产品向量化任务排队失败: {e}")
                        # 向量化失败不影响产品保存
                    
                    # 建立品牌关系 - 使用MERGE避免重复关系
                    brand_rel_query = f"""
                        MATCH (p:{settings.NEO4J_LABEL_PREFIX}_Product {{uuid: $product_uuid}})
                        MATCH (b:{settings.NEO4J_LABEL_PREFIX}_Brand {{uuid: $brand_uuid}})
                        MERGE (p)-[:MANUFACTURED_BY]->(b)
                        RETURN p, b
                    """
                    brand_rel_result = await tx.run(
                        brand_rel_query,
                        product_uuid=product_id,
                        brand_uuid=actual_brand_uuid
                    )
                    brand_rel_record = await brand_rel_result.single()
                    if not brand_rel_record:
                        logger.error(f"品牌关系创建失败: Product={product_id}, Brand={actual_brand_uuid}")
                        raise Exception("Failed to create brand relationship")
                    logger.info(f"品牌关系创建成功: {product_data['product_name']} -> {product_data['brand']}")
                    
                    # 处理成分
                    ingredient_count = 0
                    for ingredient in product_data.get('ingredients', []):
                        if not ingredient.get('name'):
                            continue
                            
                        # 创建或匹配成分 - 修复UUID处理
                        ingredient_uuid = str(uuid.uuid4())
                        ingredient_query = f"""
                            MERGE (i:{settings.NEO4J_LABEL_PREFIX}_Ingredient {{name: $name}})
                            ON CREATE SET i.uuid = $uuid,
                                          i.created_at = datetime()
                            ON MATCH SET i.uuid = COALESCE(i.uuid, $uuid),
                                         i.updated_at = datetime()
                            RETURN i.uuid as ingredient_uuid
                        """
                        ingredient_result = await tx.run(
                            ingredient_query,
                            name=ingredient['name'],
                            uuid=ingredient_uuid
                        )
                        ingredient_record = await ingredient_result.single()
                        if not ingredient_record:
                            logger.error(f"成分创建失败: {ingredient['name']}")
                            continue
                        actual_ingredient_uuid = ingredient_record['ingredient_uuid']
                        logger.info(f"成分处理完成: {ingredient['name']}, UUID: {actual_ingredient_uuid}")
                        
                        # 异步向量化成分
                        try:
                            vector_service = self._get_vector_service()
                            task_id = vector_service.queue_entity_vectorization(
                                entity_id=actual_ingredient_uuid,
                                entity_type="ingredient",
                                text=ingredient['name'],
                                priority="HIGH"
                            )
                            logger.info(f"成分向量化任务已排队: {task_id}")
                        except Exception as e:
                            logger.warning(f"成分向量化任务排队失败: {e}")
                            # 向量化失败不影响成分保存
                        
                        # 建立包含关系 - 使用MERGE避免重复关系，更新含量信息
                        contains_query = f"""
                            MATCH (p:{settings.NEO4J_LABEL_PREFIX}_Product {{uuid: $product_uuid}})
                            MATCH (i:{settings.NEO4J_LABEL_PREFIX}_Ingredient {{uuid: $ingredient_uuid}})
                            MERGE (p)-[r:CONTAINS]->(i)
                            SET r.amount = $amount,
                                r.unit = $unit
                            RETURN p, i
                        """
                        contains_result = await tx.run(
                            contains_query,
                            product_uuid=product_id,
                            ingredient_uuid=actual_ingredient_uuid,
                            amount=ingredient.get('amount'),
                            unit=ingredient.get('unit')
                        )
                        contains_record = await contains_result.single()
                        if not contains_record:
                            logger.error(f"成分关系创建失败: Product={product_id}, Ingredient={actual_ingredient_uuid}")
                            continue
                        logger.info(f"成分关系创建成功: {product_data['product_name']} -> {ingredient['name']}")
                        ingredient_count += 1
                    
                    await tx.commit()
                    logger.info(f"产品保存完成: {product_data['product_name']}, ID: {product_id}, 成分数量: {ingredient_count}")
                except Exception as e:
                    await tx.rollback()
                    logger.error(f"事务回滚: {e}")
                    raise
                finally:
                    await tx.close()
                    
        except Exception as e:
            logger.error(f"Failed to save product: {e}")
            raise
        
        return product_id
    
    async def search_entities(
        self, 
        entity_type: str, 
        keyword: str, 
        limit: int = 5,
        use_vector_search: bool = True
    ) -> List[Dict]:
        """搜索实体 - 支持向量化查询和字符串匹配"""
        try:
            entities = []
            
            # 优先使用向量化查询
            if use_vector_search:
                try:
                    vector_service = self._get_vector_service()
                    vector_matches = await vector_service.find_similar_entities(
                        text=keyword,
                        entity_type=entity_type.lower(),
                        limit=limit,
                        threshold=0.3  # 降低阈值以获取更多候选结果
                    )
                    
                    # 转换向量查询结果
                    for match in vector_matches:
                        entity = {
                            "id": match.entity_id,
                            "name": match.entity_name,
                            "type": entity_type,
                            "match_score": match.similarity_score,
                            "match_type": "vector"
                        }
                        
                        # 如果是产品，需要获取额外属性
                        if entity_type == "Product":
                            try:
                                product_details = await self._get_product_details(match.entity_id)
                                entity.update(product_details)
                            except Exception as e:
                                logger.warning(f"Failed to get product details for {match.entity_id}: {e}")
                        
                        entities.append(entity)
                    
                    logger.info(f"Vector search found {len(entities)} {entity_type} entities for keyword: {keyword}")
                    
                except Exception as e:
                    logger.warning(f"Vector search failed, falling back to string matching: {e}")
            
            # 如果向量查询结果不足或失败，补充字符串匹配结果
            if len(entities) < limit:
                remaining_limit = limit - len(entities)
                string_matches = await self._string_search_entities(entity_type, keyword, remaining_limit * 2)  # 获取更多候选
                
                # 过滤掉已存在的实体(基于名称)
                existing_names = {entity["name"].lower() for entity in entities}
                for match in string_matches:
                    if match["name"].lower() not in existing_names and len(entities) < limit:
                        entities.append(match)
                
                logger.info(f"Combined search found {len(entities)} {entity_type} entities for keyword: {keyword}")
            
            # 按匹配分数排序
            entities.sort(key=lambda x: x["match_score"], reverse=True)
            return entities[:limit]
                
        except Exception as e:
            logger.error(f"Failed to search entities: {e}")
            raise
    
    async def _string_search_entities(
        self, 
        entity_type: str, 
        keyword: str, 
        limit: int = 10
    ) -> List[Dict]:
        """字符串匹配搜索实体"""
        try:
            async with self.driver.session() as session:
                # 使用CONTAINS进行模糊搜索，按长度排序
                if entity_type == "Product":
                    # 产品搜索，返回额外属性
                    query = f"""
                        MATCH (e:{settings.NEO4J_LABEL_PREFIX}_{entity_type})
                        WHERE toLower(e.name) CONTAINS toLower($keyword)
                        RETURN e.name as name, e.uuid as uuid, 
                               e.product_type as product_type, 
                               e.sku as sku, 
                               e.benefits as benefits
                        ORDER BY size(e.name), e.name
                        LIMIT $limit
                    """
                else:
                    # 品牌和成分搜索，只返回基本信息
                    query = f"""
                        MATCH (e:{settings.NEO4J_LABEL_PREFIX}_{entity_type})
                        WHERE toLower(e.name) CONTAINS toLower($keyword)
                        RETURN e.name as name, e.uuid as uuid
                        ORDER BY size(e.name), e.name
                        LIMIT $limit
                    """
                
                result = await session.run(
                    query,
                    keyword=keyword,
                    limit=limit
                )
                
                entities = []
                async for record in result:
                    entity = {
                        "id": record["uuid"] or str(uuid.uuid4()),
                        "name": record["name"],
                        "type": entity_type,
                        "match_score": self._calculate_match_score(record["name"], keyword),
                        "match_type": "string"
                    }
                    
                    # 如果是产品，添加额外属性
                    if entity_type == "Product":
                        entity["product_type"] = record.get("product_type")
                        entity["sku"] = record.get("sku")
                        entity["benefits"] = record.get("benefits")
                    
                    entities.append(entity)
                
                return entities
                
        except Exception as e:
            logger.error(f"Failed to string search entities: {e}")
            return []
    
    async def _get_product_details(self, product_id: str) -> Dict:
        """获取产品的详细属性"""
        try:
            async with self.driver.session() as session:
                query = f"""
                    MATCH (p:{settings.NEO4J_LABEL_PREFIX}_Product)
                    WHERE p.uuid = $product_id
                    RETURN p.product_type as product_type, 
                           p.sku as sku, 
                           p.benefits as benefits
                """
                
                result = await session.run(query, product_id=product_id)
                record = await result.single()
                
                if record:
                    return {
                        "product_type": record.get("product_type"),
                        "sku": record.get("sku"),
                        "benefits": record.get("benefits")
                    }
                else:
                    return {}
                    
        except Exception as e:
            logger.error(f"Failed to get product details for {product_id}: {e}")
            return {}
    
    def _calculate_match_score(self, entity_name: str, keyword: str) -> float:
        """计算匹配分数"""
        entity_lower = entity_name.lower()
        keyword_lower = keyword.lower()
        
        # 完全匹配
        if entity_lower == keyword_lower:
            return 1.0
        
        # 开头匹配
        if entity_lower.startswith(keyword_lower):
            return 0.9
        
        # 包含匹配
        if keyword_lower in entity_lower:
            return 0.7
        
        # 默认分数
        return 0.5
    
    async def save_product_enhanced(self, product_data: Dict) -> str:
        """增强版产品保存 - 支持实体链接"""
        try:
            async with self.driver.session() as session:
                tx = await session.begin_transaction()
                try:
                    # 处理产品实体
                    if product_data.get('product_entity_link') == 'new':
                        # 创建新产品
                        product_id = str(uuid.uuid4())
                        product_query = f"""
                            CREATE (p:{settings.NEO4J_LABEL_PREFIX}_Product {{
                                uuid: $uuid,
                                name: $name,
                                sku: $sku,
                                product_type: $product_type,
                                benefits: $benefits,
                                created_at: datetime(),
                                updated_at: datetime()
                            }})
                            RETURN p.uuid as product_uuid
                        """
                        product_result = await tx.run(
                            product_query,
                            uuid=product_id,
                            name=product_data['product_name'],
                            sku=product_data.get('sku'),
                            product_type=product_data.get('product_type', '保健品'),
                            benefits=product_data.get('benefits')
                        )
                        product_record = await product_result.single()
                        if not product_record:
                            raise Exception(f"Failed to create new product: {product_data['product_name']}")
                        logger.info(f"新产品节点创建: {product_data['product_name']}, UUID: {product_id}")
                        
                        # 异步向量化新产品
                        try:
                            vector_service = self._get_vector_service()
                            product_text = f"{product_data['product_name']} {product_data['brand']}"
                            if product_data.get('benefits'):
                                product_text += f" {product_data['benefits']}"
                            
                            await vector_service.store_entity_vector(
                                entity_id=product_id,
                                entity_type="product",
                                text=product_text
                            )
                            logger.info(f"New product vector stored successfully for: {product_data['product_name']}")
                        except Exception as e:
                            logger.warning(f"Failed to store new product vector for {product_data['product_name']}: {e}")
                    else:
                        # 链接现有产品
                        product_id = product_data['product_entity_link']
                        logger.info(f"链接现有产品: ID={product_id}")
                    
                    # 处理品牌实体
                    if product_data.get('brand_entity_link') == 'new':
                        # 创建新品牌
                        brand_uuid = str(uuid.uuid4())
                        brand_query = f"""
                            MERGE (b:{settings.NEO4J_LABEL_PREFIX}_Brand {{name: $name}})
                            ON CREATE SET b.uuid = $uuid,
                                          b.created_at = datetime()
                            ON MATCH SET b.uuid = COALESCE(b.uuid, $uuid),
                                         b.updated_at = datetime()
                            RETURN b.uuid as brand_uuid
                        """
                        brand_result = await tx.run(
                            brand_query,
                            name=product_data['brand'],
                            uuid=brand_uuid
                        )
                        brand_record = await brand_result.single()
                        actual_brand_uuid = brand_record['brand_uuid']
                        logger.info(f"新品牌处理: {product_data['brand']}, UUID: {actual_brand_uuid}")
                        
                        # 异步向量化新品牌
                        try:
                            vector_service = self._get_vector_service()
                            task_id = vector_service.queue_entity_vectorization(
                                entity_id=actual_brand_uuid,
                                entity_type="brand",
                                text=product_data['brand'],
                                priority="HIGH"
                            )
                            logger.info(f"新品牌向量化任务已排队: {task_id}")
                        except Exception as e:
                            logger.warning(f"新品牌向量化任务排队失败: {e}")
                    else:
                        # 链接现有品牌
                        actual_brand_uuid = product_data['brand_entity_link']
                        logger.info(f"链接现有品牌: ID={actual_brand_uuid}")
                    
                    # 建立品牌关系（只有在创建新产品时才需要）
                    if product_data.get('product_entity_link') == 'new':
                        brand_rel_query = f"""
                            MATCH (p:{settings.NEO4J_LABEL_PREFIX}_Product {{uuid: $product_uuid}})
                            MATCH (b:{settings.NEO4J_LABEL_PREFIX}_Brand {{uuid: $brand_uuid}})
                            MERGE (p)-[:MANUFACTURED_BY]->(b)
                            RETURN p, b
                        """
                        brand_rel_result = await tx.run(
                            brand_rel_query,
                            product_uuid=product_id,
                            brand_uuid=actual_brand_uuid
                        )
                        logger.info(f"品牌关系创建: {product_data['product_name']} -> {product_data['brand']}")
                    
                    # 处理成分实体
                    ingredient_count = 0
                    for ingredient in product_data.get('ingredients', []):
                        if not ingredient.get('name'):
                            continue
                        
                        if ingredient.get('entity_link') == 'new':
                            # 创建新成分
                            ingredient_uuid = str(uuid.uuid4())
                            ingredient_query = f"""
                                MERGE (i:{settings.NEO4J_LABEL_PREFIX}_Ingredient {{name: $name}})
                                ON CREATE SET i.uuid = $uuid,
                                              i.created_at = datetime()
                                ON MATCH SET i.uuid = COALESCE(i.uuid, $uuid),
                                             i.updated_at = datetime()
                                RETURN i.uuid as ingredient_uuid
                            """
                            ingredient_result = await tx.run(
                                ingredient_query,
                                name=ingredient['name'],
                                uuid=ingredient_uuid
                            )
                            ingredient_record = await ingredient_result.single()
                            actual_ingredient_uuid = ingredient_record['ingredient_uuid']
                            logger.info(f"新成分处理: {ingredient['name']}, UUID: {actual_ingredient_uuid}")
                            
                            # 异步向量化新成分
                            try:
                                vector_service = self._get_vector_service()
                                task_id = vector_service.queue_entity_vectorization(
                                    entity_id=actual_ingredient_uuid,
                                    entity_type="ingredient",
                                    text=ingredient['name'],
                                    priority="HIGH"
                                )
                                logger.info(f"新成分向量化任务已排队: {task_id}")
                            except Exception as e:
                                logger.warning(f"新成分向量化任务排队失败: {e}")
                        else:
                            # 链接现有成分
                            actual_ingredient_uuid = ingredient['entity_link']
                            logger.info(f"链接现有成分: ID={actual_ingredient_uuid}")
                        
                        # 建立包含关系（只有在创建新产品时才需要）
                        if product_data.get('product_entity_link') == 'new':
                            contains_query = f"""
                                MATCH (p:{settings.NEO4J_LABEL_PREFIX}_Product {{uuid: $product_uuid}})
                                MATCH (i:{settings.NEO4J_LABEL_PREFIX}_Ingredient {{uuid: $ingredient_uuid}})
                                MERGE (p)-[:CONTAINS {{
                                    amount: $amount,
                                    unit: $unit
                                }}]->(i)
                                RETURN p, i
                            """
                            contains_result = await tx.run(
                                contains_query,
                                product_uuid=product_id,
                                ingredient_uuid=actual_ingredient_uuid,
                                amount=ingredient.get('amount'),
                                unit=ingredient.get('unit')
                            )
                            logger.info(f"成分关系创建: {product_data['product_name']} -> {ingredient['name']}")
                        ingredient_count += 1
                    
                    await tx.commit()
                    logger.info(f"增强版产品保存完成: {product_data['product_name']}, ID: {product_id}, 成分数量: {ingredient_count}")
                    
                except Exception as e:
                    await tx.rollback()
                    logger.error(f"事务回滚: {e}")
                    raise
                finally:
                    await tx.close()
                    
        except Exception as e:
            logger.error(f"Failed to save enhanced product: {e}")
            raise
        
        return product_id
    
    async def run_query(self, query: str, parameters: dict = None) -> List[Dict]:
        """执行Cypher查询并返回结果"""
        try:
            async with self.driver.session() as session:
                result = await session.run(query, parameters or {})
                records = []
                async for record in result:
                    records.append(record.data())
                return records
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {parameters}")
            raise
    
    async def init_database(self):
        """初始化数据库索引和约束"""
        try:
            async with self.driver.session() as session:
                # 创建唯一性约束
                prefix = settings.NEO4J_LABEL_PREFIX
                constraints = [
                    f"CREATE CONSTRAINT {prefix.lower()}_product_uuid_unique IF NOT EXISTS FOR (p:{prefix}_Product) REQUIRE p.uuid IS UNIQUE",
                    f"CREATE CONSTRAINT {prefix.lower()}_brand_uuid_unique IF NOT EXISTS FOR (b:{prefix}_Brand) REQUIRE b.uuid IS UNIQUE", 
                    f"CREATE CONSTRAINT {prefix.lower()}_ingredient_uuid_unique IF NOT EXISTS FOR (i:{prefix}_Ingredient) REQUIRE i.uuid IS UNIQUE",
                    f"CREATE CONSTRAINT {prefix.lower()}_brand_name_unique IF NOT EXISTS FOR (b:{prefix}_Brand) REQUIRE b.name IS UNIQUE",
                    f"CREATE CONSTRAINT {prefix.lower()}_ingredient_name_unique IF NOT EXISTS FOR (i:{prefix}_Ingredient) REQUIRE i.name IS UNIQUE"
                ]
                
                for constraint in constraints:
                    try:
                        await session.run(constraint)
                        logger.info(f"Created constraint: {constraint}")
                    except Exception as e:
                        # 约束可能已存在，忽略错误
                        logger.debug(f"Constraint already exists or failed: {e}")
                
                # 创建索引
                indexes = [
                    f"CREATE INDEX {prefix.lower()}_brand_name_index IF NOT EXISTS FOR (b:{prefix}_Brand) ON (b.name)",
                    f"CREATE INDEX {prefix.lower()}_ingredient_name_index IF NOT EXISTS FOR (i:{prefix}_Ingredient) ON (i.name)",
                    f"CREATE INDEX {prefix.lower()}_product_name_index IF NOT EXISTS FOR (p:{prefix}_Product) ON (p.name)",
                    # 向量化相关索引
                    f"CREATE INDEX {prefix.lower()}_brand_embedding_dim_index IF NOT EXISTS FOR (b:{prefix}_Brand) ON (b.embedding_dim)",
                    f"CREATE INDEX {prefix.lower()}_ingredient_embedding_dim_index IF NOT EXISTS FOR (i:{prefix}_Ingredient) ON (i.embedding_dim)",
                    f"CREATE INDEX {prefix.lower()}_product_embedding_dim_index IF NOT EXISTS FOR (p:{prefix}_Product) ON (p.embedding_dim)"
                ]
                
                for index in indexes:
                    try:
                        await session.run(index)
                        logger.info(f"Created index: {index}")
                    except Exception as e:
                        logger.debug(f"Index already exists or failed: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise


# 全局数据库服务实例
neo4j_service = Neo4jService()