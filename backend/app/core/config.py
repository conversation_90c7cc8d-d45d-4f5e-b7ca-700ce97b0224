from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # API配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "知识图谱智能录入系统"
    
    # Neo4j配置
    NEO4J_URI: str = "bolt://neo4j:7687"
    NEO4J_USERNAME: str = "neo4j"
    NEO4J_PASSWORD: str = "password"
    NEO4J_LABEL_PREFIX: str = "KGMS"
    
    # 火山引擎配置
    ARK_API_KEY: str
    BASE_URL: str = "https://ark.cn-beijing.volces.com/api/v3"
    MODEL_ID: str = "doubao-seed-1-6-flash-250715"
    ARK_MAX_RETRIES: int = 3
    ARK_TIMEOUT: int = 30
    
    # LangChain配置
    LANGCHAIN_CACHE_ENABLED: bool = True
    LANGCHAIN_VERBOSE: bool = False
    
    # 应用环境配置
    DEBUG: bool = False
    ENVIRONMENT: str = "production"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()