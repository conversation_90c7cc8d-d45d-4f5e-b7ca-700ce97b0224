from fastapi import APIRouter, HTTPException, Query
import logging
from app.models.entity import EntitySearchResponse, EntityInfo
from app.db.neo4j import neo4j_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/entities/search", response_model=EntitySearchResponse)
async def search_entities(
    type: str = Query(..., description="实体类型：Brand/Ingredient"),
    keyword: str = Query(..., min_length=1, description="搜索关键词（支持单字符成分如：铁、钙、锌）"),
    limit: int = Query(default=3, ge=1, le=10, description="返回数量限制"),
    use_vector: bool = Query(default=True, description="是否使用向量化搜索")
):
    """搜索实体"""
    try:
        # 验证实体类型
        if type not in ["Brand", "Ingredient", "Product"]:
            return EntitySearchResponse(
                success=False,
                error="实体类型必须是 Brand、Ingredient 或 Product"
            )
        
        search_method = "vector" if use_vector else "string"
        logger.info(f"Searching {type} entities for keyword: {keyword} (method: {search_method})")
        
        # 执行搜索
        entities = await neo4j_service.search_entities(type, keyword, limit, use_vector)
        
        # 转换为响应模型
        entity_infos = []
        for entity in entities:
            entity_info = EntityInfo(
                id=entity["id"],
                name=entity["name"],
                type=entity["type"],
                match_score=entity["match_score"],
                match_type=entity.get("match_type", "string")
            )
            
            # 如果是产品类型，添加产品专属属性
            if entity["type"] == "Product":
                entity_info.product_type = entity.get("product_type")
                entity_info.sku = entity.get("sku")
                entity_info.benefits = entity.get("benefits")
            
            entity_infos.append(entity_info)
        
        
        logger.info(f"Found {len(entity_infos)} entities")
        
        return EntitySearchResponse(
            success=True,
            data=entity_infos
        )
        
    except Exception as e:
        logger.error(f"Entity search error: {e}")
        return EntitySearchResponse(
            success=False,
            error=f"搜索失败: {str(e)}"
        )