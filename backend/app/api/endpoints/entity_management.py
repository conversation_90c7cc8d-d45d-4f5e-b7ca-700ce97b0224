"""
实体管理API端点
提供品牌、成分、产品的CRUD操作接口
"""

import logging
from fastapi import APIRouter, HTTPException, Query, Path
from typing import List, Optional

from app.models.entity_management import (
    # 品牌相关
    BrandCreateRequest, BrandUpdateRequest, BrandListRequest,
    BrandListResponse, BrandDetailResponse,
    
    # 成分相关
    IngredientCreateRequest, IngredientUpdateRequest, IngredientListRequest,
    IngredientListResponse, IngredientDetailResponse,
    
    # 产品相关
    ProductCreateRequest, ProductUpdateRequest, ProductListRequest,
    ProductListResponse, ProductDetailResponse,
    
    # 批量操作
    BatchOperationRequest, BatchOperationResponse,
    
    # 通用响应
    StandardResponse, EntityStatsResponse,
    
    # 枚举类型
    FilterType, SortOrder
)
from app.services.entity_management_service import entity_management_service

logger = logging.getLogger(__name__)

router = APIRouter()

# =============================================================================
# 品牌管理API
# =============================================================================

@router.get("/brands", response_model=BrandListResponse)
async def get_brands(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    filter_type: FilterType = Query(FilterType.ALL, description="筛选类型"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: SortOrder = Query(SortOrder.DESC, description="排序方向")
):
    """获取品牌列表"""
    try:
        request = BrandListRequest(
            page=page,
            size=size,
            search=search,
            filter_type=filter_type,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        brands, total = await entity_management_service.get_brands(request)
        
        return BrandListResponse(
            success=True,
            data=brands,
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取品牌列表失败: {e}")
        return BrandListResponse(
            success=False,
            data=[],
            total=0,
            page=page,
            size=size,
            error=str(e)
        )

@router.get("/brands/{brand_id}", response_model=BrandDetailResponse)
async def get_brand_detail(
    brand_id: str = Path(..., description="品牌ID")
):
    """获取品牌详情"""
    try:
        brand = await entity_management_service.get_brand_detail(brand_id)
        
        if not brand:
            raise HTTPException(status_code=404, detail="品牌不存在")
        
        return BrandDetailResponse(
            success=True,
            data=brand
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取品牌详情失败: {e}")
        return BrandDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.post("/brands", response_model=BrandDetailResponse)
async def create_brand(request: BrandCreateRequest):
    """创建品牌"""
    try:
        brand = await entity_management_service.create_brand(request)
        
        return BrandDetailResponse(
            success=True,
            data=brand
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建品牌失败: {e}")
        return BrandDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.put("/brands/{brand_id}", response_model=BrandDetailResponse)
async def update_brand(
    brand_id: str = Path(..., description="品牌ID"),
    request: BrandUpdateRequest = ...
):
    """更新品牌"""
    try:
        brand = await entity_management_service.update_brand(brand_id, request)
        
        if not brand:
            raise HTTPException(status_code=404, detail="品牌不存在")
        
        return BrandDetailResponse(
            success=True,
            data=brand
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"更新品牌失败: {e}")
        return BrandDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.delete("/brands/{brand_id}", response_model=StandardResponse)
async def delete_brand(
    brand_id: str = Path(..., description="品牌ID")
):
    """删除品牌"""
    try:
        success = await entity_management_service.delete_brand(brand_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="品牌不存在")
        
        return StandardResponse(
            success=True,
            message="品牌删除成功"
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"删除品牌失败: {e}")
        return StandardResponse(
            success=False,
            error=str(e)
        )

# =============================================================================
# 成分管理API
# =============================================================================

@router.get("/ingredients", response_model=IngredientListResponse)
async def get_ingredients(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    filter_type: FilterType = Query(FilterType.ALL, description="筛选类型"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: SortOrder = Query(SortOrder.DESC, description="排序方向")
):
    """获取成分列表"""
    try:
        request = IngredientListRequest(
            page=page,
            size=size,
            search=search,
            filter_type=filter_type,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        ingredients, total = await entity_management_service.get_ingredients(request)
        
        return IngredientListResponse(
            success=True,
            data=ingredients,
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取成分列表失败: {e}")
        return IngredientListResponse(
            success=False,
            data=[],
            total=0,
            page=page,
            size=size,
            error=str(e)
        )

@router.get("/ingredients/{ingredient_id}", response_model=IngredientDetailResponse)
async def get_ingredient_detail(
    ingredient_id: str = Path(..., description="成分ID")
):
    """获取成分详情"""
    try:
        ingredient = await entity_management_service.get_ingredient_detail(ingredient_id)
        
        if not ingredient:
            raise HTTPException(status_code=404, detail="成分不存在")
        
        return IngredientDetailResponse(
            success=True,
            data=ingredient
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取成分详情失败: {e}")
        return IngredientDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.post("/ingredients", response_model=IngredientDetailResponse)
async def create_ingredient(request: IngredientCreateRequest):
    """创建成分"""
    try:
        ingredient = await entity_management_service.create_ingredient(request)
        
        return IngredientDetailResponse(
            success=True,
            data=ingredient
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建成分失败: {e}")
        return IngredientDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.put("/ingredients/{ingredient_id}", response_model=IngredientDetailResponse)
async def update_ingredient(
    ingredient_id: str = Path(..., description="成分ID"),
    request: IngredientUpdateRequest = ...
):
    """更新成分"""
    try:
        ingredient = await entity_management_service.update_ingredient(ingredient_id, request)
        
        if not ingredient:
            raise HTTPException(status_code=404, detail="成分不存在")
        
        return IngredientDetailResponse(
            success=True,
            data=ingredient
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"更新成分失败: {e}")
        return IngredientDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.delete("/ingredients/{ingredient_id}", response_model=StandardResponse)
async def delete_ingredient(
    ingredient_id: str = Path(..., description="成分ID")
):
    """删除成分"""
    try:
        success = await entity_management_service.delete_ingredient(ingredient_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="成分不存在")
        
        return StandardResponse(
            success=True,
            message="成分删除成功"
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"删除成分失败: {e}")
        return StandardResponse(
            success=False,
            error=str(e)
        )

# =============================================================================
# 产品管理API
# =============================================================================

@router.get("/products", response_model=ProductListResponse)
async def get_products(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    brand_filter: Optional[str] = Query(None, description="品牌筛选"),
    ingredient_filter: Optional[str] = Query(None, description="成分筛选"),
    filter_type: FilterType = Query(FilterType.ALL, description="筛选类型"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: SortOrder = Query(SortOrder.DESC, description="排序方向")
):
    """获取产品列表"""
    try:
        request = ProductListRequest(
            page=page,
            size=size,
            search=search,
            brand_filter=brand_filter,
            ingredient_filter=ingredient_filter,
            filter_type=filter_type,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        products, total = await entity_management_service.get_products(request)
        
        return ProductListResponse(
            success=True,
            data=products,
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        return ProductListResponse(
            success=False,
            data=[],
            total=0,
            page=page,
            size=size,
            error=str(e)
        )

@router.get("/products/{product_id}", response_model=ProductDetailResponse)
async def get_product_detail(
    product_id: str = Path(..., description="产品ID")
):
    """获取产品详情"""
    try:
        product = await entity_management_service.get_product_detail(product_id)
        
        if not product:
            raise HTTPException(status_code=404, detail="产品不存在")
        
        return ProductDetailResponse(
            success=True,
            data=product
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取产品详情失败: {e}")
        return ProductDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.post("/products", response_model=ProductDetailResponse)
async def create_product(request: ProductCreateRequest):
    """创建产品"""
    try:
        product = await entity_management_service.create_product(request)
        
        return ProductDetailResponse(
            success=True,
            data=product
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"创建产品失败: {e}")
        return ProductDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.put("/products/{product_id}", response_model=ProductDetailResponse)
async def update_product(
    product_id: str = Path(..., description="产品ID"),
    request: ProductUpdateRequest = ...
):
    """更新产品"""
    try:
        product = await entity_management_service.update_product(product_id, request)
        
        if not product:
            raise HTTPException(status_code=404, detail="产品不存在")
        
        return ProductDetailResponse(
            success=True,
            data=product
        )
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"更新产品失败: {e}")
        return ProductDetailResponse(
            success=False,
            data=None,
            error=str(e)
        )

@router.delete("/products/{product_id}", response_model=StandardResponse)
async def delete_product(
    product_id: str = Path(..., description="产品ID")
):
    """删除产品"""
    try:
        success = await entity_management_service.delete_product(product_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="产品不存在")
        
        return StandardResponse(
            success=True,
            message="产品删除成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除产品失败: {e}")
        return StandardResponse(
            success=False,
            error=str(e)
        )

# =============================================================================
# 统计信息API
# =============================================================================

@router.get("/stats", response_model=EntityStatsResponse)
async def get_entity_stats():
    """获取实体统计信息"""
    try:
        stats = await entity_management_service.get_entity_stats()
        
        return EntityStatsResponse(
            success=True,
            data=stats
        )
        
    except Exception as e:
        logger.error(f"获取实体统计失败: {e}")
        return EntityStatsResponse(
            success=False,
            data={},
            error=str(e)
        )

# =============================================================================
# 批量操作接口
# =============================================================================

@router.post("/batch-operation", response_model=BatchOperationResponse)
async def batch_operation(request: BatchOperationRequest):
    """执行批量操作"""
    try:
        logger.info(f"批量操作请求: {request.operation_type} {request.entity_type}, 实体数量: {len(request.entity_ids)}")
        
        # 验证数量限制
        if len(request.entity_ids) > 100:
            raise HTTPException(status_code=400, detail="一次最多处理100个实体")
        
        # 执行批量操作
        result = await entity_management_service.batch_operation(request)
        
        # 记录操作结果
        logger.info(f"批量操作完成: 成功 {result.success_count}, 失败 {result.failed_count}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量操作异常: {e}")
        return BatchOperationResponse(
            success=False,
            processed_count=len(request.entity_ids),
            success_count=0,
            failed_count=len(request.entity_ids),
            details=[],
            error=str(e)
        )