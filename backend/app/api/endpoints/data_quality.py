"""
数据质量监控API端点
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import List, Optional
import logging

from app.services.data_quality_service import get_data_quality_service
from app.models.data_quality import (
    DataQualityReportRequest,
    DataQualityReportResponse,
    QualityCheckConfig,
    QualityCheckResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# 懒加载服务
_quality_service = None

def get_quality_service():
    """获取数据质量服务实例（懒加载）"""
    global _quality_service
    if _quality_service is None:
        _quality_service = get_data_quality_service()
    return _quality_service


@router.post("/report", response_model=DataQualityReportResponse)
async def generate_quality_report(request: DataQualityReportRequest):
    """
    生成数据质量报告
    """
    try:
        logger.info(f"生成数据质量报告: {request.entity_types}, {request.check_types}")
        
        # 输入验证
        valid_entity_types = {"brand", "ingredient", "product"}
        valid_check_types = {
            "duplicate_detection", "missing_vectors", "orphan_nodes",
            "missing_relationships", "data_completeness", "naming_consistency"
        }
        
        if request.entity_types:
            invalid_types = set(request.entity_types) - valid_entity_types
            if invalid_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的实体类型: {list(invalid_types)}"
                )
        
        if request.check_types:
            invalid_checks = set(request.check_types) - valid_check_types
            if invalid_checks:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的检查类型: {list(invalid_checks)}"
                )
        
        # 生成报告
        quality_service = get_quality_service()
        report = await quality_service.generate_quality_report(
            entity_types=request.entity_types,
            check_types=request.check_types
        )
        
        logger.info(f"数据质量报告生成完成: {report.report_id}")
        
        return DataQualityReportResponse(
            success=True,
            data=report
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成数据质量报告失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成报告失败: {str(e)}"
        )


@router.get("/config")
async def get_quality_check_config():
    """
    获取数据质量检查配置
    """
    try:
        return {
            "success": True,
            "data": {
                "supported_entity_types": ["brand", "ingredient", "product"],
                "supported_check_types": [
                    {
                        "type": "duplicate_detection",
                        "name": "重复实体检测",
                        "description": "检测疑似重复的实体"
                    },
                    {
                        "type": "missing_vectors",
                        "name": "缺失向量检测",
                        "description": "检测没有生成向量的实体"
                    },
                    {
                        "type": "orphan_nodes",
                        "name": "孤立节点检测",
                        "description": "检测没有关联关系的实体"
                    },
                    {
                        "type": "missing_relationships",
                        "name": "缺失关系检测",
                        "description": "检测缺少必要关系的实体"
                    },
                    {
                        "type": "data_completeness",
                        "name": "数据完整性检测",
                        "description": "检测缺少必要属性的实体"
                    },
                    {
                        "type": "naming_consistency",
                        "name": "命名一致性检测",
                        "description": "检测命名格式问题"
                    }
                ],
                "severity_levels": [
                    {
                        "level": "critical",
                        "name": "严重",
                        "description": "需要立即处理的问题"
                    },
                    {
                        "level": "warning",
                        "name": "警告",
                        "description": "建议处理的问题"
                    },
                    {
                        "level": "info",
                        "name": "信息",
                        "description": "可以优化的问题"
                    }
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取配置失败: {str(e)}"
        )


@router.post("/quick-check", response_model=QualityCheckResponse)
async def quick_quality_check():
    """
    快速质量检查
    """
    try:
        logger.info("执行快速质量检查")
        
        quality_service = get_quality_service()
        
        # 执行轻量级检查
        report = await quality_service.generate_quality_report(
            entity_types=["brand", "ingredient", "product"],
            check_types=["missing_vectors", "orphan_nodes"]
        )
        
        # 简化结果
        summary = {
            "overall_score": report.overall_score,
            "total_entities": report.total_entities,
            "issues_found": report.issues_found,
            "critical_issues": report.critical_issues,
            "warning_issues": report.warning_issues,
            "info_issues": report.info_issues,
            "top_recommendations": report.recommendations[:3]
        }
        
        logger.info(f"快速质量检查完成: 评分 {report.overall_score}")
        
        return QualityCheckResponse(
            success=True,
            data=summary
        )
        
    except Exception as e:
        logger.error(f"快速质量检查失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"检查失败: {str(e)}"
        )


@router.get("/stats")
async def get_quality_stats():
    """
    获取数据质量统计信息
    """
    try:
        logger.info("获取数据质量统计")
        
        quality_service = get_quality_service()
        
        # 获取基础统计
        entity_stats = await quality_service._get_entity_statistics(["brand", "ingredient", "product"])
        
        # 获取向量化统计
        from app.services.vector_storage_service import get_vector_storage_service
        vector_service = get_vector_storage_service()
        
        vectorization_stats = {}
        for entity_type in ["brand", "ingredient", "product"]:
            missing_vectors = await vector_service.get_entities_without_vectors(entity_type)
            total = entity_stats.get(entity_type, 0)
            vectorized = total - len(missing_vectors)
            
            vectorization_stats[entity_type] = {
                "total": total,
                "vectorized": vectorized,
                "missing_vectors": len(missing_vectors),
                "vectorization_rate": round(vectorized / total * 100, 1) if total > 0 else 0
            }
        
        # 汇总
        total_entities = sum(entity_stats.values())
        total_vectorized = sum(s["vectorized"] for s in vectorization_stats.values())
        
        stats = {
            "entity_counts": entity_stats,
            "total_entities": total_entities,
            "vectorization_stats": vectorization_stats,
            "overall_vectorization_rate": round(total_vectorized / total_entities * 100, 1) if total_entities > 0 else 0,
            "last_updated": None  # 可以记录最后检查时间
        }
        
        return {
            "success": True,
            "data": stats
        }
        
    except Exception as e:
        logger.error(f"获取质量统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取统计失败: {str(e)}"
        )


@router.get("/health")
async def quality_service_health():
    """
    数据质量服务健康检查
    """
    try:
        # 测试数据库连接
        quality_service = get_quality_service()
        entity_stats = await quality_service._get_entity_statistics(["brand"])
        
        return {
            "success": True,
            "data": {
                "service_status": "healthy",
                "database_connected": True,
                "vector_service_available": True,
                "last_check": None
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "data": {
                "service_status": "unhealthy",
                "database_connected": False,
                "vector_service_available": False,
                "error": str(e)
            }
        }