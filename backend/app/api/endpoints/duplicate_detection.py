"""
智能去重检测API端点
"""

from fastapi import APIRouter, HTTPException
from typing import List, Dict, Any
import logging

from app.services.duplicate_detection_service import get_duplicate_detection_service, DuplicateDetectionResult
from app.models.duplicate_detection import (
    DuplicateDetectionRequest, 
    DuplicateDetectionResponse,
    BatchDuplicateDetectionRequest,
    BatchDuplicateDetectionResponse,
    EntityDetailsRequest,
    EntityDetailsResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# 懒加载检测服务
_detection_service = None

def get_detection_service():
    """获取检测服务实例（懒加载）"""
    global _detection_service
    if _detection_service is None:
        _detection_service = get_duplicate_detection_service()
    return _detection_service


@router.post("/detect", response_model=DuplicateDetectionResponse)
async def detect_duplicates(request: DuplicateDetectionRequest):
    """
    检测实体重复
    """
    try:
        logger.info(f"开始去重检测: {request.entity_type} '{request.entity_name}'")
        
        # 输入验证
        if not request.entity_name or len(request.entity_name.strip()) < 1:
            raise HTTPException(
                status_code=400,
                detail="实体名称不能为空"
            )
        
        if request.entity_type not in ["brand", "ingredient", "product"]:
            raise HTTPException(
                status_code=400,
                detail="实体类型必须是: brand, ingredient, product"
            )
        
        # 执行检测
        detection_service = get_detection_service()
        result = await detection_service.detect_duplicates(
            entity_name=request.entity_name.strip(),
            entity_type=request.entity_type,
            limit=request.limit or 10
        )
        
        logger.info(f"去重检测完成: {result.recommendation}, 候选数量: {len(result.candidates)}")
        
        return DuplicateDetectionResponse(
            success=True,
            data=result
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"去重检测失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"检测失败: {str(e)}"
        )


@router.post("/batch-detect", response_model=BatchDuplicateDetectionResponse)
async def batch_detect_duplicates(request: BatchDuplicateDetectionRequest):
    """
    批量检测实体重复
    """
    try:
        logger.info(f"开始批量去重检测: {len(request.entities)} 个实体")
        
        # 输入验证
        if not request.entities or len(request.entities) == 0:
            raise HTTPException(
                status_code=400,
                detail="实体列表不能为空"
            )
        
        if len(request.entities) > 100:  # 限制批量数量
            raise HTTPException(
                status_code=400,
                detail="批量检测数量不能超过100个"
            )
        
        # 验证每个实体
        entities = []
        for i, entity in enumerate(request.entities):
            if not entity.entity_name or len(entity.entity_name.strip()) < 1:
                raise HTTPException(
                    status_code=400,
                    detail=f"第 {i+1} 个实体名称不能为空"
                )
            
            if entity.entity_type not in ["brand", "ingredient", "product"]:
                raise HTTPException(
                    status_code=400,
                    detail=f"第 {i+1} 个实体类型必须是: brand, ingredient, product"
                )
            
            entities.append({
                "name": entity.entity_name.strip(),
                "type": entity.entity_type
            })
        
        # 执行批量检测
        detection_service = get_detection_service()
        results = await detection_service.batch_detect_duplicates(entities)
        
        # 统计结果
        stats = {"link": 0, "manual_review": 0, "create_new": 0, "total_candidates": 0}
        for result in results:
            stats[result.recommendation] += 1
            stats["total_candidates"] += len(result.candidates)
        
        logger.info(f"批量去重检测完成: {stats}")
        
        return BatchDuplicateDetectionResponse(
            success=True,
            data={
                "results": results,
                "summary": {
                    "total_entities": len(results),
                    "link_recommended": stats["link"],
                    "manual_review_recommended": stats["manual_review"],
                    "create_new_recommended": stats["create_new"],
                    "total_candidates_found": stats["total_candidates"]
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量去重检测失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量检测失败: {str(e)}"
        )


@router.post("/entity-details", response_model=EntityDetailsResponse)
async def get_entity_details(request: EntityDetailsRequest):
    """
    获取实体详细信息用于比较
    """
    try:
        logger.info(f"获取实体详情: {request.entity_type} ID={request.entity_id}")
        
        # 输入验证
        if not request.entity_id or len(request.entity_id.strip()) < 1:
            raise HTTPException(
                status_code=400,
                detail="实体ID不能为空"
            )
        
        if request.entity_type not in ["brand", "ingredient", "product"]:
            raise HTTPException(
                status_code=400,
                detail="实体类型必须是: brand, ingredient, product"
            )
        
        # 获取详细信息
        detection_service = get_detection_service()
        details = await detection_service.get_entity_details(
            entity_id=request.entity_id.strip(),
            entity_type=request.entity_type
        )
        
        if not details:
            raise HTTPException(
                status_code=404,
                detail="未找到指定实体"
            )
        
        logger.info(f"实体详情获取成功: {details.get('name', 'Unknown')}")
        
        return EntityDetailsResponse(
            success=True,
            data=details
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取实体详情失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取详情失败: {str(e)}"
        )


@router.get("/stats")
async def get_duplicate_stats():
    """
    获取去重检测统计信息
    """
    try:
        # 这里可以添加去重检测的统计信息，比如：
        # - 总检测次数
        # - 发现重复的数量
        # - 各类推荐的分布等
        
        # 目前返回基础信息
        return {
            "success": True,
            "data": {
                "service_status": "active",
                "supported_entities": ["brand", "ingredient", "product"],
                "thresholds": {
                    "brand": {"high_risk": 0.95, "medium_risk": 0.85, "low_risk": 0.75},
                    "ingredient": {"high_risk": 0.90, "medium_risk": 0.80, "low_risk": 0.70},
                    "product": {"high_risk": 0.85, "medium_risk": 0.75, "low_risk": 0.65}
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取去重统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取统计失败: {str(e)}"
        )