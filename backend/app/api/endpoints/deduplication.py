"""
Phase 2 Week 2: 实体去重API端点
提供向量相似度和实体去重功能的API接口
"""

import time
import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from app.services.entity_deduplication import (
    get_deduplication_service, 
    EntityType, 
    DuplicationResult
)
from app.services.embedding_service import get_embedding_service

logger = logging.getLogger(__name__)
router = APIRouter()

# 请求模型
class DuplicationCheckRequest(BaseModel):
    entity_name: str = Field(..., description="实体名称", min_length=1, max_length=100)
    entity_type: str = Field(..., description="实体类型: brand/ingredient/product")
    candidate_limit: int = Field(default=20, description="候选实体数量限制", ge=1, le=50)

class BatchDuplicationRequest(BaseModel):
    entities: List[Dict[str, str]] = Field(..., description="实体列表 [{'name': 'entity_name'}, ...]")
    entity_type: str = Field(..., description="实体类型: brand/ingredient/product")

class SimilarityRequest(BaseModel):
    text1: str = Field(..., description="文本1", min_length=1, max_length=500)
    text2: str = Field(..., description="文本2", min_length=1, max_length=500)
    category: str = Field(default="general", description="相似度类别: brand/ingredient/product/general")
    method: str = Field(default="cosine", description="计算方法: cosine/euclidean/dot")

class BatchSimilarityRequest(BaseModel):
    query_text: str = Field(..., description="查询文本", min_length=1, max_length=500)
    candidate_texts: List[str] = Field(..., description="候选文本列表")
    category: str = Field(default="general", description="相似度类别")
    top_k: int = Field(default=5, description="返回前k个结果", ge=1, le=20)

# 响应模型
class StandardResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@router.post("/check-duplication", response_model=StandardResponse)
async def check_entity_duplication(request: DuplicationCheckRequest):
    """
    检测实体重复
    
    检查新实体与现有实体的重复情况，提供智能合并建议
    """
    start_time = time.time()
    
    try:
        logger.info(f"Checking duplication for {request.entity_type}: {request.entity_name}")
        
        # 验证实体类型
        try:
            entity_type = EntityType(request.entity_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid entity_type. Must be one of: {[t.value for t in EntityType]}"
            )
        
        # 执行重复检测
        deduplication_service = get_deduplication_service()
        result = await deduplication_service.detect_duplicates(
            request.entity_name,
            entity_type,
            request.candidate_limit
        )
        
        processing_time = time.time() - start_time
        
        return StandardResponse(
            success=True,
            data=result.to_dict(),
            metadata={
                "processing_time": processing_time,
                "candidates_checked": len(result.similar_entities),
                "duplication_level": result.duplication_level.value,
                "recommended_action": result.merge_recommendation.get("action")
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Duplication check failed: {e}")
        
        return StandardResponse(
            success=False,
            error=f"重复检测失败: {str(e)}",
            metadata={
                "processing_time": processing_time
            }
        )

@router.post("/batch-duplication", response_model=StandardResponse)
async def batch_duplication_check(request: BatchDuplicationRequest):
    """
    批量重复检测
    
    对多个实体同时进行重复检测，适用于批量数据处理
    """
    start_time = time.time()
    
    try:
        logger.info(f"Batch duplication check for {len(request.entities)} {request.entity_type} entities")
        
        # 验证实体类型
        try:
            entity_type = EntityType(request.entity_type)
        except ValueError:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid entity_type. Must be one of: {[t.value for t in EntityType]}"
            )
        
        # 限制批量大小
        if len(request.entities) > 50:
            raise HTTPException(
                status_code=400,
                detail="Batch size limit exceeded. Maximum 50 entities per request."
            )
        
        # 执行批量检测
        deduplication_service = get_deduplication_service()
        results = await deduplication_service.batch_duplicate_detection(
            request.entities,
            entity_type
        )
        
        # 分析统计信息
        stats = deduplication_service.analyze_duplication_stats(results)
        
        processing_time = time.time() - start_time
        
        return StandardResponse(
            success=True,
            data={
                "results": [result.to_dict() for result in results],
                "statistics": stats
            },
            metadata={
                "processing_time": processing_time,
                "entities_processed": len(results),
                "duplication_rate": stats.get("duplication_rate", 0)
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Batch duplication check failed: {e}")
        
        return StandardResponse(
            success=False,
            error=f"批量重复检测失败: {str(e)}",
            metadata={
                "processing_time": processing_time
            }
        )

@router.post("/similarity", response_model=StandardResponse)
async def calculate_similarity(request: SimilarityRequest):
    """
    计算文本相似度
    
    使用向量相似度计算两个文本的语义相似度
    """
    start_time = time.time()
    
    try:
        logger.info(f"Calculating similarity: '{request.text1}' vs '{request.text2}'")
        
        # 执行相似度计算
        embedding_service = get_embedding_service()
        result = await embedding_service.calculate_similarity(
            request.text1,
            request.text2,
            request.category,
            request.method
        )
        
        processing_time = time.time() - start_time
        
        return StandardResponse(
            success=True,
            data=result.to_dict(),
            metadata={
                "processing_time": processing_time,
                "method": request.method,
                "category": request.category
            }
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Similarity calculation failed: {e}")
        
        return StandardResponse(
            success=False,
            error=f"相似度计算失败: {str(e)}",
            metadata={
                "processing_time": processing_time
            }
        )

@router.post("/batch-similarity", response_model=StandardResponse)
async def batch_similarity_calculation(request: BatchSimilarityRequest):
    """
    批量相似度计算
    
    计算一个查询文本与多个候选文本的相似度，返回排序结果
    """
    start_time = time.time()
    
    try:
        logger.info(f"Batch similarity: 1 query vs {len(request.candidate_texts)} candidates")
        
        # 限制候选文本数量
        if len(request.candidate_texts) > 100:
            raise HTTPException(
                status_code=400,
                detail="Too many candidates. Maximum 100 texts per request."
            )
        
        # 执行批量相似度计算
        embedding_service = get_embedding_service()
        results = await embedding_service.batch_similarity(
            request.query_text,
            request.candidate_texts,
            request.category,
            request.top_k
        )
        
        processing_time = time.time() - start_time
        
        return StandardResponse(
            success=True,
            data={
                "query_text": request.query_text,
                "results": [result.to_dict() for result in results]
            },
            metadata={
                "processing_time": processing_time,
                "candidates_checked": len(request.candidate_texts),
                "results_returned": len(results),
                "category": request.category
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Batch similarity calculation failed: {e}")
        
        return StandardResponse(
            success=False,
            error=f"批量相似度计算失败: {str(e)}",
            metadata={
                "processing_time": processing_time
            }
        )

@router.get("/embedding-info")
async def get_embedding_info():
    """获取向量化服务信息"""
    try:
        embedding_service = get_embedding_service()
        
        cache_stats = embedding_service.get_cache_stats()
        
        return StandardResponse(
            success=True,
            data={
                "model": embedding_service.model,
                "base_url": embedding_service.base_url,
                "vector_dimension": "2048",  # doubao-embedding-large-text-250515的维度
                "similarity_thresholds": embedding_service.similarity_thresholds,
                "cache_stats": cache_stats
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get embedding info: {e}")
        return StandardResponse(
            success=False,
            error=str(e)
        )

@router.post("/clear-cache")
async def clear_embedding_cache():
    """清理向量化缓存"""
    try:
        embedding_service = get_embedding_service()
        await embedding_service.clear_expired_cache()
        
        cache_stats = embedding_service.get_cache_stats()
        
        return StandardResponse(
            success=True,
            data={
                "message": "Cache cleared successfully",
                "cache_stats": cache_stats
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {e}")
        return StandardResponse(
            success=False,
            error=str(e)
        )