from fastapi import APIRouter, HTTPException
import logging
from app.models.product import ExtractionRequest, ExtractionResponse
from app.langchain.chains import ProductExtractionChain

logger = logging.getLogger(__name__)
router = APIRouter()

# 懒加载提取链实例
_extraction_chain = None

def get_extraction_chain():
    """获取提取链实例（懒加载）"""
    global _extraction_chain
    if _extraction_chain is None:
        _extraction_chain = ProductExtractionChain()
    return _extraction_chain


@router.post("/extract", response_model=ExtractionResponse)
async def extract_product_info(request: ExtractionRequest):
    """AI提取产品信息"""
    try:
        # 输入验证
        if not request.text or len(request.text.strip()) < 5:
            return ExtractionResponse(
                success=False,
                error="请输入至少5个字符的产品描述文本"
            )
        
        # 执行提取
        logger.info(f"Starting extraction for text length: {len(request.text)}")
        extraction_chain = get_extraction_chain()
        result = await extraction_chain.extract(request.text)
        
        # 计算置信度
        confidence = calculate_confidence(result)
        
        logger.info(f"Extraction completed with confidence: {confidence}")
        
        return ExtractionResponse(
            success=True,
            data=result,
            confidence=confidence
        )
        
    except Exception as e:
        logger.error(f"Extraction error: {e}")
        return ExtractionResponse(
            success=False,
            error=f"提取失败: {str(e)}"
        )


def calculate_confidence(result) -> float:
    """计算提取结果的置信度"""
    score = 0.0
    total = 5.0
    
    # 产品名称 (20%)
    if result.product_name and result.product_name != "需要手动填写":
        score += 1.0
    
    # 品牌名称 (20%)
    if result.brand and result.brand != "需要手动填写":
        score += 1.0
    
    # 成分信息 (30%)
    if result.ingredients and len(result.ingredients) > 0:
        score += 1.5
        # 如果有含量信息，额外加分
        if any(ing.amount for ing in result.ingredients):
            score += 0.5
    
    # 功效描述 (15%)
    if result.benefits and len(result.benefits) > 5:
        score += 0.75
    
    # SKU信息 (15%)
    if result.sku:
        score += 0.75
    
    return min(score / total, 1.0)