"""
Phase 2: 优化提取API端点 (移除标准化版本)
实现 /api/v2/extract-optimized 接口
"""

import time
import logging
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional

from app.models.product import ExtractionRequest
from app.langchain.optimized_extraction_chain import get_optimized_extraction_chain

logger = logging.getLogger(__name__)
router = APIRouter()

# 响应模型
class OptimizedExtractionResponse(BaseModel):
    success: bool
    data: Optional[dict] = None
    metadata: Optional[dict] = None
    error: Optional[str] = None

@router.post("/extract-optimized", response_model=OptimizedExtractionResponse)
async def extract_optimized(request: ExtractionRequest):
    """
    Phase 2: 优化版信息提取接口 (纯提取，无标准化)
    
    流程:
    1. 使用优化版提取链进行单次LLM调用
    2. 返回纯提取结果，不进行标准化处理
    3. 保持高性能的同时确保提取准确性
    """
    start_time = time.time()
    
    try:
        # 输入验证
        if not request.text or len(request.text.strip()) < 5:
            return OptimizedExtractionResponse(
                success=False,
                error="请输入至少5个字符的产品描述文本"
            )
        
        logger.info(f"Starting optimized extraction for text length: {len(request.text)}")
        
        # 获取优化版提取链
        extraction_chain = get_optimized_extraction_chain()
        
        # 执行提取
        result = await extraction_chain.extract_optimized(request.text)
        
        # 计算总处理时间
        total_processing_time = time.time() - start_time
        result["metadata"]["total_processing_time"] = total_processing_time
        
        logger.info(f"Optimized extraction completed in {total_processing_time:.2f}s")
        
        return OptimizedExtractionResponse(
            success=True,
            data=result["data"],
            metadata=result["metadata"]
        )
        
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Optimized extraction error after {processing_time:.2f}s: {e}")
        return OptimizedExtractionResponse(
            success=False,
            error=f"提取失败: {str(e)}"
        )