from fastapi import APIRouter, HTTPException
import logging
from app.models.product import ProductSaveRequest, ProductSaveResponse, EnhancedProductSaveRequest
from app.db.neo4j import neo4j_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/products", response_model=ProductSaveResponse)
async def save_product(request: ProductSaveRequest):
    """保存产品信息"""
    try:
        logger.info(f"Saving product: {request.product_name}")
        
        # 验证数据
        if not request.ingredients or len(request.ingredients) == 0:
            return ProductSaveResponse(
                success=False,
                error="至少需要一个成分信息"
            )
        
        # 验证成分名称
        for ingredient in request.ingredients:
            if not ingredient.name or len(ingredient.name.strip()) < 1:
                return ProductSaveResponse(
                    success=False,
                    error="成分名称不能为空"
                )
        
        # 准备保存数据
        product_data = {
            "product_name": request.product_name,
            "brand": request.brand,
            "brand_id": request.brand_id,
            "product_type": request.product_type,
            "sku": request.sku,
            "benefits": request.benefits,
            "ingredients": [
                {
                    "name": ing.name,
                    "amount": ing.amount,
                    "unit": ing.unit,
                    "ingredient_id": ing.ingredient_id
                }
                for ing in request.ingredients
            ]
        }
        
        # 保存到数据库
        product_id = await neo4j_service.save_product(product_data)
        
        logger.info(f"Product saved successfully with ID: {product_id}")
        
        return ProductSaveResponse(
            success=True,
            data={
                "product_id": product_id,
                "message": "产品保存成功"
            }
        )
        
    except Exception as e:
        logger.error(f"Product save error: {e}")
        return ProductSaveResponse(
            success=False,
            error=f"保存失败: {str(e)}"
        )


@router.get("/products/{product_id}")
async def get_product(product_id: str):
    """获取产品信息（预留接口）"""
    # TODO: 实现产品查询功能
    return {"message": "Product retrieval not implemented yet"}


@router.post("/products/enhanced", response_model=ProductSaveResponse)
async def save_product_enhanced(request: EnhancedProductSaveRequest):
    """增强版产品保存 - 支持实体链接"""
    try:
        logger.info(f"Enhanced saving product: {request.product.name}")
        logger.info(f"Entity linking info - Product: {request.product.entity_link}, Brand: {request.brand.entity_link}")
        
        # 验证数据
        if not request.ingredients or len(request.ingredients) == 0:
            return ProductSaveResponse(
                success=False,
                error="至少需要一个成分信息"
            )
        
        # 验证成分名称
        for ingredient in request.ingredients:
            if not ingredient.name or len(ingredient.name.strip()) < 1:
                return ProductSaveResponse(
                    success=False,
                    error="成分名称不能为空"
                )
        
        # 准备增强版保存数据
        enhanced_product_data = {
            "product_name": request.product.name,
            "product_entity_link": request.product.entity_link,
            "brand": request.brand.name,
            "brand_entity_link": request.brand.entity_link,
            "product_type": request.product_type,
            "sku": request.sku,
            "benefits": request.benefits,
            "ingredients": [
                {
                    "name": ing.name,
                    "entity_link": ing.entity_link,
                    "amount": ing.amount,
                    "unit": ing.unit
                }
                for ing in request.ingredients
            ]
        }
        
        # 使用增强版保存方法
        product_id = await neo4j_service.save_product_enhanced(enhanced_product_data)
        
        logger.info(f"Enhanced product saved successfully with ID: {product_id}")
        
        return ProductSaveResponse(
            success=True,
            data={
                "product_id": product_id,
                "message": "产品保存成功（使用实体链接）"
            }
        )
        
    except Exception as e:
        logger.error(f"Enhanced product save error: {e}")
        return ProductSaveResponse(
            success=False,
            error=f"增强版保存失败: {str(e)}"
        )


@router.get("/products")
async def list_products(skip: int = 0, limit: int = 20):
    """获取产品列表（预留接口）"""
    # TODO: 实现产品列表功能
    return {"message": "Product listing not implemented yet"}