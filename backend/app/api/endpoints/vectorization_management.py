"""
向量化2.0 - 任务管理API
提供向量化任务的监控、管理和控制功能
"""

from fastapi import APIRouter, HTTPException, Query, Path
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import logging

from app.services.vectorization_queue_service import (
    get_queue_service, 
    VectorizationStatus as QueueVectorizationStatus, 
    TaskPriority
)
from app.services.vectorization_monitor_service import get_monitor_service

logger = logging.getLogger(__name__)

router = APIRouter()

# ===== Pydantic Models =====

class VectorizationTaskDetail(BaseModel):
    """向量化任务详情"""
    task_id: str
    entity_id: str
    entity_type: str
    entity_name: str
    text: str
    status: str
    priority: int
    created_at: str
    updated_at: str
    scheduled_at: str
    attempts: int
    max_attempts: int
    error_message: Optional[str] = None
    retry_at: Optional[str] = None
    force_update: bool

class VectorizationStatus(BaseModel):
    """整体向量化状态"""
    total_tasks: int
    processing_count: int
    queue_size: int
    total_created: int
    total_completed: int
    total_failed: int
    last_cleanup: str
    status_counts: Dict[str, int]

class TaskListResponse(BaseModel):
    """任务列表响应"""
    success: bool
    data: List[VectorizationTaskDetail]
    total: int
    page: int
    size: int
    error: Optional[str] = None

class StatusResponse(BaseModel):
    """状态响应"""
    success: bool
    data: Optional[VectorizationStatus] = None
    error: Optional[str] = None

class BatchRetryRequest(BaseModel):
    """批量重试请求"""
    entity_type: Optional[str] = None
    max_attempts_exceeded: bool = True
    limit: int = 100

class BatchRetryResponse(BaseModel):
    """批量重试响应"""
    success: bool
    retried_count: int
    error: Optional[str] = None

class TaskOperationResponse(BaseModel):
    """任务操作响应"""
    success: bool
    message: str
    error: Optional[str] = None

# ===== API Endpoints =====

@router.get("/status", response_model=StatusResponse)
async def get_vectorization_status():
    """获取向量化整体状态概览"""
    try:
        queue_service = get_queue_service()
        stats = queue_service.get_stats()
        
        status = VectorizationStatus(
            total_tasks=stats["total_tasks"],
            processing_count=stats["processing_count"],
            queue_size=stats["queue_size"],
            total_created=stats["total_created"],
            total_completed=stats["total_completed"],
            total_failed=stats["total_failed"],
            last_cleanup=stats["last_cleanup"].isoformat(),
            status_counts=stats["status_counts"]
        )
        
        return StatusResponse(success=True, data=status)
        
    except Exception as e:
        logger.error(f"获取向量化状态失败: {e}")
        return StatusResponse(success=False, error=str(e))

@router.get("/tasks", response_model=TaskListResponse)
async def get_vectorization_tasks(
    status: Optional[str] = Query(None, description="按状态过滤: PENDING/PROCESSING/COMPLETED/FAILED"),
    entity_type: Optional[str] = Query(None, description="按实体类型过滤: brand/ingredient/product"),
    page: int = Query(1, description="页码，从1开始", ge=1),
    size: int = Query(20, description="每页数量", ge=1, le=100)
):
    """获取向量化任务列表"""
    try:
        queue_service = get_queue_service()
        
        # 转换状态参数
        status_filter = None
        if status:
            try:
                status_filter = QueueVectorizationStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效状态值: {status}")
        
        # 获取任务列表
        all_tasks = queue_service.get_tasks(
            status=status_filter,
            entity_type=entity_type,
            limit=size * page  # 获取足够的数据进行分页
        )
        
        # 手动分页
        total = len(all_tasks)
        start_idx = (page - 1) * size
        end_idx = start_idx + size
        tasks_page = all_tasks[start_idx:end_idx]
        
        # 转换为响应格式
        task_details = []
        for task in tasks_page:
            task_dict = task.to_dict()
            task_detail = VectorizationTaskDetail(**task_dict)
            task_details.append(task_detail)
        
        return TaskListResponse(
            success=True,
            data=task_details,
            total=total,
            page=page,
            size=size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        return TaskListResponse(
            success=False,
            data=[],
            total=0,
            page=page,
            size=size,
            error=str(e)
        )

@router.post("/retry/{task_id}", response_model=TaskOperationResponse)
async def retry_task(
    task_id: str = Path(..., description="任务ID")
):
    """重试单个失败的任务"""
    try:
        queue_service = get_queue_service()
        
        # 检查任务是否存在
        task = queue_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 重试任务
        success = queue_service.retry_task(task_id)
        
        if success:
            return TaskOperationResponse(
                success=True,
                message=f"任务 {task_id} 已重新排队"
            )
        else:
            return TaskOperationResponse(
                success=False,
                message="重试失败",
                error="任务可能不在失败状态或其他错误"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务失败: {e}")
        return TaskOperationResponse(
            success=False,
            message="重试任务时发生错误",
            error=str(e)
        )

@router.post("/batch-retry", response_model=BatchRetryResponse)
async def batch_retry_tasks(request: BatchRetryRequest):
    """批量重试失败的任务"""
    try:
        queue_service = get_queue_service()
        
        # 获取失败任务
        failed_tasks = queue_service.get_tasks(
            status=QueueVectorizationStatus.FAILED,
            entity_type=request.entity_type,
            limit=request.limit
        )
        
        retried_count = 0
        for task in failed_tasks:
            # 如果设置了只重试超过最大重试次数的任务
            if request.max_attempts_exceeded and task.attempts < task.max_attempts:
                continue
            
            success = queue_service.retry_task(task.task_id)
            if success:
                retried_count += 1
        
        return BatchRetryResponse(
            success=True,
            retried_count=retried_count
        )
        
    except Exception as e:
        logger.error(f"批量重试任务失败: {e}")
        return BatchRetryResponse(
            success=False,
            retried_count=0,
            error=str(e)
        )

@router.delete("/task/{task_id}", response_model=TaskOperationResponse)
async def delete_task(
    task_id: str = Path(..., description="任务ID")
):
    """删除任务记录"""
    try:
        queue_service = get_queue_service()
        
        # 检查任务是否存在
        task = queue_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 取消任务
        success = queue_service.cancel_task(task_id)
        
        if success:
            return TaskOperationResponse(
                success=True,
                message=f"任务 {task_id} 已删除"
            )
        else:
            return TaskOperationResponse(
                success=False,
                message="删除失败",
                error="无法删除正在处理中的任务"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        return TaskOperationResponse(
            success=False,
            message="删除任务时发生错误",
            error=str(e)
        )

@router.delete("/clear-failed", response_model=BatchRetryResponse)
async def clear_failed_tasks(
    entity_type: Optional[str] = Query(None, description="按实体类型过滤"),
    max_attempts_exceeded: bool = Query(True, description="只清理超过最大重试次数的任务")
):
    """清理失败任务"""
    try:
        queue_service = get_queue_service()
        
        # 获取失败任务
        failed_tasks = queue_service.get_tasks(
            status=QueueVectorizationStatus.FAILED,
            entity_type=entity_type,
            limit=1000  # 一次最多清理1000个
        )
        
        cleared_count = 0
        for task in failed_tasks:
            # 如果设置了只清理超过最大重试次数的任务
            if max_attempts_exceeded and task.attempts < task.max_attempts:
                continue
            
            success = queue_service.cancel_task(task.task_id)
            if success:
                cleared_count += 1
        
        return BatchRetryResponse(
            success=True,
            retried_count=cleared_count
        )
        
    except Exception as e:
        logger.error(f"清理失败任务失败: {e}")
        return BatchRetryResponse(
            success=False,
            retried_count=0,
            error=str(e)
        )

@router.get("/health")
async def vectorization_health_check():
    """向量化服务健康检查"""
    try:
        queue_service = get_queue_service()
        stats = queue_service.get_stats()
        
        # 简单的健康检查逻辑
        is_healthy = True
        health_issues = []
        
        # 检查队列积压
        if stats["queue_size"] > 100:
            health_issues.append(f"队列积压严重: {stats['queue_size']} 个任务")
            
        # 检查失败率
        if stats["total_created"] > 0:
            failure_rate = stats["total_failed"] / stats["total_created"]
            if failure_rate > 0.1:  # 失败率超过10%
                health_issues.append(f"失败率过高: {failure_rate:.1%}")
        
        # 检查处理中任务是否过多
        if stats["processing_count"] > 10:
            health_issues.append(f"处理中任务过多: {stats['processing_count']}")
        
        if health_issues:
            is_healthy = False
        
        return {
            "status": "healthy" if is_healthy else "warning",
            "queue_service": "running",
            "stats": stats,
            "issues": health_issues
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "queue_service": "error",
            "error": str(e)
        }

# ===== Dashboard API Endpoints =====

class DashboardOverviewResponse(BaseModel):
    """Dashboard概览响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class CoverageStatsResponse(BaseModel):
    """覆盖率统计响应"""
    success: bool
    data: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None

class HealthStatusResponse(BaseModel):
    """健康状态响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

class MonitoringReportResponse(BaseModel):
    """监控报告响应"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

@router.get("/dashboard/overview", response_model=DashboardOverviewResponse)
async def get_dashboard_overview():
    """获取Dashboard概览数据"""
    try:
        monitor_service = get_monitor_service()
        queue_service = get_queue_service()
        
        # 获取基础统计
        queue_stats = queue_service.get_stats()
        coverage_stats = await monitor_service.get_vectorization_coverage()
        health_status = await monitor_service.check_health()
        performance_metrics = await monitor_service.get_performance_metrics()
        
        # 计算概览指标
        total_entities = sum(c.total_entities for c in coverage_stats)
        vectorized_entities = sum(c.vectorized_entities for c in coverage_stats)
        overall_coverage = (vectorized_entities / total_entities * 100) if total_entities > 0 else 0
        
        # 构建概览数据
        overview_data = {
            "summary": {
                "total_entities": total_entities,
                "vectorized_entities": vectorized_entities,
                "overall_coverage": round(overall_coverage, 1),
                "queue_size": queue_stats["queue_size"],
                "processing_count": queue_stats["processing_count"],
                "total_failed": queue_stats["total_failed"],
                "success_rate": performance_metrics.success_rate,
                "overall_health": health_status.overall_status
            },
            "coverage_by_type": [
                {
                    "entity_type": c.entity_type,
                    "total": c.total_entities,
                    "vectorized": c.vectorized_entities,
                    "pending": c.pending_entities,
                    "failed": c.failed_entities,
                    "coverage_percentage": c.coverage_percentage
                }
                for c in coverage_stats
            ],
            "recent_activity": {
                "total_completed": queue_stats["total_completed"],
                "total_failed": queue_stats["total_failed"],
                "last_cleanup": queue_stats["last_cleanup"].isoformat()
            },
            "health_alerts": health_status.issues[:3] if health_status.issues else []
        }
        
        return DashboardOverviewResponse(success=True, data=overview_data)
        
    except Exception as e:
        logger.error(f"获取Dashboard概览失败: {e}")
        return DashboardOverviewResponse(success=False, error=str(e))

@router.get("/dashboard/coverage", response_model=CoverageStatsResponse)
async def get_coverage_stats():
    """获取向量化覆盖率详细统计"""
    try:
        monitor_service = get_monitor_service()
        coverage_stats = await monitor_service.get_vectorization_coverage()
        
        coverage_data = [c.to_dict() for c in coverage_stats]
        
        return CoverageStatsResponse(success=True, data=coverage_data)
        
    except Exception as e:
        logger.error(f"获取覆盖率统计失败: {e}")
        return CoverageStatsResponse(success=False, error=str(e))

@router.get("/dashboard/health", response_model=HealthStatusResponse)
async def get_health_status():
    """获取系统健康状态详情"""
    try:
        monitor_service = get_monitor_service()
        health_status = await monitor_service.check_health()
        performance_metrics = await monitor_service.get_performance_metrics()
        
        health_data = {
            "overall_status": health_status.overall_status,
            "issues": health_status.issues,
            "recommendations": health_status.recommendations,
            "last_check": health_status.last_check.isoformat(),
            "performance": {
                "avg_vectorization_time": performance_metrics.avg_vectorization_time,
                "queue_throughput": performance_metrics.queue_throughput,
                "success_rate": performance_metrics.success_rate,
                "active_workers": performance_metrics.active_workers,
                "queue_backlog": performance_metrics.queue_backlog
            }
        }
        
        return HealthStatusResponse(success=True, data=health_data)
        
    except Exception as e:
        logger.error(f"获取健康状态失败: {e}")
        return HealthStatusResponse(success=False, error=str(e))

@router.get("/dashboard/report", response_model=MonitoringReportResponse)
async def get_monitoring_report():
    """获取完整监控报告"""
    try:
        monitor_service = get_monitor_service()
        report = await monitor_service.generate_report()
        
        return MonitoringReportResponse(success=True, data=report)
        
    except Exception as e:
        logger.error(f"生成监控报告失败: {e}")
        return MonitoringReportResponse(success=False, error=str(e))

@router.get("/dashboard/failures")
async def get_failure_analysis():
    """获取失败任务分析"""
    try:
        monitor_service = get_monitor_service()
        failure_analyses = await monitor_service.analyze_failures()
        
        return {
            "success": True,
            "data": [{
                "entity_type": analysis.entity_type,
                "total_failures": analysis.total_failures,
                "common_errors": analysis.common_errors,
                "retry_success_rate": analysis.retry_success_rate,
                "avg_failure_time": analysis.avg_failure_time
            } for analysis in failure_analyses]
        }
        
    except Exception as e:
        logger.error(f"获取失败分析失败: {e}")
        return {"success": False, "error": str(e)}