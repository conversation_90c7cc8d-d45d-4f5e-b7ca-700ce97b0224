"""
图数据可视化API端点
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional
import logging

from app.services.graph_visualization_service import get_graph_visualization_service
from app.models.graph_visualization import (
    GraphDataResponse,
    GraphStatsResponse,
    EntitySearchRequest,
    EntitySearchResponse,
    SubgraphRequest,
    SubgraphResponse,
    VisualizationConfigResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# 懒加载服务
_graph_service = None

def get_graph_service():
    """获取图可视化服务实例（懒加载）"""
    global _graph_service
    if _graph_service is None:
        _graph_service = get_graph_visualization_service()
    return _graph_service


@router.get("/overview", response_model=GraphDataResponse)
async def get_graph_overview(limit: int = Query(100, description="节点数量限制", ge=10, le=500)):
    """
    获取知识图谱概览
    """
    try:
        logger.info(f"获取图谱概览，限制 {limit} 个节点")
        
        graph_service = get_graph_service()
        graph_data = await graph_service.get_graph_overview(limit=limit)
        
        # 转换为API响应格式
        response_data = {
            "nodes": [
                {
                    "id": node.id,
                    "label": node.name,
                    "type": node.entity_type,
                    "properties": node.properties,
                    "color": graph_service.node_colors.get(node.entity_type, "#999"),
                    "size": graph_service.node_sizes.get(node.entity_type, 15)
                }
                for node in graph_data.nodes
            ],
            "edges": [
                {
                    "id": edge.id,
                    "source": edge.source,
                    "target": edge.target,
                    "label": edge.relationship,
                    "properties": edge.properties
                }
                for edge in graph_data.edges
            ],
            "stats": graph_data.stats
        }
        
        logger.info(f"图谱概览获取成功: {len(response_data['nodes'])} 个节点")
        
        return GraphDataResponse(
            success=True,
            data=response_data
        )
        
    except Exception as e:
        logger.error(f"获取图谱概览失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取图谱概览失败: {str(e)}"
        )


@router.post("/subgraph", response_model=SubgraphResponse)
async def get_entity_subgraph(request: SubgraphRequest):
    """
    获取以指定实体为中心的子图
    """
    try:
        logger.info(f"获取实体子图: {request.entity_id}, 深度: {request.depth}")
        
        # 输入验证
        if not request.entity_id:
            raise HTTPException(
                status_code=400,
                detail="实体ID不能为空"
            )
        
        if request.depth < 1 or request.depth > 5:
            raise HTTPException(
                status_code=400,
                detail="遍历深度必须在1-5之间"
            )
        
        if request.limit < 10 or request.limit > 200:
            raise HTTPException(
                status_code=400,
                detail="节点限制必须在10-200之间"
            )
        
        graph_service = get_graph_service()
        graph_data = await graph_service.get_entity_subgraph(
            entity_id=request.entity_id,
            depth=request.depth,
            limit=request.limit
        )
        
        # 转换为API响应格式
        response_data = {
            "nodes": [
                {
                    "id": node.id,
                    "label": node.name,
                    "type": node.entity_type,
                    "properties": node.properties,
                    "color": graph_service.node_colors.get(node.entity_type, "#999"),
                    "size": graph_service.node_sizes.get(node.entity_type, 15),
                    "isCenter": node.id == request.entity_id  # 标记中心节点
                }
                for node in graph_data.nodes
            ],
            "edges": [
                {
                    "id": edge.id,
                    "source": edge.source,
                    "target": edge.target,
                    "label": edge.relationship,
                    "properties": edge.properties
                }
                for edge in graph_data.edges
            ],
            "stats": graph_data.stats
        }
        
        logger.info(f"子图获取成功: {len(response_data['nodes'])} 个节点")
        
        return SubgraphResponse(
            success=True,
            data=response_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取子图失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取子图失败: {str(e)}"
        )


@router.get("/stats", response_model=GraphStatsResponse)
async def get_graph_statistics():
    """
    获取图统计信息
    """
    try:
        logger.info("获取图统计信息")
        
        graph_service = get_graph_service()
        stats = await graph_service.get_graph_statistics()
        
        stats_data = {
            "total_nodes": stats.total_nodes,
            "total_edges": stats.total_edges,
            "node_counts_by_type": stats.node_counts_by_type,
            "edge_counts_by_type": stats.edge_counts_by_type,
            "density": round(stats.density, 4),
            "components": stats.components
        }
        
        logger.info(f"图统计获取成功: {stats.total_nodes} 个节点, {stats.total_edges} 条边")
        
        return GraphStatsResponse(
            success=True,
            data=stats_data
        )
        
    except Exception as e:
        logger.error(f"获取图统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取图统计失败: {str(e)}"
        )


@router.post("/search", response_model=EntitySearchResponse)
async def search_entities(request: EntitySearchRequest):
    """
    搜索实体用于可视化
    """
    try:
        logger.info(f"搜索实体: '{request.query}', 类型: {request.entity_types}")
        
        # 输入验证
        if not request.query or len(request.query.strip()) < 1:
            raise HTTPException(
                status_code=400,
                detail="搜索查询不能为空"
            )
        
        if request.entity_types:
            valid_types = {"brand", "ingredient", "product"}
            invalid_types = set(request.entity_types) - valid_types
            if invalid_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的实体类型: {list(invalid_types)}"
                )
        
        if request.limit < 1 or request.limit > 100:
            raise HTTPException(
                status_code=400,
                detail="限制数量必须在1-100之间"
            )
        
        graph_service = get_graph_service()
        entities = await graph_service.search_entities_for_visualization(
            query=request.query.strip(),
            entity_types=request.entity_types,
            limit=request.limit
        )
        
        logger.info(f"实体搜索完成: 找到 {len(entities)} 个结果")
        
        return EntitySearchResponse(
            success=True,
            data={
                "entities": entities,
                "total": len(entities),
                "query": request.query.strip()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索实体失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"搜索失败: {str(e)}"
        )


@router.get("/config", response_model=VisualizationConfigResponse)
async def get_visualization_config():
    """
    获取可视化配置
    """
    try:
        graph_service = get_graph_service()
        config = graph_service.get_visualization_config()
        
        return VisualizationConfigResponse(
            success=True,
            data=config
        )
        
    except Exception as e:
        logger.error(f"获取可视化配置失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取配置失败: {str(e)}"
        )


@router.get("/health")
async def graph_service_health():
    """
    图可视化服务健康检查
    """
    try:
        # 测试服务可用性
        graph_service = get_graph_service()
        stats = await graph_service._get_basic_stats()
        
        return {
            "success": True,
            "data": {
                "service_status": "healthy",
                "database_connected": True,
                "total_nodes": stats.get("total_nodes", 0)
            }
        }
        
    except Exception as e:
        logger.error(f"图服务健康检查失败: {e}")
        return {
            "success": False,
            "data": {
                "service_status": "unhealthy",
                "database_connected": False,
                "error": str(e)
            }
        }