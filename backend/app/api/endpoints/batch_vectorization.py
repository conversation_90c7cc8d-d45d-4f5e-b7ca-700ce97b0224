"""
批量向量化API端点
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, Optional
import logging
import asyncio

from app.tools.batch_vectorization import BatchVectorizationTool
from app.models.batch_vectorization import (
    BatchVectorizationRequest,
    BatchVectorizationResponse,
    VectorizationStatsResponse,
    VectorizationTaskStatus,
    TaskStatusResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# 任务状态存储（生产环境中应使用Redis等）
_task_status: Dict[str, Dict[str, Any]] = {}

# 全局工具实例
_vectorization_tool = None

async def get_vectorization_tool():
    """获取向量化工具实例"""
    global _vectorization_tool
    if _vectorization_tool is None:
        _vectorization_tool = BatchVectorizationTool()
        await _vectorization_tool.initialize()
    return _vectorization_tool


@router.get("/stats", response_model=VectorizationStatsResponse)
async def get_vectorization_stats():
    """
    获取向量化统计信息
    """
    try:
        logger.info("获取向量化统计信息")
        
        tool = await get_vectorization_tool()
        stats = await tool.get_vectorization_stats()
        
        if "error" in stats:
            raise HTTPException(
                status_code=500,
                detail=f"获取统计信息失败: {stats['error']}"
            )
        
        logger.info("向量化统计信息获取成功")
        
        return VectorizationStatsResponse(
            success=True,
            data=stats
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取向量化统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取统计失败: {str(e)}"
        )


@router.post("/start", response_model=BatchVectorizationResponse)
async def start_batch_vectorization(
    request: BatchVectorizationRequest,
    background_tasks: BackgroundTasks
):
    """
    启动批量向量化任务
    """
    try:
        logger.info(f"启动批量向量化: {request.entity_types}, force_update={request.force_update}")
        
        # 输入验证
        valid_types = {"brand", "ingredient", "product"}
        if request.entity_types:
            invalid_types = set(request.entity_types) - valid_types
            if invalid_types:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的实体类型: {list(invalid_types)}"
                )
        
        if request.limit_per_type and request.limit_per_type <= 0:
            raise HTTPException(
                status_code=400,
                detail="limit_per_type 必须大于 0"
            )
        
        # 生成任务ID
        import uuid
        task_id = str(uuid.uuid4())
        
        # 初始化任务状态
        _task_status[task_id] = {
            "status": VectorizationTaskStatus.RUNNING,
            "progress": 0,
            "message": "任务启动中...",
            "start_time": None,
            "end_time": None,
            "result": None,
            "error": None
        }
        
        # 启动后台任务
        if not request.entity_types or len(request.entity_types) == 0:
            # 处理所有类型
            background_tasks.add_task(
                _run_vectorization_all_task,
                task_id,
                request.force_update,
                request.limit_per_type
            )
        else:
            # 处理指定类型
            background_tasks.add_task(
                _run_vectorization_types_task,
                task_id,
                request.entity_types,
                request.force_update,
                request.limit_per_type
            )
        
        logger.info(f"批量向量化任务启动: {task_id}")
        
        return BatchVectorizationResponse(
            success=True,
            data={
                "task_id": task_id,
                "message": "批量向量化任务已启动",
                "status": VectorizationTaskStatus.RUNNING
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动批量向量化失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"启动任务失败: {str(e)}"
        )


@router.get("/task/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str):
    """
    获取任务状态
    """
    try:
        if task_id not in _task_status:
            raise HTTPException(
                status_code=404,
                detail="任务不存在"
            )
        
        status = _task_status[task_id]
        
        return TaskStatusResponse(
            success=True,
            data={
                "task_id": task_id,
                "status": status["status"],
                "progress": status["progress"],
                "message": status["message"],
                "start_time": status["start_time"],
                "end_time": status["end_time"],
                "result": status["result"],
                "error": status["error"]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务状态失败: {str(e)}"
        )


@router.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """
    删除任务状态记录
    """
    try:
        if task_id not in _task_status:
            raise HTTPException(
                status_code=404,
                detail="任务不存在"
            )
        
        # 检查任务是否还在运行
        if _task_status[task_id]["status"] == VectorizationTaskStatus.RUNNING:
            raise HTTPException(
                status_code=400,
                detail="无法删除正在运行的任务"
            )
        
        del _task_status[task_id]
        
        return {
            "success": True,
            "message": "任务记录已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除任务失败: {str(e)}"
        )


@router.get("/tasks")
async def list_tasks():
    """
    列出所有任务
    """
    try:
        tasks = []
        for task_id, status in _task_status.items():
            tasks.append({
                "task_id": task_id,
                "status": status["status"],
                "progress": status["progress"],
                "message": status["message"],
                "start_time": status["start_time"],
                "end_time": status["end_time"]
            })
        
        # 按开始时间排序
        tasks.sort(key=lambda x: x["start_time"] or "", reverse=True)
        
        return {
            "success": True,
            "data": {
                "tasks": tasks,
                "total": len(tasks)
            }
        }
        
    except Exception as e:
        logger.error(f"列出任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"列出任务失败: {str(e)}"
        )


async def _run_vectorization_all_task(
    task_id: str,
    force_update: bool,
    limit_per_type: Optional[int]
):
    """运行批量向量化所有实体的后台任务"""
    try:
        from datetime import datetime
        
        # 更新任务状态
        _task_status[task_id].update({
            "status": VectorizationTaskStatus.RUNNING,
            "message": "正在向量化所有实体类型...",
            "start_time": datetime.now().isoformat(),
            "progress": 0
        })
        
        tool = await get_vectorization_tool()
        
        # 执行向量化
        result = await tool.vectorize_all_entities(
            force_update=force_update,
            limit_per_type=limit_per_type
        )
        
        # 任务完成
        _task_status[task_id].update({
            "status": VectorizationTaskStatus.COMPLETED,
            "message": "批量向量化完成",
            "end_time": datetime.now().isoformat(),
            "progress": 100,
            "result": result
        })
        
    except Exception as e:
        logger.error(f"批量向量化任务失败: {e}")
        _task_status[task_id].update({
            "status": VectorizationTaskStatus.FAILED,
            "message": f"任务失败: {str(e)}",
            "end_time": datetime.now().isoformat(),
            "error": str(e)
        })


async def _run_vectorization_types_task(
    task_id: str,
    entity_types: list,
    force_update: bool,
    limit_per_type: Optional[int]
):
    """运行指定类型的批量向量化后台任务"""
    try:
        from datetime import datetime
        
        # 更新任务状态
        _task_status[task_id].update({
            "status": VectorizationTaskStatus.RUNNING,
            "message": f"正在向量化 {', '.join(entity_types)} 实体...",
            "start_time": datetime.now().isoformat(),
            "progress": 0
        })
        
        tool = await get_vectorization_tool()
        
        results = {}
        total_success = 0
        total_processed = 0
        
        for i, entity_type in enumerate(entity_types):
            # 更新进度
            progress = int((i / len(entity_types)) * 100)
            _task_status[task_id].update({
                "message": f"正在向量化 {entity_type} 实体...",
                "progress": progress
            })
            
            # 处理当前类型
            result = await tool.vectorize_entity_type(
                entity_type=entity_type,
                force_update=force_update,
                limit=limit_per_type
            )
            
            results[entity_type] = result
            total_success += result["success_count"]
            total_processed += result["total_found"]
            
            # 短暂休息
            await asyncio.sleep(1)
        
        # 汇总结果
        summary = {
            "total_entities_processed": total_processed,
            "total_entities_success": total_success,
            "total_entities_failed": total_processed - total_success,
            "success_rate": round(total_success / total_processed * 100, 2) if total_processed > 0 else 0,
            "results_by_type": results
        }
        
        # 任务完成
        _task_status[task_id].update({
            "status": VectorizationTaskStatus.COMPLETED,
            "message": "批量向量化完成",
            "end_time": datetime.now().isoformat(),
            "progress": 100,
            "result": summary
        })
        
    except Exception as e:
        logger.error(f"批量向量化任务失败: {e}")
        _task_status[task_id].update({
            "status": VectorizationTaskStatus.FAILED,
            "message": f"任务失败: {str(e)}",
            "end_time": datetime.now().isoformat(),
            "error": str(e)
        })