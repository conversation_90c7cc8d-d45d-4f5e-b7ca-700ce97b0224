from fastapi import Depends, HTTPException, status
from app.db.neo4j import neo4j_service
import logging

logger = logging.getLogger(__name__)


async def get_neo4j_service():
    """获取Neo4j服务依赖"""
    if not neo4j_service.driver:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database service unavailable"
        )
    return neo4j_service


async def check_service_health():
    """检查服务健康状态"""
    try:
        # 检查数据库连接
        if not neo4j_service.driver:
            return {"neo4j": "disconnected"}
        
        # 执行简单查询测试连接
        async with neo4j_service.driver.session() as session:
            result = await session.run("RETURN 1 as test")
            await result.consume()
        
        return {"neo4j": "connected"}
    
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"neo4j": "error", "message": str(e)}