"""
向量化2.0 - 数据迁移脚本
为现有实体添加向量化状态字段，检查向量完整性
"""

import asyncio
import logging
import sys
import argparse
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from app.db.neo4j import neo4j_service
from app.core.config import settings
from app.services.vectorization_queue_service import VectorizationStatus

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

class VectorizationMigration:
    """向量化迁移工具"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        
    async def run_migration(self, dry_run: bool = False):
        """运行迁移"""
        try:
            logger.info("=== 开始向量化2.0数据迁移 ===")
            
            # 连接数据库
            await self.neo4j.connect()
            
            # 1. 添加向量化状态字段
            await self._add_vectorization_fields(dry_run)
            
            # 2. 检查现有向量完整性
            stats = await self._check_vector_integrity()
            
            # 3. 标记需要重新向量化的实体
            await self._mark_entities_for_vectorization(dry_run)
            
            # 4. 显示迁移统计
            await self._show_migration_stats(stats)
            
            logger.info("=== 向量化2.0数据迁移完成 ===")
            
        except Exception as e:
            logger.error(f"迁移失败: {e}")
            raise
        finally:
            await self.neo4j.close()
    
    async def _add_vectorization_fields(self, dry_run: bool = False):
        """为所有实体节点添加向量化状态字段"""
        logger.info("步骤1: 添加向量化状态字段")
        
        entity_types = ["Brand", "Ingredient", "Product"]
        
        for entity_type in entity_types:
            label = f"{settings.NEO4J_LABEL_PREFIX}_{entity_type}"
            
            # 检查需要更新的节点数量
            count_query = f"""
            MATCH (n:{label})
            WHERE n.vectorization_status IS NULL
            RETURN count(n) as count
            """
            
            result = await self.neo4j.run_query(count_query)
            count = result[0]["count"] if result else 0
            
            if count == 0:
                logger.info(f"  {entity_type}: 无需更新 (已有状态字段)")
                continue
            
            logger.info(f"  {entity_type}: {count} 个节点需要添加状态字段")
            
            if not dry_run:
                # 添加向量化状态字段
                update_query = f"""
                MATCH (n:{label})
                WHERE n.vectorization_status IS NULL
                SET n.vectorization_status = CASE
                    WHEN n.embedding IS NOT NULL AND n.embedding_dim IS NOT NULL 
                    THEN 'COMPLETED' 
                    ELSE 'PENDING' 
                END,
                n.vectorization_attempts = 0,
                n.vectorization_last_attempt = null,
                n.vectorization_error = null,
                n.vectorization_completed_at = CASE
                    WHEN n.embedding IS NOT NULL 
                    THEN n.embedding_created_at
                    ELSE null
                END,
                n.updated_at = datetime()
                RETURN count(n) as updated
                """
                
                result = await self.neo4j.run_query(update_query)
                updated = result[0]["updated"] if result else 0
                
                logger.info(f"  {entity_type}: 已更新 {updated} 个节点")
            else:
                logger.info(f"  {entity_type}: [DRY RUN] 将更新 {count} 个节点")
    
    async def _check_vector_integrity(self) -> Dict[str, Any]:
        """检查现有向量的完整性"""
        logger.info("步骤2: 检查向量完整性")
        
        stats = {
            "total_entities": 0,
            "with_vectors": 0,
            "without_vectors": 0,
            "corrupted_vectors": 0,
            "by_type": {}
        }
        
        entity_types = ["Brand", "Ingredient", "Product"]
        
        for entity_type in entity_types:
            label = f"{settings.NEO4J_LABEL_PREFIX}_{entity_type}"
            
            # 统计查询
            stats_query = f"""
            MATCH (n:{label})
            RETURN 
                count(n) as total,
                count(n.embedding) as with_embedding,
                count(CASE WHEN n.embedding IS NULL OR n.embedding_dim IS NULL THEN 1 END) as without_embedding,
                count(CASE WHEN n.embedding IS NOT NULL AND 
                                (n.embedding_dim IS NULL OR 
                                 n.embedding_dim = 0 OR 
                                 size(n.embedding) <> n.embedding_dim) 
                           THEN 1 END) as corrupted
            """
            
            result = await self.neo4j.run_query(stats_query)
            if result:
                record = result[0]
                type_stats = {
                    "total": record["total"],
                    "with_vectors": record["with_embedding"],
                    "without_vectors": record["without_embedding"],
                    "corrupted_vectors": record["corrupted"]
                }
                
                stats["by_type"][entity_type] = type_stats
                stats["total_entities"] += type_stats["total"]
                stats["with_vectors"] += type_stats["with_vectors"]
                stats["without_vectors"] += type_stats["without_vectors"]
                stats["corrupted_vectors"] += type_stats["corrupted_vectors"]
                
                logger.info(f"  {entity_type}: {type_stats['total']} 总数, "
                          f"{type_stats['with_vectors']} 有向量, "
                          f"{type_stats['without_vectors']} 无向量, "
                          f"{type_stats['corrupted_vectors']} 损坏")
        
        return stats
    
    async def _mark_entities_for_vectorization(self, dry_run: bool = False):
        """标记需要重新向量化的实体"""
        logger.info("步骤3: 标记需要向量化的实体")
        
        entity_types = ["Brand", "Ingredient", "Product"]
        
        for entity_type in entity_types:
            label = f"{settings.NEO4J_LABEL_PREFIX}_{entity_type}"
            
            # 查找需要向量化的实体
            query = f"""
            MATCH (n:{label})
            WHERE n.vectorization_status = 'PENDING' OR
                  (n.embedding IS NULL OR n.embedding_dim IS NULL OR
                   n.embedding_dim = 0 OR size(n.embedding) <> n.embedding_dim)
            RETURN n.uuid as id, n.name as name
            LIMIT 1000
            """
            
            result = await self.neo4j.run_query(query)
            
            if not result:
                logger.info(f"  {entity_type}: 无需向量化的实体")
                continue
            
            logger.info(f"  {entity_type}: 找到 {len(result)} 个需要向量化的实体")
            
            if not dry_run:
                # 更新状态为 PENDING
                for record in result:
                    entity_id = record["id"]
                    entity_name = record["name"]
                    
                    update_query = f"""
                    MATCH (n:{label} {{uuid: $entity_id}})
                    SET n.vectorization_status = 'PENDING',
                        n.vectorization_attempts = 0,
                        n.vectorization_error = null,
                        n.updated_at = datetime()
                    """
                    
                    await self.neo4j.run_query(update_query, {"entity_id": entity_id})
                
                logger.info(f"  {entity_type}: 已标记 {len(result)} 个实体为待向量化")
            else:
                logger.info(f"  {entity_type}: [DRY RUN] 将标记 {len(result)} 个实体")
    
    async def _show_migration_stats(self, stats: Dict[str, Any]):
        """显示迁移统计信息"""
        logger.info("步骤4: 迁移统计")
        logger.info("=" * 50)
        logger.info(f"总实体数量: {stats['total_entities']}")
        logger.info(f"已有向量: {stats['with_vectors']}")
        logger.info(f"缺失向量: {stats['without_vectors']}")
        logger.info(f"损坏向量: {stats['corrupted_vectors']}")
        logger.info("")
        
        for entity_type, type_stats in stats["by_type"].items():
            logger.info(f"{entity_type}:")
            logger.info(f"  总数: {type_stats['total']}")
            logger.info(f"  有向量: {type_stats['with_vectors']}")
            logger.info(f"  无向量: {type_stats['without_vectors']}")
            logger.info(f"  损坏: {type_stats['corrupted_vectors']}")
        
        logger.info("=" * 50)
        
        # 计算向量化覆盖率
        if stats['total_entities'] > 0:
            coverage = (stats['with_vectors'] - stats['corrupted_vectors']) / stats['total_entities'] * 100
            logger.info(f"向量化覆盖率: {coverage:.1f}%")
        
        if stats['without_vectors'] > 0 or stats['corrupted_vectors'] > 0:
            need_vectorization = stats['without_vectors'] + stats['corrupted_vectors']
            logger.info(f"需要向量化的实体: {need_vectorization}")
            logger.info("建议运行批量向量化任务来完成向量化")
    
    async def create_vectorization_indexes(self, dry_run: bool = False):
        """创建向量化相关的索引"""
        logger.info("创建向量化索引")
        
        indexes = [
            # 向量化状态索引
            ("KGMS_Brand", "vectorization_status"),
            ("KGMS_Ingredient", "vectorization_status"),
            ("KGMS_Product", "vectorization_status"),
            
            # 向量化时间索引
            ("KGMS_Brand", "vectorization_completed_at"),
            ("KGMS_Ingredient", "vectorization_completed_at"),
            ("KGMS_Product", "vectorization_completed_at"),
        ]
        
        for label, property_name in indexes:
            if not dry_run:
                try:
                    index_query = f"CREATE INDEX IF NOT EXISTS FOR (n:{label}) ON (n.{property_name})"
                    await self.neo4j.run_query(index_query)
                    logger.info(f"  创建索引: {label}.{property_name}")
                except Exception as e:
                    logger.warning(f"  索引创建失败 {label}.{property_name}: {e}")
            else:
                logger.info(f"  [DRY RUN] 将创建索引: {label}.{property_name}")
    
    async def rollback_migration(self):
        """回滚迁移（删除向量化状态字段）"""
        logger.info("回滚向量化状态字段")
        
        entity_types = ["Brand", "Ingredient", "Product"]
        
        for entity_type in entity_types:
            label = f"{settings.NEO4J_LABEL_PREFIX}_{entity_type}"
            
            rollback_query = f"""
            MATCH (n:{label})
            REMOVE n.vectorization_status,
                   n.vectorization_attempts,
                   n.vectorization_last_attempt,
                   n.vectorization_error,
                   n.vectorization_completed_at
            RETURN count(n) as updated
            """
            
            result = await self.neo4j.run_query(rollback_query)
            updated = result[0]["updated"] if result else 0
            
            logger.info(f"  {entity_type}: 回滚 {updated} 个节点")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="向量化2.0数据迁移工具")
    parser.add_argument("--dry-run", action="store_true", help="预演模式，不实际修改数据")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")
    parser.add_argument("--create-indexes", action="store_true", help="创建索引")
    
    args = parser.parse_args()
    
    migration = VectorizationMigration()
    
    try:
        if args.rollback:
            await migration.rollback_migration()
        elif args.create_indexes:
            await migration.create_vectorization_indexes(dry_run=args.dry_run)
        else:
            await migration.run_migration(dry_run=args.dry_run)
            
            if not args.dry_run:
                await migration.create_vectorization_indexes()
    
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())