#!/usr/bin/env python3
"""
批量向量化工具
为现有数据库中的实体生成向量
"""

import asyncio
import logging
import sys
import os
from typing import List, Dict, Any, Tuple
from datetime import datetime
import argparse

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.neo4j import neo4j_service
from app.services.vector_storage_service import get_vector_storage_service
from app.core.config import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchVectorizationTool:
    """批量向量化工具类"""
    
    def __init__(self):
        self.neo4j = neo4j_service
        self.vector_service = get_vector_storage_service()
        self.batch_size = 50  # 批处理大小
        
    async def initialize(self):
        """初始化连接"""
        try:
            await self.neo4j.connect()
            await self.neo4j.init_database()
            logger.info("数据库连接初始化完成")
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭连接"""
        try:
            await self.neo4j.close()
            await self.vector_service.close()
            logger.info("连接关闭完成")
        except Exception as e:
            logger.error(f"关闭连接失败: {e}")
    
    async def get_entities_without_vectors(self, entity_type: str) -> List[Dict[str, Any]]:
        """获取没有向量的实体列表"""
        try:
            entities = await self.vector_service.get_entities_without_vectors(entity_type)
            logger.info(f"发现 {len(entities)} 个没有向量的 {entity_type} 实体")
            return entities
        except Exception as e:
            logger.error(f"获取无向量实体失败: {e}")
            return []
    
    async def vectorize_entities(
        self, 
        entities: List[Dict[str, Any]], 
        force_update: bool = False
    ) -> Tuple[int, int]:
        """
        批量向量化实体
        
        Args:
            entities: 实体列表
            force_update: 是否强制更新已有向量
            
        Returns:
            Tuple[成功数量, 总数量]
        """
        if not entities:
            return 0, 0
        
        try:
            logger.info(f"开始批量向量化 {len(entities)} 个实体...")
            
            # 分批处理
            total_success = 0
            total_count = len(entities)
            
            for i in range(0, total_count, self.batch_size):
                batch = entities[i:i + self.batch_size]
                logger.info(f"处理批次 {i//self.batch_size + 1}/{(total_count + self.batch_size - 1)//self.batch_size}: {len(batch)} 个实体")
                
                # 使用向量服务的批量处理方法
                success_count, batch_count = await self.vector_service.batch_store_vectors(
                    entities=batch,
                    force_update=force_update
                )
                
                total_success += success_count
                logger.info(f"批次完成: {success_count}/{batch_count} 成功")
                
                # 避免过快请求API
                if i + self.batch_size < total_count:
                    await asyncio.sleep(1)
            
            logger.info(f"批量向量化完成: {total_success}/{total_count} 成功")
            return total_success, total_count
            
        except Exception as e:
            logger.error(f"批量向量化失败: {e}")
            return 0, len(entities)
    
    async def vectorize_entity_type(
        self, 
        entity_type: str, 
        force_update: bool = False,
        limit: int = None
    ) -> Dict[str, Any]:
        """
        向量化指定类型的所有实体
        
        Args:
            entity_type: 实体类型 (brand/ingredient/product)
            force_update: 是否强制更新已有向量
            limit: 限制处理数量
            
        Returns:
            处理结果统计
        """
        try:
            start_time = datetime.now()
            logger.info(f"开始向量化 {entity_type} 实体...")
            
            # 获取需要向量化的实体
            if force_update:
                # 如果强制更新，获取所有实体
                entities = await self._get_all_entities(entity_type, limit)
            else:
                # 否则只获取没有向量的实体
                entities = await self.get_entities_without_vectors(entity_type)
                if limit and len(entities) > limit:
                    entities = entities[:limit]
            
            if not entities:
                logger.info(f"没有找到需要向量化的 {entity_type} 实体")
                return {
                    "entity_type": entity_type,
                    "total_found": 0,
                    "success_count": 0,
                    "failed_count": 0,
                    "duration_seconds": 0
                }
            
            # 执行向量化
            success_count, total_count = await self.vectorize_entities(entities, force_update)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            result = {
                "entity_type": entity_type,
                "total_found": total_count,
                "success_count": success_count,
                "failed_count": total_count - success_count,
                "duration_seconds": round(duration, 2),
                "average_time_per_entity": round(duration / total_count, 3) if total_count > 0 else 0
            }
            
            logger.info(f"{entity_type} 向量化完成: {success_count}/{total_count} 成功, 耗时 {duration:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"向量化 {entity_type} 失败: {e}")
            return {
                "entity_type": entity_type,
                "total_found": 0,
                "success_count": 0,
                "failed_count": 0,
                "duration_seconds": 0,
                "error": str(e)
            }
    
    async def _get_all_entities(self, entity_type: str, limit: int = None) -> List[Dict[str, Any]]:
        """获取所有指定类型的实体"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                raise ValueError(f"不支持的实体类型: {entity_type}")
            
            limit_clause = f"LIMIT {limit}" if limit else ""
            query = f"""
            MATCH (n:{label})
            RETURN n.uuid as id, n.name as name, '{entity_type}' as type, n.name as text
            ORDER BY n.created_at DESC
            {limit_clause}
            """
            
            result = await self.neo4j.run_query(query)
            
            entities = []
            for record in result:
                entities.append({
                    "id": record["id"],
                    "name": record["name"], 
                    "type": record["type"],
                    "text": record["text"]
                })
            
            logger.info(f"获取到 {len(entities)} 个 {entity_type} 实体")
            return entities
            
        except Exception as e:
            logger.error(f"获取 {entity_type} 实体失败: {e}")
            return []
    
    async def vectorize_all_entities(
        self, 
        force_update: bool = False,
        limit_per_type: int = None
    ) -> Dict[str, Any]:
        """
        向量化所有类型的实体
        
        Args:
            force_update: 是否强制更新已有向量
            limit_per_type: 每种类型的处理数量限制
            
        Returns:
            所有类型的处理结果
        """
        try:
            start_time = datetime.now()
            logger.info("开始批量向量化所有实体类型...")
            
            entity_types = ["brand", "ingredient", "product"]
            results = {}
            
            total_success = 0
            total_processed = 0
            
            for entity_type in entity_types:
                logger.info(f"\n=== 处理 {entity_type.upper()} 实体 ===")
                
                result = await self.vectorize_entity_type(
                    entity_type=entity_type,
                    force_update=force_update,
                    limit=limit_per_type
                )
                
                results[entity_type] = result
                total_success += result["success_count"]
                total_processed += result["total_found"]
                
                # 短暂休息避免过载
                await asyncio.sleep(2)
            
            end_time = datetime.now()
            total_duration = (end_time - start_time).total_seconds()
            
            # 汇总统计
            summary = {
                "total_entities_processed": total_processed,
                "total_entities_success": total_success,
                "total_entities_failed": total_processed - total_success,
                "total_duration_seconds": round(total_duration, 2),
                "success_rate": round(total_success / total_processed * 100, 2) if total_processed > 0 else 0,
                "results_by_type": results
            }
            
            logger.info(f"\n=== 批量向量化完成 ===")
            logger.info(f"总计处理: {total_processed} 个实体")
            logger.info(f"成功: {total_success} 个")
            logger.info(f"失败: {total_processed - total_success} 个")
            logger.info(f"成功率: {summary['success_rate']:.2f}%")
            logger.info(f"总耗时: {total_duration:.2f} 秒")
            
            return summary
            
        except Exception as e:
            logger.error(f"批量向量化所有实体失败: {e}")
            return {"error": str(e)}
    
    async def get_vectorization_stats(self) -> Dict[str, Any]:
        """获取向量化统计信息"""
        try:
            stats = {}
            entity_types = ["brand", "ingredient", "product"]
            
            for entity_type in entity_types:
                # 获取总实体数
                total_entities = await self._get_entity_count(entity_type)
                
                # 获取有向量的实体数
                vectorized_entities = await self._get_vectorized_entity_count(entity_type)
                
                stats[entity_type] = {
                    "total_entities": total_entities,
                    "vectorized_entities": vectorized_entities,
                    "missing_vectors": total_entities - vectorized_entities,
                    "vectorization_rate": round(vectorized_entities / total_entities * 100, 2) if total_entities > 0 else 0
                }
            
            # 汇总统计
            total_all = sum(s["total_entities"] for s in stats.values())
            vectorized_all = sum(s["vectorized_entities"] for s in stats.values())
            
            stats["summary"] = {
                "total_entities": total_all,
                "vectorized_entities": vectorized_all,
                "missing_vectors": total_all - vectorized_all,
                "overall_vectorization_rate": round(vectorized_all / total_all * 100, 2) if total_all > 0 else 0
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取向量化统计失败: {e}")
            return {"error": str(e)}
    
    async def _get_entity_count(self, entity_type: str) -> int:
        """获取实体总数"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient", 
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product"
            }
            
            label = label_map.get(entity_type)
            if not label:
                return 0
            
            query = f"MATCH (n:{label}) RETURN count(n) as count"
            result = await self.neo4j.run_query(query)
            
            return result[0]["count"] if result else 0
            
        except Exception as e:
            logger.error(f"获取 {entity_type} 实体数量失败: {e}")
            return 0
    
    async def _get_vectorized_entity_count(self, entity_type: str) -> int:
        """获取已向量化的实体数"""
        try:
            label_map = {
                "brand": f"{settings.NEO4J_LABEL_PREFIX}_Brand",
                "ingredient": f"{settings.NEO4J_LABEL_PREFIX}_Ingredient",
                "product": f"{settings.NEO4J_LABEL_PREFIX}_Product" 
            }
            
            label = label_map.get(entity_type)
            if not label:
                return 0
            
            query = f"""
            MATCH (n:{label})
            WHERE n.embedding IS NOT NULL AND n.embedding_dim IS NOT NULL
            RETURN count(n) as count
            """
            result = await self.neo4j.run_query(query)
            
            return result[0]["count"] if result else 0
            
        except Exception as e:
            logger.error(f"获取 {entity_type} 向量化实体数量失败: {e}")
            return 0

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量向量化工具')
    parser.add_argument('--type', choices=['brand', 'ingredient', 'product', 'all'], 
                       default='all', help='实体类型')
    parser.add_argument('--force', action='store_true', 
                       help='强制更新已有向量')
    parser.add_argument('--limit', type=int, 
                       help='限制处理数量')
    parser.add_argument('--stats', action='store_true', 
                       help='仅显示统计信息')
    
    args = parser.parse_args()
    
    tool = BatchVectorizationTool()
    
    try:
        await tool.initialize()
        
        if args.stats:
            # 显示统计信息
            logger.info("获取向量化统计信息...")
            stats = await tool.get_vectorization_stats()
            
            print("\n=== 向量化统计信息 ===")
            for entity_type in ["brand", "ingredient", "product"]:
                if entity_type in stats:
                    s = stats[entity_type]
                    print(f"{entity_type.upper()}:")
                    print(f"  总数: {s['total_entities']}")
                    print(f"  已向量化: {s['vectorized_entities']}")
                    print(f"  缺少向量: {s['missing_vectors']}")
                    print(f"  向量化率: {s['vectorization_rate']:.2f}%")
                    print()
            
            if "summary" in stats:
                s = stats["summary"]
                print("汇总:")
                print(f"  总数: {s['total_entities']}")
                print(f"  已向量化: {s['vectorized_entities']}")
                print(f"  缺少向量: {s['missing_vectors']}")
                print(f"  整体向量化率: {s['overall_vectorization_rate']:.2f}%")
        
        else:
            # 执行向量化
            if args.type == 'all':
                result = await tool.vectorize_all_entities(
                    force_update=args.force,
                    limit_per_type=args.limit
                )
                print(f"\n批量向量化结果: {result}")
            else:
                result = await tool.vectorize_entity_type(
                    entity_type=args.type,
                    force_update=args.force,
                    limit=args.limit
                )
                print(f"\n{args.type} 向量化结果: {result}")
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await tool.close()

if __name__ == "__main__":
    asyncio.run(main())