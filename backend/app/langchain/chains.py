import json
import logging
import re
from typing import Optional
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from app.langchain.volcano_llm import create_volcengine_llm
from app.models.product import ProductInfo, IngredientInfo

logger = logging.getLogger(__name__)


class ProductExtractionChain:
    """产品信息提取链"""
    
    def __init__(self):
        self.llm = create_volcengine_llm()
        self.parser = PydanticOutputParser(pydantic_object=ProductInfo)
        
        # 构建提示模板
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", """你是一个保健品行业的数据分析专家，专门负责从非结构化文本中提取产品信息。

任务要求：
1. 仔细分析提供的产品描述文本
2. 提取所有相关的产品信息
3. 严格按照指定的JSON格式返回
4. 如果某个字段无法确定，使用null值
5. 金额数字只保留数值部分，单位单独提取

{format_instructions}

请直接返回JSON格式的结果，不要包含任何其他文字："""),
            ("user", "请从以下文本中提取产品信息：\n\n{text}")
        ])
    
    async def extract(self, text: str) -> ProductInfo:
        """执行产品信息提取"""
        try:
            # 构建完整提示
            messages = self.prompt_template.format_messages(
                text=text,
                format_instructions=self.parser.get_format_instructions()
            )
            
            # 调用LLM
            response = await self.llm.ainvoke(messages)
            
            # 获取响应内容
            response_content = response.content
            
            # 清理响应内容
            cleaned_response = self._clean_response(response_content)
            
            # 解析JSON
            try:
                parsed_data = json.loads(cleaned_response)
                result = ProductInfo(**parsed_data)
                logger.info("Product extraction successful")
                return result
            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"JSON parsing failed: {e}, trying fallback extraction")
                return await self._fallback_extraction(text)
                
        except Exception as e:
            logger.error(f"Extraction failed: {e}")
            return await self._fallback_extraction(text)
    
    def _clean_response(self, response: str) -> str:
        """清理LLM响应，提取JSON部分"""
        # 移除可能的markdown代码块标记
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*$', '', response)
        
        # 查找JSON对象
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            return json_match.group()
        
        return response.strip()
    
    async def _fallback_extraction(self, text: str) -> ProductInfo:
        """降级提取策略 - 使用正则表达式"""
        logger.info("Using fallback extraction strategy")
        
        try:
            # 基础的正则提取
            product_name = self._extract_product_name(text)
            brand = self._extract_brand(text)
            ingredients = self._extract_ingredients(text)
            benefits = self._extract_benefits(text)
            sku = self._extract_sku(text)
            
            return ProductInfo(
                product_name=product_name or "需要手动填写",
                brand=brand or "需要手动填写",
                product_type="保健品",
                sku=sku,
                ingredients=ingredients,
                benefits=benefits
            )
            
        except Exception as e:
            logger.error(f"Fallback extraction failed: {e}")
            # 返回最基础的结构
            return ProductInfo(
                product_name="需要手动填写",
                brand="需要手动填写",
                product_type="保健品",
                ingredients=[]
            )
    
    def _extract_product_name(self, text: str) -> Optional[str]:
        """提取产品名称"""
        # 常见产品名称模式
        patterns = [
            r'([A-Za-z\'\s]+[^\w\s]*[^，。]*(?:胶囊|片|粒|丸|口服液|冲剂))',
            r'([A-Za-z\'\s]+\s*[^，。]{0,20}(?:维生素|钙片|蛋白粉))',
            r'^([^，。\n]{2,50}?)(?:，|。|\n)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                name = match.group(1).strip()
                if len(name) > 2:
                    return name
        
        return None
    
    def _extract_brand(self, text: str) -> Optional[str]:
        """提取品牌名称"""
        # 常见品牌模式
        patterns = [
            r"([A-Za-z\'\s&]+)(?:牌|品牌|公司)",
            r"^([A-Za-z\'\s&]+)(?=\s*[^A-Za-z\s])",
            r"([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:\s*['\s]*\s*[A-Z][a-z]+)*)",
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                match = match.strip()
                if 2 <= len(match) <= 30 and not any(char.isdigit() for char in match):
                    return match
        
        return None
    
    def _extract_ingredients(self, text: str) -> list[IngredientInfo]:
        """提取成分信息"""
        ingredients = []
        
        # 匹配成分、含量和单位的模式
        patterns = [
            r'([^，。\n]{2,20}?)(?:含量?|为)?\s*(\d+(?:\.\d+)?)\s*(mg|g|ml|IU|iu|毫克|克|毫升|国际单位)',
            r'每[^，。]*含\s*([^，。\n]{2,20}?)\s*(\d+(?:\.\d+)?)\s*(mg|g|ml|IU|iu|毫克|克|毫升|国际单位)',
            r'([^，。\n]{2,20}?)\s*(\d+(?:\.\d+)?)\s*(mg|g|ml|IU|iu|毫克|克|毫升|国际单位)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                name, amount, unit = match
                name = name.strip()
                
                # 清理成分名称
                name = re.sub(r'^[每含]', '', name)
                name = re.sub(r'[：:]$', '', name)
                
                if name and len(name) >= 2:
                    # 单位标准化
                    unit = unit.lower()
                    if unit in ['毫克', '毫升']:
                        unit = 'mg' if '克' in unit else 'ml'
                    elif unit == '克':
                        unit = 'g'
                    elif unit in ['国际单位', 'iu']:
                        unit = 'IU'
                    
                    ingredients.append(IngredientInfo(
                        name=name,
                        amount=float(amount),
                        unit=unit
                    ))
        
        return ingredients
    
    def _extract_benefits(self, text: str) -> Optional[str]:
        """提取功效描述"""
        # 功效关键词
        benefit_keywords = ['功效', '作用', '效果', '有助于', '促进', '增强', '改善', '缓解', '预防']
        
        for keyword in benefit_keywords:
            pattern = f'{keyword}[：:]?([^，。\n]{{5,100}})'
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
        
        # 查找包含健康相关词汇的句子
        health_patterns = [
            r'([^，。\n]*(?:免疫|抗氧化|美容|养颜|健康|营养|补充)[^，。\n]*)',
            r'([^，。\n]*(?:维生素|矿物质|蛋白质|氨基酸)[^，。\n]*功效[^，。\n]*)',
        ]
        
        for pattern in health_patterns:
            match = re.search(pattern, text)
            if match:
                benefit = match.group(1).strip()
                if 5 <= len(benefit) <= 200:
                    return benefit
        
        return None
    
    def _extract_sku(self, text: str) -> Optional[str]:
        """提取SKU编码"""
        patterns = [
            r'(?:SKU|sku|编号|货号|型号)[：:]?\s*([A-Za-z0-9\-_]+)',
            r'([A-Z]{2,}[-_][A-Z0-9\-_]{2,})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                sku = match.group(1).strip()
                if 3 <= len(sku) <= 20:
                    return sku
        
        return None
    
    async def close(self):
        """关闭资源"""
        # ChatOpenAI不需要手动关闭
        pass