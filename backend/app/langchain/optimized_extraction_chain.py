"""
优化版的纯提取链
单次LLM调用完成信息提取，不包含标准化处理
"""

import logging
import time
from typing import Dict, Any
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import PydanticOutputParser

from app.langchain.volcano_llm import create_volcengine_llm
from app.models.product import ProductInfo

logger = logging.getLogger(__name__)

class OptimizedExtractionChain:
    """优化版提取链 - 纯提取，无标准化"""
    
    def __init__(self):
        self.llm = create_volcengine_llm()
        
        # 创建纯提取的提示模板
        self.extraction_prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专业的产品信息提取专家。请从产品描述中准确提取结构化信息。

## 提取规则

### 1. 产品名称提取
- 提取完整的产品名称，保持原文描述
- 包含品牌+产品类型+规格等完整信息

### 2. 品牌名称提取  
- 提取准确的品牌名称，保持原始写法
- 包括标点符号和大小写

### 3. 成分信息提取
- 准确提取成分名称，保持原文描述
- 提取含量数值和单位
- 支持中英文成分名称

### 4. 产品类型
- 根据描述判断产品类型（保健品、营养品、药品等）

### 5. SKU和功效
- 提取产品编号、型号等标识
- 提取功效描述

**重要：保持提取的准确性，不要进行任何标准化处理，返回原文中实际出现的名称。**

{format_instructions}"""),
            ("human", "请从以下产品描述中提取信息：\n{text}")
        ])
        
        # 输出解析器
        self.output_parser = PydanticOutputParser(pydantic_object=ProductInfo)
    
    async def extract_optimized(self, text: str) -> Dict[str, Any]:
        """
        优化版提取 - 单次LLM调用完成纯提取
        
        Args:
            text: 产品描述文本
            
        Returns:
            提取的产品信息
        """
        start_time = time.time()
        
        try:
            logger.info(f"Starting optimized extraction for text length: {len(text)}")
            
            # 创建提取链
            chain = self.extraction_prompt | self.llm | self.output_parser
            
            # 执行提取
            result = await chain.ainvoke({
                "text": text,
                "format_instructions": self.output_parser.get_format_instructions()
            })
            
            # 转换为标准格式
            extracted_data = self._convert_to_dict(result)
            
            processing_time = time.time() - start_time
            logger.info(f"Optimized extraction completed in {processing_time:.2f}s")
            
            # 构建元数据
            metadata = {
                "processing_time": processing_time,
                "extraction_method": "optimized_pure_extraction",
                "overall_confidence_score": self._calculate_confidence(result),
                "text_length": len(text)
            }
            
            return {
                "data": extracted_data,
                "metadata": metadata
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Optimized extraction failed after {processing_time:.2f}s: {e}")
            raise
    
    def _convert_to_dict(self, result: ProductInfo) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "product_name": result.product_name,
            "brand": result.brand,
            "product_type": result.product_type,
            "sku": result.sku,
            "benefits": result.benefits,
            "ingredients": [
                {
                    "name": ing.name,
                    "amount": ing.amount,
                    "unit": ing.unit
                }
                for ing in result.ingredients
            ]
        }
    
    def _calculate_confidence(self, result: ProductInfo) -> float:
        """计算简化的置信度"""
        score = 0.0
        total = 5.0
        
        # 产品名称 (20%)
        if result.product_name and result.product_name != "需要手动填写":
            score += 1.0
        
        # 品牌名称 (20%)  
        if result.brand and result.brand != "需要手动填写":
            score += 1.0
        
        # 成分信息 (30%)
        if result.ingredients and len(result.ingredients) > 0:
            score += 1.5
            # 如果有含量信息，额外加分
            if any(ing.amount for ing in result.ingredients):
                score += 0.5
        
        # 功效描述 (15%)
        if result.benefits and len(result.benefits) > 5:
            score += 0.75
        
        # SKU信息 (15%)
        if result.sku:
            score += 0.75
        
        return min(score / total, 1.0)

# 全局实例
_optimized_chain = None

def get_optimized_extraction_chain() -> OptimizedExtractionChain:
    """获取优化版提取链实例"""
    global _optimized_chain
    if _optimized_chain is None:
        _optimized_chain = OptimizedExtractionChain()
    return _optimized_chain