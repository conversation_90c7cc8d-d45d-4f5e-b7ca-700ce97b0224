from langchain_community.chat_models import ChatOpenAI

from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


def create_volcengine_llm():
    """创建火山引擎方舟大模型LLM实例"""
    return ChatOpenAI(
        temperature=0,
        model_name=settings.MODEL_ID,           # doubao-seed-1-6-flash-250715
        openai_api_key=settings.ARK_API_KEY,  # ARK API密钥
        openai_api_base=settings.BASE_URL,    # https://ark.cn-beijing.volces.com/api/v3
        max_retries=settings.ARK_MAX_RETRIES,
        request_timeout=settings.ARK_TIMEOUT
    )