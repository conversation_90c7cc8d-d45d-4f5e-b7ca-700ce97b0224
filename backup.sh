#!/bin/bash
# KGMS Data Backup Script
# Neo4j数据备份脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
BACKUP_DIR="./backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="kgms_backup_${TIMESTAMP}"
NEO4J_CONTAINER="kgms-neo4j"
# NEO4J_CONTAINER="pet_archive_neo4j"
BACKEND_CONTAINER="kgms-backend"

# 创建备份目录
create_backup_dir() {
    print_info "创建备份目录..."
    mkdir -p ${BACKUP_DIR}
    print_success "备份目录创建完成: ${BACKUP_DIR}"
}

# 检查Neo4j容器
check_neo4j_container() {
    print_info "检查Neo4j容器状态..."
    
    if ! docker ps | grep -q ${NEO4J_CONTAINER}; then
        print_error "KGMS Neo4j容器 (${NEO4J_CONTAINER}) 未运行"
        echo ""
        print_info "当前运行的容器："
        docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
        echo ""
        print_info "请先启动KGMS服务："
        echo "  ./deploy.sh"
        echo "  或 docker-compose up -d"
        exit 1
    fi
    
    print_success "Neo4j容器运行正常"
}

# 备份Neo4j数据
backup_neo4j() {
    print_info "开始备份Neo4j数据..."
    
    local backup_file="${BACKUP_DIR}/${BACKUP_NAME}.dump"
    
    print_warning "备份需要停止Neo4j服务以确保数据一致性"
    read -p "确认继续备份？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "备份操作已取消"
        return 1
    fi
    
    # 停止Neo4j服务
    print_info "停止Neo4j服务..."
    docker stop ${NEO4J_CONTAINER}
    
    # 等待服务完全停止
    sleep 5
    
    # 启动临时容器进行备份
    print_info "创建临时备份容器..."
    docker run --rm \
        --volumes-from ${NEO4J_CONTAINER} \
        --volume $(pwd)/backups:/backup \
        neo4j:5.26-community \
        neo4j-admin database dump neo4j --to-path=/backup/
    
    # 重命名备份文件
    if [ -f "${BACKUP_DIR}/neo4j.dump" ]; then
        mv "${BACKUP_DIR}/neo4j.dump" "${backup_file}"
        
        # 压缩备份文件
        print_info "压缩备份文件..."
        gzip ${backup_file}
        
        print_success "Neo4j数据备份完成: ${backup_file}.gz"
    else
        print_error "备份文件生成失败"
        return 1
    fi
    
    # 重启Neo4j服务
    print_info "重启Neo4j服务..."
    docker start ${NEO4J_CONTAINER}
    
    # 等待服务启动
    sleep 10
    print_success "Neo4j服务已重启"
}

# 备份SQLite数据库
backup_sqlite() {
    print_info "开始备份SQLite用户数据库..."
    
    local sqlite_backup_file="${BACKUP_DIR}/${BACKUP_NAME}_sqlite.db"
    
    # 检查后端容器是否运行
    if ! docker ps | grep -q ${BACKEND_CONTAINER}; then
        print_warning "后端容器未运行，跳过SQLite备份"
        return
    fi
    
    # 从容器中复制SQLite数据库文件
    if docker cp ${BACKEND_CONTAINER}:/app/app_users.db ${sqlite_backup_file} 2>/dev/null; then
        # 压缩SQLite备份
        gzip ${sqlite_backup_file}
        print_success "SQLite数据库备份完成: ${sqlite_backup_file}.gz"
    else
        print_warning "SQLite数据库文件不存在或备份失败"
    fi
}

# 备份应用日志
backup_logs() {
    print_info "备份应用日志..."
    
    local log_backup_dir="${BACKUP_DIR}/${BACKUP_NAME}_logs"
    mkdir -p ${log_backup_dir}
    
    # 备份Docker容器日志
    for container in kgms-backend kgms-frontend kgms-neo4j kgms-nginx; do
        if docker ps -a --format "table {{.Names}}" | grep -q ${container}; then
            print_info "备份${container}日志..."
            docker logs ${container} > ${log_backup_dir}/${container}.log 2>&1 || true
        fi
    done
    
    # 打包日志文件
    tar -czf ${log_backup_dir}.tar.gz -C ${BACKUP_DIR} ${BACKUP_NAME}_logs
    rm -rf ${log_backup_dir}
    
    print_success "应用日志备份完成: ${log_backup_dir}.tar.gz"
}

# 备份环境配置
backup_config() {
    print_info "备份环境配置..."
    
    local config_backup_dir="${BACKUP_DIR}/${BACKUP_NAME}_config"
    mkdir -p ${config_backup_dir}
    
    # 备份重要配置文件（排除敏感信息）
    if [[ -f .env ]]; then
        # 复制.env但移除敏感信息
        sed 's/\(.*PASSWORD.*=\).*/\1[REDACTED]/' .env > ${config_backup_dir}/env_template
        sed -i 's/\(.*API_KEY.*=\).*/\1[REDACTED]/' ${config_backup_dir}/env_template
    fi
    
    # 备份Docker配置
    cp docker-compose.yml ${config_backup_dir}/ 2>/dev/null || true
    
    # 备份Nginx配置
    cp -r nginx/ ${config_backup_dir}/ 2>/dev/null || true
    
    # 打包配置文件
    tar -czf ${config_backup_dir}.tar.gz -C ${BACKUP_DIR} ${BACKUP_NAME}_config
    rm -rf ${config_backup_dir}
    
    print_success "环境配置备份完成: ${config_backup_dir}.tar.gz"
}

# 清理旧备份
cleanup_old_backups() {
    print_info "清理旧备份文件..."
    
    # 保留最近30天的备份
    find ${BACKUP_DIR} -name "kgms_backup_*.dump.gz" -mtime +30 -delete 2>/dev/null || true
    find ${BACKUP_DIR} -name "*_sqlite.db.gz" -mtime +30 -delete 2>/dev/null || true
    find ${BACKUP_DIR} -name "*_logs.tar.gz" -mtime +30 -delete 2>/dev/null || true
    find ${BACKUP_DIR} -name "*_config.tar.gz" -mtime +30 -delete 2>/dev/null || true
    
    print_success "旧备份文件清理完成"
}

# 显示备份信息
show_backup_info() {
    print_info "备份完成，文件信息："
    echo ""
    
    ls -lh ${BACKUP_DIR}/kgms_backup_${TIMESTAMP}*
    
    echo ""
    print_info "备份文件说明："
    echo "  - *.dump.gz       : Neo4j图数据库备份"
    echo "  - *_sqlite.db.gz  : SQLite用户数据备份"
    echo "  - *_logs.tar.gz   : 应用运行日志"
    echo "  - *_config.tar.gz : 环境配置文件"
    echo ""
    print_info "恢复数据请参考文档或联系管理员"
}

# 恢复功能
restore_neo4j() {
    local backup_file=$1
    
    if [[ -z "$backup_file" ]]; then
        print_error "请指定备份文件: ./backup.sh restore <backup_file>"
        exit 1
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        print_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    print_warning "此操作将覆盖现有数据库！"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi
    
    print_info "停止Neo4j服务..."
    docker-compose stop neo4j
    
    # 等待服务完全停止
    sleep 5
    
    print_info "恢复数据库..."
    
    # 解压备份文件到本地
    local local_dump_file
    if [[ "$backup_file" == *.gz ]]; then
        local_dump_file="/tmp/restore.dump"
        gunzip -c "$backup_file" > "$local_dump_file"
    else
        local_dump_file="$backup_file"
    fi
    
    # 使用临时容器恢复数据库
    print_info "创建临时恢复容器..."
    
    # Neo4j load 命令期望特定的文件名 neo4j.dump，直接执行恢复
    docker run --rm \
        --volumes-from ${NEO4J_CONTAINER} \
        --volume "$(realpath "$local_dump_file"):/tmp/backup.dump:ro" \
        neo4j:5.26-community \
        sh -c "cp /tmp/backup.dump /tmp/neo4j.dump && echo 'File copied successfully' && neo4j-admin database load neo4j --from-path=/tmp/ --overwrite-destination=true --verbose"
    
    # 重启服务
    print_info "重启Neo4j服务..."
    docker-compose start neo4j
    
    # 等待服务启动
    sleep 10
    
    # 清理临时文件
    if [[ "$backup_file" == *.gz ]]; then
        rm -f /tmp/restore.dump
    fi
    
    print_success "数据恢复完成"
}

# 恢复SQLite数据库
restore_sqlite() {
    local sqlite_backup_file=$1
    
    if [[ -z "$sqlite_backup_file" ]]; then
        print_error "请指定SQLite备份文件"
        return 1
    fi
    
    if [[ ! -f "$sqlite_backup_file" ]]; then
        print_error "SQLite备份文件不存在: $sqlite_backup_file"
        return 1
    fi
    
    print_warning "此操作将覆盖现有用户数据！"
    read -p "确认恢复SQLite数据？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "SQLite恢复操作已取消"
        return 0
    fi
    
    print_info "恢复SQLite用户数据库..."
    
    # 解压备份文件
    local temp_file="/tmp/restore_sqlite.db"
    if [[ "$sqlite_backup_file" == *.gz ]]; then
        gunzip -c "$sqlite_backup_file" > "$temp_file"
    else
        cp "$sqlite_backup_file" "$temp_file"
    fi
    
    # 停止后端服务
    print_info "停止后端服务..."
    docker-compose stop backend
    
    # 复制数据库文件到容器
    docker cp "$temp_file" ${BACKEND_CONTAINER}:/app/app_users.db
    
    # 重启后端服务
    print_info "重启后端服务..."
    docker-compose start backend
    
    # 清理临时文件
    rm -f "$temp_file"
    
    print_success "SQLite数据恢复完成"
}

# 主函数
main() {
    case "${1:-backup}" in
        "backup")
            echo "========================================"
            echo "    KGMS 数据备份"
            echo "========================================"
            echo ""
            
            create_backup_dir
            check_neo4j_container
            backup_neo4j
            backup_sqlite
            backup_logs
            backup_config
            cleanup_old_backups
            show_backup_info
            
            print_success "🎉 备份完成！"
            ;;
            
        "restore")
            echo "========================================"
            echo "    KGMS 数据恢复"
            echo "========================================"
            echo ""
            
            restore_neo4j "$2"
            ;;
            
        "restore-sqlite")
            echo "========================================"
            echo "    KGMS SQLite数据恢复"
            echo "========================================"
            echo ""
            
            restore_sqlite "$2"
            ;;
            
        "list")
            print_info "可用备份文件："
            ls -lht ${BACKUP_DIR}/kgms_backup_*.dump.gz 2>/dev/null || print_warning "未找到备份文件"
            ;;
            
        *)
            echo "用法: $0 [backup|restore <file>|restore-sqlite <file>|list]"
            echo ""
            echo "命令："
            echo "  backup                    - 创建新备份 (包含Neo4j和SQLite)"
            echo "  restore <neo4j_file>      - 从备份恢复Neo4j图数据库"
            echo "  restore-sqlite <sql_file> - 从备份恢复SQLite用户数据库"
            echo "  list                      - 列出所有备份文件"
            echo ""
            echo "示例："
            echo "  $0 backup"
            echo "  $0 restore backups/kgms_backup_20250828_143022.dump.gz"
            echo "  $0 restore-sqlite backups/kgms_backup_20250828_143022_sqlite.db.gz"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"