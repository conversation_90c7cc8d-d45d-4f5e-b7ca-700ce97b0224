# KGMS 生产部署指南

## 📋 部署概述

KGMS采用Docker Compose进行容器化部署，包含以下服务：
- **Nginx**: 反向代理和静态文件服务
- **Frontend**: React前端应用
- **Backend**: FastAPI后端服务 (包含SQLite用户数据库)
- **Neo4j**: 图数据库

## 🔧 环境要求

### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / 其他支持Docker的Linux发行版
- **内存**: 最低4GB，推荐8GB+
- **存储**: 最低20GB可用空间，推荐50GB+
- **网络**: 确保可访问火山引擎API

### 软件依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io docker-compose curl

# CentOS/RHEL  
sudo yum install -y docker docker-compose curl
sudo systemctl start docker
sudo systemctl enable docker
```

## 🚀 部署步骤

### 1. 下载项目代码
```bash
# 克隆项目仓库
git clone <your-repo-url> kgms
cd kgms

# 或者上传项目文件到服务器
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.production .env

# 编辑配置文件
vim .env
```

**必须配置的关键项：**
```bash
# Neo4j数据库密码（请使用强密码）
NEO4J_PASSWORD=your_strong_password_here

# 火山引擎API密钥
ARK_API_KEY=your_volcano_api_key_here

# 域名配置（如果使用域名）
DOMAIN=your-domain.com
```

### 3. 一键部署
```bash
# 给脚本执行权限
chmod +x deploy.sh backup.sh health-check.sh

# 执行部署
./deploy.sh
```

部署脚本会自动：
- 检查环境依赖
- 构建Docker镜像
- 启动所有服务
- 初始化SQLite用户数据库（默认管理员：admin）
- 执行健康检查

### 4. 验证部署
```bash
# 检查服务状态
docker-compose ps

# 执行健康检查
./health-check.sh

# 查看服务日志
docker-compose logs -f
```

## 🌐 服务访问

部署完成后可访问：

- **前端应用**: http://your-server-ip:8001 (通过nginx) 或 http://your-domain.com
- **API文档**: http://your-server-ip:8001/docs 或直接访问后端 http://your-server-ip:8081/docs
- **Neo4j浏览器**: http://your-server-ip:7474
  - 账户：`neo4j` / `你的Neo4j密码`
- **健康检查**: 
  - Nginx状态: http://your-server-ip:8001/health
  - 后端状态: http://your-server-ip:8001/health-backend

### 端口映射说明 (Docker内部 → 宿主机)
- `frontend:80` → `5173` (前端服务)
- `backend:8080` → `8081` (后端API)  
- `nginx:80` → `8001` (统一入口)
- `neo4j:7474` → `7474` (图数据库管理)
- `neo4j:7687` → `7687` (Bolt协议)

## 🔒 SSL/HTTPS 配置

### 1. 获取SSL证书
```bash
# 使用Let's Encrypt（推荐）
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 或上传现有证书到 nginx/ssl/ 目录
```

### 2. 启用HTTPS
编辑 `nginx/conf.d/kgms.conf`，取消SSL相关配置的注释：

```nginx
listen 443 ssl http2;
ssl_certificate /etc/nginx/ssl/cert.pem;
ssl_certificate_key /etc/nginx/ssl/key.pem;
```

### 3. 重启Nginx
```bash
docker-compose restart nginx
```

## 🌍 域名配置和反向代理

### 使用自定义域名访问 (推荐生产环境)

**1. DNS解析配置**
```bash
# 将域名解析到服务器IP
# A记录: your-domain.com -> your-server-ip
# 可通过域名服务商管理面板配置
```

**2. 反向代理配置** 
如果服务器已有Web服务(如宝塔面板)，推荐使用现有反向代理：

```nginx
# 在现有nginx配置中添加：
location / {
    proxy_pass http://127.0.0.1:8001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

**3. 防火墙配置**
```bash
# 只需开放nginx端口，内部端口可保持关闭提高安全性
# 开放端口: 80, 443 (SSL), 8001 (KGMS)
# 关闭内部端口: 5173, 8081 (仅Docker内部访问)

# CentOS/RHEL 防火墙配置
firewall-cmd --permanent --add-port=8001/tcp
firewall-cmd --reload

# 阿里云安全组规则
# 入方向: 允许 8001/tcp
```

**4. 域名访问验证**
```bash
# 验证域名解析
nslookup your-domain.com

# 验证服务访问
curl -I http://your-domain.com
curl http://your-domain.com/health-backend  # 应返回JSON格式系统状态
```

## 💾 数据持久化

KGMS使用Docker数据卷确保数据在容器重启后保持持久化：

### 数据卷配置
```yaml
volumes:
  neo4j_data: # Neo4j图数据库数据
  sqlite_data: # SQLite用户数据库
  backend_logs: # 后端应用日志
  nginx_logs: # Nginx访问日志
```

### 数据存储位置
- **Neo4j图数据**: Docker卷 `neo4j_data`
- **SQLite用户数据**: Docker卷 `sqlite_data` (挂载到 `/app/app_users.db`)
- **应用日志**: Docker卷 `backend_logs` 和 `nginx_logs`

### 默认用户账户
首次部署后会自动创建默认管理员账户：
- **用户名**: `admin`
- **密码**: `admin123` 
- **权限**: 系统管理员

**⚠️ 生产环境请立即修改默认密码！**

## 📊 监控与维护

### 健康检查
```bash
# 完整健康检查
./health-check.sh check

# 实时监控
./health-check.sh monitor

# 快速检查
./health-check.sh quick

# 直接检查各服务状态
curl http://your-server-ip:8001/health          # Nginx状态
curl http://your-server-ip:8001/health-backend  # 后端系统状态 (JSON格式)
curl http://your-server-ip:8081/health          # 直接访问后端健康接口
```

### 数据备份
```bash
# 创建完整备份（Neo4j图数据 + SQLite用户数据 + 日志配置）
./backup.sh backup

# 查看备份列表
./backup.sh list

# 恢复Neo4j图数据库
./backup.sh restore backups/kgms_backup_20250828_143022.dump.gz

# 恢复SQLite用户数据库
./backup.sh restore-sqlite backups/kgms_backup_20250828_143022_sqlite.db.gz
```

**备份文件说明：**
- `*.dump.gz` - Neo4j图数据库备份
- `*_sqlite.db.gz` - SQLite用户数据备份
- `*_logs.tar.gz` - 应用运行日志
- `*_config.tar.gz` - 环境配置文件

### 日志管理
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend

# 清理日志
docker system prune --volumes -f
```

### 服务管理
```bash
# 重启所有服务
docker-compose restart

# 重启单个服务
docker-compose restart backend

# 停止服务
docker-compose down

# 更新服务
git pull
./deploy.sh
```

## ⚙️ 性能优化

### Neo4j优化
编辑 `docker-compose.yml` 中的Neo4j环境变量：

```yaml
environment:
  # 根据服务器内存调整
  - NEO4J_dbms_memory_heap_max_size=4G
  - NEO4J_dbms_memory_pagecache_size=2G
```

### 后端优化
编辑 `backend/Dockerfile` 调整worker数量：

```dockerfile
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8080", "--workers", "8"]
```

### Nginx优化
根据需要调整 `nginx/nginx.conf` 中的配置：

```nginx
worker_processes auto;
worker_connections 2048;
```

## 🔍 故障排除

### 常见问题

**1. 端口冲突问题** (生产环境常见)
```bash
# 检查端口占用情况
sudo netstat -tlnp | grep :8080
sudo netstat -tlnp | grep :3000
sudo netstat -tlnp | grep :80

# 如遇端口冲突，修改 docker-compose.yml 端口映射：
# 8080:8080 → 8081:8080 (后端服务)
# 3000:80 → 5173:80 (前端服务)
# 80:80 → 8001:80 (nginx服务)

# 重新部署
docker-compose down && docker-compose up -d
```

**2. 服务启动失败**
```bash
# 查看详细错误信息
docker-compose logs service_name

# 常见问题：版本兼容性
# 如果看到 "version is obsolete" 警告，可忽略，不影响功能

# 检查所有服务状态
docker-compose ps
```

**3. Neo4j连接失败**
```bash
# 检查Neo4j日志
docker-compose logs neo4j

# 重置Neo4j密码
docker-compose exec neo4j cypher-shell -u neo4j -p current_password

# Neo4j 5.26版本特有问题解决
# 如果遇到配置错误，简化 docker-compose.yml 中的 Neo4j 配置：
environment:
  - NEO4J_AUTH=neo4j/your_password
  - NEO4J_PLUGINS=["apoc"]
```

**4. 数据备份恢复失败**
```bash
# Neo4j备份恢复常见问题
# 问题：容器已停止无法执行恢复命令
# 解决：使用临时容器进行数据恢复

# 检查备份文件
file backups/your_backup.dump.gz
gunzip -t backups/your_backup.dump.gz  # 验证文件完整性

# 如果备份脚本失败，手动恢复：
docker-compose stop neo4j
docker run --rm \
  --volumes-from kgms-neo4j \
  -v $(pwd)/backups:/backup \
  neo4j:5.26-community \
  sh -c "gunzip -c /backup/your_backup.dump.gz > /tmp/neo4j.dump && neo4j-admin database load neo4j --from-path=/tmp/ --overwrite-destination=true"
docker-compose start neo4j
```

**3. API调用失败**
```bash
# 检查火山引擎API密钥
curl -H "Authorization: Bearer $ARK_API_KEY" https://ark.cn-beijing.volces.com/api/v3/models

# 检查网络连通性
docker-compose exec backend ping ark.cn-beijing.volces.com
```

**5. 前端页面空白或API调用失败**
```bash
# 检查前端构建
docker-compose logs frontend

# 检查API连接 (注意端口变化)
curl http://localhost:8081/health  # 直接访问后端
curl http://localhost:8001/health-backend  # 通过nginx访问

# 常见问题：前端API路径配置
# 前端会根据hostname自动选择API地址：
# - localhost/127.0.0.1: http://hostname:8080 (开发环境)
# - 其他域名: 相对路径 (生产环境，通过nginx代理)

# 如果登录时出现CORS错误，检查nginx配置：
docker exec kgms-nginx cat /etc/nginx/conf.d/kgms.conf | grep -A 5 "auth"
```

**6. 用户认证失败和密码管理**
```bash
# 检查SQLite数据库状态
docker-compose logs backend | grep -i sqlite

# 重新初始化默认管理员账户（会保留现有数据）
docker-compose restart backend

# 检查SQLite数据库文件
docker-compose exec backend ls -la /app/app_users.db

# 更新管理员密码的安全方法：
# 1. 创建密码更新脚本并复制到容器
docker cp update_admin_password.py kgms-backend:/tmp/
# 2. 在容器内执行
docker exec -it kgms-backend python /tmp/update_admin_password.py
# 3. 清理临时文件
docker-compose restart backend  # 重启会清理/tmp目录
```

**7. TypeScript编译错误** (开发/构建时)
```bash
# 如果前端构建时出现大量TypeScript错误：
# 常见错误类型：
# - 未使用的变量: 添加下划线前缀或使用 void 操作符
# - 类型不匹配: 检查Ant Design组件属性类型
# - 导入错误: 检查模块路径和类型定义

# 查看具体构建错误
docker-compose logs frontend | grep -i error
```

### 紧急恢复
```bash
# 停止所有服务
docker-compose down

# 清理所有容器和卷（注意：会丢失数据）
docker system prune --volumes -f

# 从备份恢复（分别恢复图数据和用户数据）
./backup.sh restore backups/latest_neo4j.dump.gz
./backup.sh restore-sqlite backups/latest_sqlite.db.gz

# 重新部署
./deploy.sh
```

## 📈 扩展配置

### 多节点部署
对于高负载场景，可以配置多个后端实例：

```yaml
backend:
  deploy:
    replicas: 3
  # ... 其他配置
```

### 负载均衡
使用Nginx upstream配置多后端负载均衡：

```nginx
upstream kgms_backend {
    server backend1:8080;
    server backend2:8080;
    server backend3:8080;
}
```

### 数据库集群
Neo4j企业版支持集群部署，详见Neo4j官方文档。

## 📞 技术支持

如遇到部署问题：

1. 首先查看日志：`docker-compose logs -f`
2. 执行健康检查：`./health-check.sh`
3. 查看本文档的故障排除部分
4. 检查KGMS项目的 `CLAUDE.md` 和 `README.md`

---

## 📝 部署经验总结

### 这次成功部署的关键经验：

1. **端口冲突处理**: 生产环境常见8080、3000端口被占用，需要重映射端口
2. **TypeScript严格模式**: 24+个编译错误需要系统性解决，主要是unused variables和type issues
3. **Neo4j版本兼容**: 5.26版本的配置简化和备份恢复流程优化
4. **Nginx路由配置**: API代理、健康检查端点需要精确配置避免冲突
5. **前端API动态配置**: 开发和生产环境的自动检测和路径切换
6. **数据安全**: 生产环境密码更新和临时文件清理
7. **域名和反向代理**: 与现有Web服务(宝塔)的集成配置

### 最终成功配置：
- **域名访问**: kgms.kilofolo.com → 127.0.0.1:8001
- **端口映射**: 后端8081, 前端5173, nginx8001
- **管理员密码**: 已更新为生产环境安全密码
- **系统状态**: 所有服务健康运行，状态监控正常

### 推荐部署流程：
1. 环境检查和端口冲突预处理
2. Docker Compose服务编排部署
3. TypeScript编译问题批量修复
4. 数据备份和恢复测试
5. 域名配置和反向代理设置
6. 安全配置(密码更新、防火墙)
7. 系统监控和健康检查验证

---

**最后更新**: 2025年8月28日 (生产部署实践版)  
**版本**: v1.1.0 (包含生产环境部署经验)  
**部署状态**: ✅ 生产环境成功运行