#!/bin/bash
# KGMS Health Check Script
# 系统健康检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 健康检查结果
HEALTH_STATUS=0
CHECKS_PASSED=0
CHECKS_TOTAL=0

# 记录检查结果
record_check() {
    local check_name=$1
    local status=$2
    local message=$3
    
    ((CHECKS_TOTAL++))
    
    if [[ $status -eq 0 ]]; then
        print_success "✓ $check_name: $message"
        ((CHECKS_PASSED++))
    else
        print_error "✗ $check_name: $message"
        HEALTH_STATUS=1
    fi
}

# 检查Docker服务
check_docker() {
    local check_name="Docker服务"
    
    if docker info > /dev/null 2>&1; then
        record_check "$check_name" 0 "运行正常"
    else
        record_check "$check_name" 1 "Docker服务未运行"
    fi
}

# 检查容器状态
check_containers() {
    local containers=("kgms-neo4j" "kgms-backend" "kgms-frontend" "kgms-nginx")
    
    for container in "${containers[@]}"; do
        local check_name="容器 $container"
        
        if docker ps | grep -q "$container"; then
            local status=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "unknown")
            if [[ "$status" == "healthy" ]] || [[ "$status" == "unknown" ]]; then
                record_check "$check_name" 0 "运行正常"
            else
                record_check "$check_name" 1 "健康检查失败: $status"
            fi
        else
            record_check "$check_name" 1 "容器未运行"
        fi
    done
}

# 检查端口连通性
check_ports() {
    local ports=(
        "80:Nginx主入口"
        "8080:后端API"
        "7474:Neo4j HTTP"
        "7687:Neo4j Bolt"
    )
    
    for port_info in "${ports[@]}"; do
        local port=$(echo "$port_info" | cut -d: -f1)
        local desc=$(echo "$port_info" | cut -d: -f2)
        local check_name="端口 $port ($desc)"
        
        if nc -z localhost "$port" 2>/dev/null; then
            record_check "$check_name" 0 "可访问"
        else
            record_check "$check_name" 1 "无法访问"
        fi
    done
}

# 检查HTTP服务
check_http_services() {
    local services=(
        "http://localhost/health:前端健康检查"
        "http://localhost/api/docs:后端API文档"
        "http://localhost:8080/health:后端健康检查"
    )
    
    for service_info in "${services[@]}"; do
        local url=$(echo "$service_info" | cut -d: -f1-2)
        local desc=$(echo "$service_info" | cut -d: -f3)
        local check_name="HTTP服务 ($desc)"
        
        if curl -f -s "$url" > /dev/null 2>&1; then
            record_check "$check_name" 0 "响应正常"
        else
            record_check "$check_name" 1 "无响应或错误"
        fi
    done
}

# 检查Neo4j数据库
check_neo4j_database() {
    local check_name="Neo4j数据库连接"
    
    # 从环境变量或默认值获取密码
    local neo4j_password=$(grep "NEO4J_PASSWORD" .env 2>/dev/null | cut -d= -f2 || echo "changeme123")
    
    # 尝试连接数据库
    if docker exec kgms-neo4j cypher-shell -u neo4j -p "$neo4j_password" "RETURN 1" > /dev/null 2>&1; then
        # 检查KGMS数据
        local node_count=$(docker exec kgms-neo4j cypher-shell -u neo4j -p "$neo4j_password" "MATCH (n) WHERE any(label IN labels(n) WHERE label STARTS WITH 'KGMS_') RETURN count(n)" 2>/dev/null | grep -o '[0-9]*' | head -1 || echo "0")
        record_check "$check_name" 0 "连接正常，KGMS节点数: $node_count"
    else
        record_check "$check_name" 1 "无法连接数据库"
    fi
}

# 检查磁盘空间
check_disk_space() {
    local check_name="磁盘空间"
    local usage=$(df . | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $usage -lt 80 ]]; then
        record_check "$check_name" 0 "使用率: ${usage}%"
    elif [[ $usage -lt 90 ]]; then
        record_check "$check_name" 1 "使用率过高: ${usage}% (警告)"
        print_warning "磁盘空间不足，建议清理"
    else
        record_check "$check_name" 1 "使用率危险: ${usage}% (严重)"
        print_error "磁盘空间严重不足，请立即清理"
    fi
}

# 检查内存使用
check_memory_usage() {
    local check_name="内存使用"
    
    if command -v free > /dev/null 2>&1; then
        local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2 }')
        
        if [[ $mem_usage -lt 80 ]]; then
            record_check "$check_name" 0 "使用率: ${mem_usage}%"
        elif [[ $mem_usage -lt 90 ]]; then
            record_check "$check_name" 1 "使用率过高: ${mem_usage}% (警告)"
        else
            record_check "$check_name" 1 "使用率危险: ${mem_usage}% (严重)"
        fi
    else
        record_check "$check_name" 1 "无法获取内存信息"
    fi
}

# 检查日志文件大小
check_log_sizes() {
    local check_name="日志文件大小"
    local total_size=0
    
    # 检查Docker容器日志
    for container in kgms-neo4j kgms-backend kgms-frontend kgms-nginx; do
        if docker ps -q -f name=$container > /dev/null 2>&1; then
            local log_file="/var/lib/docker/containers/$(docker ps -q -f name=$container)/$container-json.log"
            if [[ -f "$log_file" ]]; then
                local size=$(stat -c%s "$log_file" 2>/dev/null || echo "0")
                total_size=$((total_size + size))
            fi
        fi
    done
    
    # 转换为MB
    total_size_mb=$((total_size / 1024 / 1024))
    
    if [[ $total_size_mb -lt 500 ]]; then
        record_check "$check_name" 0 "总大小: ${total_size_mb}MB"
    else
        record_check "$check_name" 1 "总大小过大: ${total_size_mb}MB，建议清理"
        print_warning "可以使用 docker system prune --volumes -f 清理"
    fi
}

# 生成健康报告
generate_report() {
    echo ""
    echo "========================================"
    echo "    KGMS 健康检查报告"
    echo "========================================"
    echo ""
    
    if [[ $HEALTH_STATUS -eq 0 ]]; then
        print_success "🎉 系统健康状态: 良好"
    else
        print_error "⚠️  系统健康状态: 存在问题"
    fi
    
    echo ""
    print_info "检查结果: ${CHECKS_PASSED}/${CHECKS_TOTAL} 项通过"
    
    # 显示系统信息
    echo ""
    print_info "系统信息:"
    echo "  - 主机: $(hostname)"
    echo "  - 操作系统: $(uname -s) $(uname -r)"
    echo "  - 运行时间: $(uptime -p 2>/dev/null || echo "N/A")"
    echo "  - 当前时间: $(date)"
    
    # 显示Docker信息
    echo ""
    print_info "Docker信息:"
    echo "  - Docker版本: $(docker --version | cut -d' ' -f3 | cut -d',' -f1)"
    echo "  - 运行容器数: $(docker ps -q | wc -l)"
    echo "  - 总容器数: $(docker ps -a -q | wc -l)"
    
    # 显示快速命令
    echo ""
    print_info "常用管理命令:"
    echo "  - 查看服务状态: docker-compose ps"
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 完整重启: ./deploy.sh"
    echo "  - 数据备份: ./backup.sh"
}

# 监控模式
monitor_mode() {
    print_info "进入监控模式 (Ctrl+C 退出)..."
    
    while true; do
        clear
        echo "========================================"
        echo "    KGMS 实时监控 - $(date)"
        echo "========================================"
        
        # 重置计数器
        HEALTH_STATUS=0
        CHECKS_PASSED=0
        CHECKS_TOTAL=0
        
        # 执行基本检查
        check_containers
        check_ports
        check_http_services
        check_neo4j_database
        
        # 显示状态
        echo ""
        if [[ $HEALTH_STATUS -eq 0 ]]; then
            print_success "✅ 系统状态正常 (${CHECKS_PASSED}/${CHECKS_TOTAL})"
        else
            print_error "❌ 系统存在问题 (${CHECKS_PASSED}/${CHECKS_TOTAL})"
        fi
        
        sleep 30
    done
}

# 主函数
main() {
    case "${1:-check}" in
        "check")
            echo "========================================"
            echo "    KGMS 健康检查"
            echo "========================================"
            echo ""
            
            check_docker
            check_containers
            check_ports
            check_http_services
            check_neo4j_database
            check_disk_space
            check_memory_usage
            check_log_sizes
            
            generate_report
            
            # 返回健康状态码
            exit $HEALTH_STATUS
            ;;
            
        "monitor")
            monitor_mode
            ;;
            
        "quick")
            print_info "快速检查..."
            check_containers
            check_ports
            
            if [[ $HEALTH_STATUS -eq 0 ]]; then
                print_success "✅ 服务运行正常"
            else
                print_error "❌ 服务存在问题"
            fi
            
            exit $HEALTH_STATUS
            ;;
            
        *)
            echo "用法: $0 [check|monitor|quick]"
            echo ""
            echo "命令："
            echo "  check    - 完整健康检查 (默认)"
            echo "  monitor  - 实时监控模式"
            echo "  quick    - 快速状态检查"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"