# KGMS Docker Compose Configuration
# 完整的生产环境部署配置

version: '3.8'

services:
  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.26-community
    container_name: kgms-neo4j
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD:-changeme123}
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
      # 内存优化配置 - Neo4j 5.26官方环境变量格式
      - NEO4J_server_memory_heap_initial__size=128m
      - NEO4J_server_memory_heap_max__size=200m
      - NEO4J_server_memory_pagecache_size=80m
    ports:
      - "7474:7474"  # Neo4j Browser
      - "7687:7687"  # Bolt protocol
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - kgms-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:7474 || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 60s

  # FastAPI 后端服务
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: kgms-backend
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-changeme123}
      - NEO4J_LABEL_PREFIX=KGMS
      - ARK_API_KEY=${ARK_API_KEY}
      - BASE_URL=https://ark.cn-beijing.volces.com/api/v3
      - MODEL_ID=doubao-seed-1-6-flash-250715
      - EMBEDDING_MODEL=doubao-embedding-large-text-250515
      - ARK_MAX_RETRIES=3
      - ARK_TIMEOUT=30
      - DEBUG=false
      - ENVIRONMENT=production
      - PYTHONPATH=/app
    volumes:
      - backend_logs:/app/logs
      - sqlite_data:/app
    depends_on:
      - neo4j
    networks:
      - kgms-network
    # 内存限制 - 单进程模式下的内存限制
    deploy:
      resources:
        limits:
          memory: 256m
        reservations:
          memory: 64m
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # React 前端服务
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    container_name: kgms-frontend
    restart: unless-stopped
    ports:
      - "5173:80"
    networks:
      - kgms-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: kgms-nginx
    restart: unless-stopped
    ports:
      - "8001:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend  
      - backend
    networks:
      - kgms-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 5s
      retries: 3

# 数据卷定义
volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local
  neo4j_import:
    driver: local  
  neo4j_plugins:
    driver: local
  backend_logs:
    driver: local
  sqlite_data:
    driver: local
  nginx_logs:
    driver: local

# 网络定义
networks:
  kgms-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16