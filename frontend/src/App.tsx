import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';

// 导入组件
import LoginPage from './components/LoginPage';
import PrivateRoute from './components/PrivateRoute';
import AdminRoute from './components/AdminRoute';
import { AdminLayout } from './components/Layout/AdminLayout';

// 导入页面组件
import { Overview } from './pages/Overview';
import { SmartEntry } from './pages/SmartEntry';
import { TraditionalEntry } from './pages/TraditionalEntry';
import BrandManagement from './pages/BrandManagement';
import IngredientManagement from './pages/IngredientManagement';
import ProductManagement from './pages/ProductManagement';
import UserManagement from './pages/UserManagement';
import VectorizationDashboard from './pages/VectorizationDashboard';
import BatchOperations from './pages/BatchOperations';

import { useAuthStore } from './stores/authStore';
import './App.css';

// 主应用内容组件
const AppContent: React.FC = () => {
  return (
    <Routes>
      {/* 公共路由 */}
      <Route path="/login" element={<LoginPage />} />
      
      {/* 受保护的路由 */}
      <Route path="/" element={
        <PrivateRoute>
          <AdminLayout>
            <Overview />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/smart-entry" element={
        <PrivateRoute>
          <AdminLayout>
            <SmartEntry />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/traditional-entry" element={
        <PrivateRoute>
          <AdminLayout>
            <TraditionalEntry />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/brands" element={
        <PrivateRoute>
          <AdminLayout>
            <BrandManagement />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/ingredients" element={
        <PrivateRoute>
          <AdminLayout>
            <IngredientManagement />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/products" element={
        <PrivateRoute>
          <AdminLayout>
            <ProductManagement />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/vectorization" element={
        <PrivateRoute>
          <AdminLayout>
            <VectorizationDashboard />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      <Route path="/batch-operations" element={
        <PrivateRoute>
          <AdminLayout>
            <BatchOperations />
          </AdminLayout>
        </PrivateRoute>
      } />
      
      {/* 管理员专用路由 */}
      <Route path="/admin/users" element={
        <AdminRoute>
          <AdminLayout>
            <UserManagement />
          </AdminLayout>
        </AdminRoute>
      } />
      
      {/* 默认重定向 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  const { getCurrentUser } = useAuthStore();

  // 应用启动时尝试获取用户信息
  useEffect(() => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      getCurrentUser();
    }
  }, [getCurrentUser]);

  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <AppContent />
      </Router>
    </ConfigProvider>
  );
}

export default App;