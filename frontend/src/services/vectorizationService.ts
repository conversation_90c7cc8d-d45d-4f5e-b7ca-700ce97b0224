import api from './api';

// 向量化任务状态类型
export type VectorizationStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';

// 向量化任务详情
export interface VectorizationTaskDetail {
  task_id: string;
  entity_id: string;
  entity_type: string;
  entity_name: string;
  text: string;
  status: VectorizationStatus;
  priority: number;
  created_at: string;
  updated_at: string;
  scheduled_at: string;
  attempts: number;
  max_attempts: number;
  error_message?: string;
  retry_at?: string;
  force_update: boolean;
}

// 向量化整体状态
export interface VectorizationOverallStatus {
  total_tasks: number;
  processing_count: number;
  queue_size: number;
  total_created: number;
  total_completed: number;
  total_failed: number;
  last_cleanup: string;
  status_counts: Record<string, number>;
}

// 覆盖率统计
export interface VectorizationCoverage {
  entity_type: string;
  total_entities: number;
  vectorized_entities: number;
  pending_entities: number;
  failed_entities: number;
  coverage_percentage: number;
}

// Dashboard概览数据
export interface DashboardOverview {
  summary: {
    total_entities: number;
    vectorized_entities: number;
    overall_coverage: number;
    queue_size: number;
    processing_count: number;
    total_failed: number;
    success_rate: number;
    overall_health: 'healthy' | 'warning' | 'critical';
  };
  coverage_by_type: Array<{
    entity_type: string;
    total: number;
    vectorized: number;
    pending: number;
    failed: number;
    coverage_percentage: number;
  }>;
  recent_activity: {
    total_completed: number;
    total_failed: number;
    last_cleanup: string;
  };
  health_alerts: string[];
}

// 健康状态详情
export interface HealthStatus {
  overall_status: 'healthy' | 'warning' | 'critical';
  issues: string[];
  recommendations: string[];
  last_check: string;
  performance: {
    avg_vectorization_time: number;
    queue_throughput: number;
    success_rate: number;
    active_workers: number;
    queue_backlog: number;
  };
}

// 失败分析
export interface FailureAnalysis {
  entity_type: string;
  total_failures: number;
  common_errors: Array<{ error: string; count: number }>;
  retry_success_rate: number;
  avg_failure_time: number;
}

// 批量操作类型
export type EntityType = 'Brand' | 'Ingredient' | 'Product';
export type BatchOperationType = 'delete' | 'merge' | 'start_vectorization';

// 批量操作请求
export interface BatchOperationRequest {
  entity_type: EntityType;
  operation_type: BatchOperationType;
  entity_ids: string[];
  operation_data?: Record<string, any>;
}

// 批量操作响应
export interface BatchOperationResponse {
  success: boolean;
  processed_count: number;
  success_count: number;
  failed_count: number;
  details: Array<{
    entity_id: string;
    entity_name: string;
    status: 'success' | 'failed' | 'skipped' | 'error';
    message: string;
  }>;
  task_ids?: string[];
  error?: string;
}

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

class VectorizationService {
  private baseUrl = '/api/v2/vectorization';

  // =============================================================================
  // 任务管理方法
  // =============================================================================

  async getOverallStatus(): Promise<ApiResponse<VectorizationOverallStatus>> {
    const response = await api.get<ApiResponse<VectorizationOverallStatus>>(`${this.baseUrl}/status`);
    return response.data;
  }

  async getTasks(params?: {
    status?: VectorizationStatus;
    entity_type?: string;
    page?: number;
    size?: number;
  }): Promise<ApiResponse<VectorizationTaskDetail[]> & {
    total: number;
    page: number;
    size: number;
  }> {
    const response = await api.get<any>(`${this.baseUrl}/tasks`, { params });
    return response.data;
  }

  async retryTask(taskId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await api.post<ApiResponse<any>>(`${this.baseUrl}/retry/${taskId}`);
    return response.data;
  }

  async batchRetry(params?: {
    entity_type?: string;
    max_attempts_exceeded?: boolean;
    limit?: number;
  }): Promise<ApiResponse<{ retried_count: number }>> {
    const response = await api.post<ApiResponse<any>>(`${this.baseUrl}/batch-retry`, params);
    return response.data;
  }

  async deleteTask(taskId: string): Promise<ApiResponse<{ message: string }>> {
    const response = await api.delete<ApiResponse<any>>(`${this.baseUrl}/task/${taskId}`);
    return response.data;
  }

  async clearFailedTasks(): Promise<ApiResponse<{ retried_count: number }>> {
    const response = await api.delete<ApiResponse<any>>(`${this.baseUrl}/clear-failed`);
    return response.data;
  }

  // =============================================================================
  // Dashboard监控方法
  // =============================================================================

  async getDashboardOverview(): Promise<ApiResponse<DashboardOverview>> {
    const response = await api.get<ApiResponse<DashboardOverview>>(`${this.baseUrl}/dashboard/overview`);
    return response.data;
  }

  async getCoverageStats(): Promise<ApiResponse<VectorizationCoverage[]>> {
    const response = await api.get<ApiResponse<VectorizationCoverage[]>>(`${this.baseUrl}/dashboard/coverage`);
    return response.data;
  }

  async getHealthStatus(): Promise<ApiResponse<HealthStatus>> {
    const response = await api.get<ApiResponse<HealthStatus>>(`${this.baseUrl}/dashboard/health`);
    return response.data;
  }

  async getMonitoringReport(): Promise<ApiResponse<any>> {
    const response = await api.get<ApiResponse<any>>(`${this.baseUrl}/dashboard/report`);
    return response.data;
  }

  async getFailureAnalysis(): Promise<ApiResponse<FailureAnalysis[]>> {
    const response = await api.get<ApiResponse<FailureAnalysis[]>>(`${this.baseUrl}/dashboard/failures`);
    return response.data;
  }

  // =============================================================================
  // 健康检查方法
  // =============================================================================

  async checkHealth(): Promise<ApiResponse<{
    status: 'healthy' | 'warning' | 'unhealthy';
    queue_service: string;
    stats?: VectorizationOverallStatus;
    issues?: string[];
  }>> {
    const response = await api.get<ApiResponse<any>>(`${this.baseUrl}/health`);
    return response.data;
  }

  // =============================================================================
  // 批量操作方法
  // =============================================================================

  async batchOperation(request: BatchOperationRequest): Promise<BatchOperationResponse> {
    const response = await api.post<BatchOperationResponse>('/api/v2/entity/batch-operation', request);
    return response.data;
  }
}

// 导出单例实例
export const vectorizationService = new VectorizationService();

// 类型已在文件顶部通过 export interface 导出，无需重复导出