import axios from 'axios';
import type {
  ExtractionRequest,
  ExtractionResponse,
  DuplicationCheckResponse,
  BatchDuplicationRequest,
  BatchDuplicationResponse,
  EntitySearchResponse,
  ProductSaveRequest,
  ProductSaveResponse,
  HealthResponse,
} from '../types/api';

// 动态获取后端 API 地址
const getApiBaseUrl = () => {
  const hostname = window.location.hostname;
  
  // 本地开发环境 (前端开发服务器)
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `http://${hostname}:8080`;
  }
  
  // 生产环境通过Nginx代理，使用相对路径
  return '';
};

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
);

export const apiService = {
  // 健康检查
  async checkHealth(): Promise<HealthResponse> {
    const response = await api.get<HealthResponse>('/health-backend');
    return response.data;
  },

  // ===== Phase 1 API (v1) =====
  
  // AI文本提取 (基础版)
  async extractText(request: ExtractionRequest): Promise<ExtractionResponse> {
    const response = await api.post<ExtractionResponse>('/api/v1/extract', request);
    return response.data;
  },

  // 实体搜索
  async searchEntities(
    type: 'Brand' | 'Ingredient' | 'Product',
    keyword: string,
    limit: number = 5
  ): Promise<EntitySearchResponse> {
    const response = await api.get<EntitySearchResponse>('/api/v1/entities/search', {
      params: { type, keyword, limit },
    });
    return response.data;
  },

  // 保存产品
  async saveProduct(request: ProductSaveRequest): Promise<ProductSaveResponse> {
    const response = await api.post<ProductSaveResponse>('/api/v1/products', request);
    return response.data;
  },

  // 增强版产品保存 - 支持实体链接
  async saveProductEnhanced(request: any): Promise<ProductSaveResponse> {
    const response = await api.post<ProductSaveResponse>('/api/v1/products/enhanced', request);
    return response.data;
  },

  // ===== Phase 2 API (v2) =====
  

  // Phase 2 Week 2: 单实体重复检测
  async checkDuplication(
    entityName: string, 
    entityType: 'Brand' | 'Ingredient'
  ): Promise<DuplicationCheckResponse> {
    const response = await api.post<DuplicationCheckResponse>('/api/v2/check-duplication', {
      entity_name: entityName,
      entity_type: entityType
    });
    return response.data;
  },

  // Phase 2 Week 2: 批量重复检测
  async batchDuplicationCheck(request: BatchDuplicationRequest): Promise<BatchDuplicationResponse> {
    const response = await api.post<BatchDuplicationResponse>('/api/v2/batch-duplication', request);
    return response.data;
  },

  // Phase 2 Week 2: 相似度计算
  async calculateSimilarity(
    text1: string,
    text2: string
  ): Promise<{ success: boolean; data: { similarity_score: number; processing_time: number }; error: string | null }> {
    const response = await api.post('/api/v2/similarity', {
      text1,
      text2
    });
    return response.data;
  },

  // Phase 2 Week 2: 向量化服务信息
  async getEmbeddingInfo(): Promise<{ success: boolean; data: any; error: string | null }> {
    const response = await api.get('/api/v2/embedding-info');
    return response.data;
  },

  // ===== 便捷方法 =====
  

  // Phase 2 优化版: 单次LLM调用完成提取 (移除标准化版本)
  async extractOptimized(request: ExtractionRequest): Promise<any> {
    const response = await api.post('/api/v2/extract-optimized', request);
    return response.data;
  },

  // 智能提取 - 使用优化版接口
  async smartExtract(request: ExtractionRequest): Promise<any> {
    try {
      // 使用优化版接口（移除了标准化）
      const optimizedResponse = await this.extractOptimized(request);
      
      if (optimizedResponse.success && optimizedResponse.data) {
        return {
          success: true,
          data: optimizedResponse.data,
          metadata: optimizedResponse.metadata,
          error: null
        };
      }
      
      throw new Error('优化版接口调用失败');
      
    } catch (optimizedError) {
      // 降级到基础版本
      const basicResult = await this.extractText(request);
      return {
        success: basicResult.success,
        data: basicResult.data,
        confidence: basicResult.confidence,
        error: basicResult.error
      };
    }
  },

  // 智能实体检查 (提取+去重检测)
  async smartEntityCheck(
    entityName: string, 
    entityType: 'Brand' | 'Ingredient'
  ): Promise<{
    searchResults: EntitySearchResponse;
    duplicationCheck: DuplicationCheckResponse | null;
  }> {
    // 并行执行搜索和去重检测
    const [searchResults, duplicationCheck] = await Promise.allSettled([
      this.searchEntities(entityType, entityName),
      this.checkDuplication(entityName, entityType)
    ]);

    return {
      searchResults: searchResults.status === 'fulfilled' ? searchResults.value : { success: false, data: [], error: 'Search failed' },
      duplicationCheck: duplicationCheck.status === 'fulfilled' ? duplicationCheck.value : null
    };
  }
};

export default api;