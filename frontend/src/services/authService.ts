import axios from 'axios';
import type { LoginRequest, LoginResponse, User, UserCreateRequest, UserUpdateRequest, UserListResponse, UserStats } from '../types/auth';

// 动态获取后端 API 地址
const getApiBaseUrl = () => {
  const hostname = window.location.hostname;
  
  // 本地开发环境 (前端开发服务器)
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `http://${hostname}:8080`;
  }
  
  // 生产环境通过Nginx代理，使用相对路径
  return '';
};

const API_BASE_URL = getApiBaseUrl();

// 创建 axios 实例
const authApi = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 自动添加认证 token
authApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
authApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token 过期或无效，清除本地存储
      localStorage.removeItem('auth_token');
      localStorage.removeItem('current_user');
      // 可以在这里触发重定向到登录页
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const authService = {
  // 用户登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await authApi.post('/auth/login', credentials);
    return response.data;
  },

  // 用户登出
  async logout(): Promise<void> {
    await authApi.post('/auth/logout');
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    const response = await authApi.get('/auth/me');
    return response.data;
  },

  // 刷新 token
  async refreshToken(): Promise<{ access_token: string; token_type: string; expires_in: number }> {
    const response = await authApi.post('/auth/refresh');
    return response.data;
  },

  // 获取用户列表（管理员功能）
  async getUsers(params: {
    page?: number;
    size?: number;
    search?: string;
    is_active?: boolean;
    is_admin?: boolean;
  } = {}): Promise<UserListResponse> {
    const response = await authApi.get('/admin/users', { params });
    return response.data;
  },

  // 创建用户（管理员功能）
  async createUser(userData: UserCreateRequest): Promise<User> {
    const response = await authApi.post('/admin/users', userData);
    return response.data;
  },

  // 获取用户详情（管理员功能）
  async getUser(userId: number): Promise<User> {
    const response = await authApi.get(`/admin/users/${userId}`);
    return response.data;
  },

  // 更新用户信息（管理员功能）
  async updateUser(userId: number, userData: UserUpdateRequest): Promise<User> {
    const response = await authApi.put(`/admin/users/${userId}`, userData);
    return response.data;
  },

  // 启用/禁用用户（管理员功能）
  async updateUserStatus(userId: number, isActive: boolean): Promise<User> {
    const response = await authApi.put(`/admin/users/${userId}/status`, { is_active: isActive });
    return response.data;
  },

  // 删除用户（管理员功能）
  async deleteUser(userId: number): Promise<void> {
    await authApi.delete(`/admin/users/${userId}`);
  },

  // 获取用户统计（管理员功能）
  async getUserStats(): Promise<UserStats> {
    const response = await authApi.get('/admin/stats');
    return response.data;
  },
};