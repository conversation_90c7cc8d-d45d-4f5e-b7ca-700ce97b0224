import api from './api';

// 类型定义
interface BrandInfo {
  uuid: string;
  name: string;
  created_at: string;
  updated_at: string;
  product_count: number;
  is_active: boolean;
  // 向量化状态信息
  vectorization_status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  vectorization_attempts: number;
  vectorization_error?: string;
}

interface BrandDetail extends BrandInfo {
  products: string[];
  related_ingredients: string[];
}

interface IngredientInfo {
  uuid: string;
  name: string;
  created_at: string;
  updated_at: string;
  product_count: number;
  is_active: boolean;
  // 向量化状态信息
  vectorization_status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  vectorization_attempts: number;
  vectorization_error?: string;
}

interface IngredientDetail extends IngredientInfo {
  products: string[];
  related_brands: string[];
  total_amount?: number;
  common_unit?: string;
}

interface ProductInfo {
  uuid: string;
  name: string;
  sku?: string;
  product_type: string;
  brand_name: string;
  brand_id: string;
  ingredient_count: number;
  benefits?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  // 向量化状态信息
  vectorization_status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  vectorization_attempts: number;
  vectorization_error?: string;
}

interface ProductDetail extends ProductInfo {
  ingredients: any[];
  brand_info?: { [key: string]: string };
}

// API响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

interface ListResponse<T> extends ApiResponse<T[]> {
  total: number;
  page: number;
  size: number;
}

// 查询参数类型
interface BrandListParams {
  page: number;
  size: number;
  search?: string;
  filter_type: 'all' | 'active' | 'unused' | 'duplicate';
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

interface IngredientListParams {
  page: number;
  size: number;
  search?: string;
  filter_type: 'all' | 'active' | 'unused' | 'duplicate';
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

interface ProductListParams {
  page: number;
  size: number;
  search?: string;
  brand_filter?: string;
  ingredient_filter?: string;
  filter_type: 'all' | 'active' | 'unused' | 'duplicate';
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

class EntityManagementService {
  private baseUrl = '/api/v2/entity';

  // =============================================================================
  // 品牌管理方法
  // =============================================================================

  async getBrands(params: BrandListParams): Promise<ListResponse<BrandInfo>> {
    const response = await api.get<ListResponse<BrandInfo>>(`${this.baseUrl}/brands`, {
      params
    });
    return response.data;
  }

  async getBrandDetail(brandId: string): Promise<ApiResponse<BrandDetail>> {
    const response = await api.get<ApiResponse<BrandDetail>>(`${this.baseUrl}/brands/${brandId}`);
    return response.data;
  }

  async createBrand(data: { name: string }): Promise<ApiResponse<BrandDetail>> {
    const response = await api.post<ApiResponse<BrandDetail>>(`${this.baseUrl}/brands`, data);
    return response.data;
  }

  async updateBrand(brandId: string, data: { name?: string; is_active?: boolean }): Promise<ApiResponse<BrandDetail>> {
    const response = await api.put<ApiResponse<BrandDetail>>(`${this.baseUrl}/brands/${brandId}`, data);
    return response.data;
  }

  async deleteBrand(brandId: string): Promise<ApiResponse<null>> {
    const response = await api.delete<ApiResponse<null>>(`${this.baseUrl}/brands/${brandId}`);
    return response.data;
  }

  // =============================================================================
  // 成分管理方法
  // =============================================================================

  async getIngredients(params: IngredientListParams): Promise<ListResponse<IngredientInfo>> {
    const response = await api.get<ListResponse<IngredientInfo>>(`${this.baseUrl}/ingredients`, {
      params
    });
    return response.data;
  }

  async getIngredientDetail(ingredientId: string): Promise<ApiResponse<IngredientDetail>> {
    const response = await api.get<ApiResponse<IngredientDetail>>(`${this.baseUrl}/ingredients/${ingredientId}`);
    return response.data;
  }

  async createIngredient(data: { name: string }): Promise<ApiResponse<IngredientDetail>> {
    const response = await api.post<ApiResponse<IngredientDetail>>(`${this.baseUrl}/ingredients`, data);
    return response.data;
  }

  async updateIngredient(ingredientId: string, data: { name?: string; is_active?: boolean }): Promise<ApiResponse<IngredientDetail>> {
    const response = await api.put<ApiResponse<IngredientDetail>>(`${this.baseUrl}/ingredients/${ingredientId}`, data);
    return response.data;
  }

  async deleteIngredient(ingredientId: string): Promise<ApiResponse<null>> {
    const response = await api.delete<ApiResponse<null>>(`${this.baseUrl}/ingredients/${ingredientId}`);
    return response.data;
  }

  // =============================================================================
  // 产品管理方法
  // =============================================================================

  async getProducts(params: ProductListParams): Promise<ListResponse<ProductInfo>> {
    const response = await api.get<ListResponse<ProductInfo>>(`${this.baseUrl}/products`, {
      params
    });
    return response.data;
  }

  async getProductDetail(productId: string): Promise<ApiResponse<ProductDetail>> {
    const response = await api.get<ApiResponse<ProductDetail>>(`${this.baseUrl}/products/${productId}`);
    return response.data;
  }

  async createProduct(data: {
    name: string;
    sku?: string;
    product_type: string;
    brand_id: string;
    ingredients: any[];
    benefits?: string;
  }): Promise<ApiResponse<ProductDetail>> {
    const response = await api.post<ApiResponse<ProductDetail>>(`${this.baseUrl}/products`, data);
    return response.data;
  }

  async updateProduct(productId: string, data: {
    name?: string;
    sku?: string;
    product_type?: string;
    brand_id?: string;
    ingredients?: any[];
    benefits?: string;
    is_active?: boolean;
  }): Promise<ApiResponse<ProductDetail>> {
    const response = await api.put<ApiResponse<ProductDetail>>(`${this.baseUrl}/products/${productId}`, data);
    return response.data;
  }

  async deleteProduct(productId: string): Promise<ApiResponse<null>> {
    const response = await api.delete<ApiResponse<null>>(`${this.baseUrl}/products/${productId}`);
    return response.data;
  }

  // =============================================================================
  // 统计信息方法
  // =============================================================================

  async getEntityStats(): Promise<ApiResponse<{
    total_brands: number;
    total_ingredients: number;
    total_products: number;
    active_brands: number;
    active_ingredients: number;
    unused_brands: number;
    unused_ingredients: number;
  }>> {
    const response = await api.get<ApiResponse<any>>(`${this.baseUrl}/stats`);
    return response.data;
  }

  // =============================================================================
  // 批量操作方法
  // =============================================================================

  async batchOperation(data: {
    entity_type: 'Brand' | 'Ingredient' | 'Product';
    operation_type: 'delete' | 'merge' | 'update_status';
    entity_ids: string[];
    operation_data?: any;
  }): Promise<ApiResponse<{
    processed_count: number;
    success_count: number;
    failed_count: number;
    details: any[];
  }>> {
    const response = await api.post<ApiResponse<any>>(`${this.baseUrl}/batch-operation`, data);
    return response.data;
  }
}

// 导出单例实例
export const entityManagementService = new EntityManagementService();

// 导出类型
export type {
  BrandInfo,
  BrandDetail,
  IngredientInfo,
  IngredientDetail,
  ProductInfo,
  ProductDetail,
  BrandListParams,
  IngredientListParams,
  ProductListParams,
  ApiResponse,
  ListResponse
};