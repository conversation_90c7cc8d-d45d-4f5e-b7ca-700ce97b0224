import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { AppState, ProductInfo, DuplicationCheckResponse } from '../types/api';
import { apiService } from '../services/api';

interface AppActions {
  // 提取相关
  extractText: (text: string) => Promise<void>;
  extractBasicText: (text: string) => Promise<void>; // 基础v1提取
  clearExtraction: () => void;
  
  // Phase 2增强功能
  checkEntityDuplication: (entityName: string, entityType: 'Brand' | 'Ingredient') => Promise<DuplicationCheckResponse | null>;
  
  // 表单相关
  setFormData: (data: ProductInfo) => void;
  updateFormField: (field: keyof ProductInfo, value: any) => void;
  saveProduct: () => Promise<void>;
  clearForm: () => void;
  
  // 实体搜索相关
  searchBrands: (keyword: string) => Promise<void>;
  searchIngredients: (keyword: string) => Promise<void>;
  clearSuggestions: () => void;
  
  // 实体链接相关
  showEntityLinkingPanel: () => void;
  hideEntityLinkingPanel: () => void;
  setEntityLinkingResults: (results: any[]) => void;
  
  // 产品保存成功
  setProductSaved: (productName: string) => void;
  clearProductSaved: () => void;
  
  // 重置状态
  resetApp: () => void;
}

const initialState: AppState = {
  extractedData: null,
  isExtracting: false,
  extractionError: null,
  
  formData: null,
  isSaving: false,
  saveError: null,
  
  brandSuggestions: [],
  ingredientSuggestions: [],
  isSearching: false,
  
  
  // 实体链接状态
  showEntityLinking: false,
  entityLinkingResults: [],
  
  // 产品保存成功状态
  productSaved: false,
  savedProductName: null,
};

export const useAppStore = create<AppState & AppActions>()(
  devtools(
    (set, get) => ({
      ...initialState,

          // 简化的提取文本 - 直接使用基础提取
      extractText: async (text: string) => {
        set({ 
          isExtracting: true, 
          extractionError: null,
          extractedData: null,
          formData: null,
          showEntityLinking: false
        });
        
        try {
          // 使用简化的智能提取API (实际调用基础v1接口)
          const response = await apiService.smartExtract({ text });
          
          if (response.success && response.data) {
            set({ 
              extractedData: response.data,
              formData: response.data, // 自动填充到表单
              isExtracting: false 
            });
            
            // 自动触发实体链接面板 - 这是保存前的必须步骤
            // 提取成功后立即显示实体链接面板，无需用户手动点击
            setTimeout(() => {
              set({ showEntityLinking: true });
            }, 300); // 稍作延迟，让用户看到提取结果
          } else {
            set({ 
              extractionError: response.error || '提取失败', 
              isExtracting: false 
            });
          }
        } catch (error) {
          set({ 
            extractionError: error instanceof Error ? error.message : '网络错误', 
            isExtracting: false 
          });
        }
      },
      
      // 基础提取 - Phase 1 v1接口
      extractBasicText: async (text: string) => {
        set({ 
          isExtracting: true, 
          extractionError: null,
          extractedData: null,
          formData: null,
          showEntityLinking: false
        });
        
        try {
          // 使用基础v1提取API
          const response = await apiService.extractText({ text });
          
          if (response.success && response.data) {
            set({ 
              extractedData: response.data,
              formData: response.data, // 自动填充到表单
              isExtracting: false 
            });
          } else {
            set({ 
              extractionError: response.error || '提取失败', 
              isExtracting: false 
            });
          }
        } catch (error) {
          set({ 
            extractionError: error instanceof Error ? error.message : '网络错误', 
            isExtracting: false 
          });
        }
      },

      clearExtraction: () => {
        set({ 
          extractedData: null, 
          extractionError: null,
          showEntityLinking: false,
          entityLinkingResults: []
        });
      },

      // 设置表单数据
      setFormData: (data: ProductInfo) => {
        set({ formData: data });
      },

      // 更新表单字段
      updateFormField: (field: keyof ProductInfo, value: any) => {
        const currentForm = get().formData;
        if (currentForm) {
          set({ 
            formData: { ...currentForm, [field]: value } 
          });
        }
      },

      // 保存产品 - 支持实体链接
      saveProduct: async () => {
        const { formData, entityLinkingResults } = get();
        if (!formData) return;

        set({ isSaving: true, saveError: null });

        try {
          // 检查是否有实体链接结果
          if (entityLinkingResults.length > 0) {
            // 使用实体链接结果构建增强版保存请求
            console.log('使用实体链接结果保存:', entityLinkingResults);
            
            // 构建实体链接映射
            const entityLinkMap = entityLinkingResults.reduce((acc, item) => {
              acc[`${item.extracted_entity.type}:${item.extracted_entity.name}`] = item.selected_option;
              return acc;
            }, {} as Record<string, string>);
            
            // 构建增强版保存请求
            const enhancedRequest = {
              product: {
                name: formData.product_name,
                entity_link: entityLinkMap[`Product:${formData.product_name}`] || 'new'
              },
              product_type: formData.product_type,
              sku: formData.sku,
              benefits: formData.benefits,
              brand: {
                name: formData.brand,
                entity_link: entityLinkMap[`Brand:${formData.brand}`] || 'new'
              },
              ingredients: formData.ingredients.map(ing => ({
                name: ing.name,
                amount: ing.amount,
                unit: ing.unit,
                entity_link: entityLinkMap[`Ingredient:${ing.name}`] || 'new'
              }))
            };
            
            console.log('增强版保存请求:', enhancedRequest);
            
            // 使用增强版API
            const response = await apiService.saveProductEnhanced(enhancedRequest);
            
            if (response.success) {
              set({ isSaving: false });
              // 保存成功，清空实体链接结果
              set({ entityLinkingResults: [] });
              console.log('增强版产品保存成功:', response.data);
              return; // 成功后直接返回，不执行下面的原有API
            } else {
              console.warn('增强版保存失败，回退到原有API:', response.error);
            }
          }
          
          // 使用原有保存接口（向后兼容）
          const response = await apiService.saveProduct({
            product_name: formData.product_name,
            brand: formData.brand,
            product_type: formData.product_type,
            sku: formData.sku || undefined,
            ingredients: formData.ingredients,
            benefits: formData.benefits || undefined,
          });

          if (response.success) {
            set({ isSaving: false });
            // 保存成功，清空实体链接结果
            set({ entityLinkingResults: [] });
            console.log('产品保存成功:', response.data);
          } else {
            set({ 
              saveError: response.error || '保存失败', 
              isSaving: false 
            });
          }
        } catch (error) {
          set({ 
            saveError: error instanceof Error ? error.message : '网络错误', 
            isSaving: false 
          });
        }
      },

      clearForm: () => {
        set({ formData: null, saveError: null });
      },

      // 搜索品牌
      searchBrands: async (keyword: string) => {
        if (keyword.length < 1) {
          set({ brandSuggestions: [] });
          return;
        }

        set({ isSearching: true });

        try {
          const response = await apiService.searchEntities('Brand', keyword);
          
          if (response.success) {
            set({ brandSuggestions: response.data, isSearching: false });
          } else {
            set({ brandSuggestions: [], isSearching: false });
          }
        } catch (error) {
          set({ brandSuggestions: [], isSearching: false });
        }
      },

      // 搜索成分
      searchIngredients: async (keyword: string) => {
        if (keyword.length < 1) {
          set({ ingredientSuggestions: [] });
          return;
        }

        set({ isSearching: true });

        try {
          const response = await apiService.searchEntities('Ingredient', keyword);
          
          if (response.success) {
            set({ ingredientSuggestions: response.data, isSearching: false });
          } else {
            set({ ingredientSuggestions: [], isSearching: false });
          }
        } catch (error) {
          set({ ingredientSuggestions: [], isSearching: false });
        }
      },

      clearSuggestions: () => {
        set({ brandSuggestions: [], ingredientSuggestions: [] });
      },

      // Phase 2: 单独检查实体去重
      checkEntityDuplication: async (entityName: string, entityType: 'Brand' | 'Ingredient') => {
        try {
          return await apiService.checkDuplication(entityName, entityType);
        } catch (error) {
          console.error('去重检查失败:', error);
          return null;
        }
      },
      
      // 实体链接面板控制
      showEntityLinkingPanel: () => {
        set({ showEntityLinking: true });
      },
      
      hideEntityLinkingPanel: () => {
        set({ showEntityLinking: false, entityLinkingResults: [] });
      },
      
      setEntityLinkingResults: (results: any[]) => {
        set({ entityLinkingResults: results, showEntityLinking: false });
      },
      
      // 产品保存成功状态控制
      setProductSaved: (productName: string) => {
        set({ productSaved: true, savedProductName: productName });
      },
      
      clearProductSaved: () => {
        set({ productSaved: false, savedProductName: null });
      },
      
      // 重置应用状态
      resetApp: () => {
        set(initialState);
      },
    }),
    {
      name: 'kgms-app-store',
    }
  )
);