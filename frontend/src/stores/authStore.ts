import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type { User, LoginRequest, UserCreateRequest, UserUpdateRequest, UserStats } from '../types/auth';
import { authService } from '../services/authService';

interface AuthState {
  // 当前用户状态
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
  error: string | null;

  // 用户管理状态
  users: User[];
  userStats: UserStats | null;
  userListLoading: boolean;
  userListError: string | null;
  totalUsers: number;
  currentPage: number;
  pageSize: number;

  // Actions
  login: (credentials: LoginRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  refreshToken: () => Promise<void>;
  
  // 用户管理 Actions
  getUsers: (params?: {
    page?: number;
    size?: number;
    search?: string;
    is_active?: boolean;
    is_admin?: boolean;
  }) => Promise<void>;
  createUser: (userData: UserCreateRequest) => Promise<void>;
  updateUser: (userId: number, userData: UserUpdateRequest) => Promise<void>;
  updateUserStatus: (userId: number, isActive: boolean) => Promise<void>;
  deleteUser: (userId: number) => Promise<void>;
  getUserStats: () => Promise<void>;
  
  // Utility Actions
  clearError: () => void;
  setCurrentPage: (page: number) => void;
  setPageSize: (size: number) => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        token: null,
        isAuthenticated: false,
        isAdmin: false,
        loading: false,
        error: null,

        users: [],
        userStats: null,
        userListLoading: false,
        userListError: null,
        totalUsers: 0,
        currentPage: 1,
        pageSize: 20,

        // Authentication actions
        login: async (credentials: LoginRequest): Promise<boolean> => {
          set({ loading: true, error: null });
          try {
            const response = await authService.login(credentials);
            const { access_token, user } = response;

            // 保存到 localStorage
            localStorage.setItem('auth_token', access_token);
            localStorage.setItem('current_user', JSON.stringify(user));

            set({
              user,
              token: access_token,
              isAuthenticated: true,
              isAdmin: user.is_admin,
              loading: false,
              error: null,
            });

            return true;
          } catch (error: any) {
            const errorMessage = error.response?.data?.detail || '登录失败，请稍后重试';
            set({
              loading: false,
              error: errorMessage,
              user: null,
              token: null,
              isAuthenticated: false,
              isAdmin: false,
            });
            return false;
          }
        },

        logout: async (): Promise<void> => {
          try {
            await authService.logout();
          } catch (error) {
            console.error('Logout error:', error);
          } finally {
            // 清除本地存储
            localStorage.removeItem('auth_token');
            localStorage.removeItem('current_user');

            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isAdmin: false,
              loading: false,
              error: null,
            });
          }
        },

        getCurrentUser: async (): Promise<void> => {
          const token = localStorage.getItem('auth_token');
          if (!token) {
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isAdmin: false,
            });
            return;
          }

          set({ loading: true });
          try {
            const user = await authService.getCurrentUser();
            set({
              user,
              token,
              isAuthenticated: true,
              isAdmin: user.is_admin,
              loading: false,
              error: null,
            });
          } catch (error: any) {
            console.error('Get current user error:', error);
            // Token 可能过期，清除本地存储
            localStorage.removeItem('auth_token');
            localStorage.removeItem('current_user');
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isAdmin: false,
              loading: false,
              error: '登录状态已过期',
            });
          }
        },

        refreshToken: async (): Promise<void> => {
          try {
            const response = await authService.refreshToken();
            localStorage.setItem('auth_token', response.access_token);
            set({ token: response.access_token });
          } catch (error) {
            console.error('Refresh token error:', error);
            get().logout();
          }
        },

        // User management actions
        getUsers: async (params = {}): Promise<void> => {
          set({ userListLoading: true, userListError: null });
          try {
            const response = await authService.getUsers({
              page: get().currentPage,
              size: get().pageSize,
              ...params,
            });
            set({
              users: response.users,
              totalUsers: response.total,
              currentPage: response.page,
              pageSize: response.size,
              userListLoading: false,
              userListError: null,
            });
          } catch (error: any) {
            const errorMessage = error.response?.data?.detail || '获取用户列表失败';
            set({
              userListLoading: false,
              userListError: errorMessage,
            });
          }
        },

        createUser: async (userData: UserCreateRequest): Promise<void> => {
          try {
            await authService.createUser(userData);
            // 重新加载用户列表
            await get().getUsers();
            await get().getUserStats();
          } catch (error: any) {
            const errorMessage = error.response?.data?.detail || '创建用户失败';
            throw new Error(errorMessage);
          }
        },

        updateUser: async (userId: number, userData: UserUpdateRequest): Promise<void> => {
          try {
            await authService.updateUser(userId, userData);
            // 重新加载用户列表
            await get().getUsers();
          } catch (error: any) {
            const errorMessage = error.response?.data?.detail || '更新用户失败';
            throw new Error(errorMessage);
          }
        },

        updateUserStatus: async (userId: number, isActive: boolean): Promise<void> => {
          try {
            await authService.updateUserStatus(userId, isActive);
            // 重新加载用户列表
            await get().getUsers();
            await get().getUserStats();
          } catch (error: any) {
            const errorMessage = error.response?.data?.detail || '更新用户状态失败';
            throw new Error(errorMessage);
          }
        },

        deleteUser: async (userId: number): Promise<void> => {
          try {
            await authService.deleteUser(userId);
            // 重新加载用户列表
            await get().getUsers();
            await get().getUserStats();
          } catch (error: any) {
            const errorMessage = error.response?.data?.detail || '删除用户失败';
            throw new Error(errorMessage);
          }
        },

        getUserStats: async (): Promise<void> => {
          try {
            const stats = await authService.getUserStats();
            set({ userStats: stats });
          } catch (error: any) {
            console.error('Get user stats error:', error);
          }
        },

        // Utility actions
        clearError: () => set({ error: null, userListError: null }),
        setCurrentPage: (page: number) => set({ currentPage: page }),
        setPageSize: (size: number) => set({ pageSize: size }),
      }),
      {
        name: 'auth-store',
        partialize: (state) => ({
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
          isAdmin: state.isAdmin,
        }),
      }
    )
  )
);