// 用户信息类型
export interface User {
  id: number;
  username: string;
  email?: string;
  full_name?: string;
  is_active: boolean;
  is_admin: boolean;
  last_login_at?: string;
  created_at: string;
}

// 登录请求类型
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应类型
export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

// 用户创建请求类型
export interface UserCreateRequest {
  username: string;
  email?: string;
  password: string;
  full_name?: string;
  is_admin: boolean;
}

// 用户更新请求类型
export interface UserUpdateRequest {
  email?: string;
  full_name?: string;
  is_active?: boolean;
  is_admin?: boolean;
  password?: string;
}

// 用户列表响应类型
export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  size: number;
}

// 用户统计类型
export interface UserStats {
  total_users: number;
  active_users: number;
  inactive_users: number;
  admin_users: number;
  regular_users: number;
}