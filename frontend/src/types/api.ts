// API相关类型定义

export interface Ingredient {
  name: string;
  amount?: number | null;
  unit?: string | null;
  ingredient_id?: string | null;
}

export interface ProductInfo {
  product_name: string;
  brand: string;
  product_type: string;
  sku?: string | null;
  ingredients: Ingredient[];
  benefits?: string | null;
  brand_id?: string | null;
}

export interface ExtractionRequest {
  text: string;
}

export interface ExtractionResponse {
  success: boolean;
  data: ProductInfo | null;
  confidence: number;
  error: string | null;
}

// Phase 2 Week 1: LLM标准化提取响应
export interface StandardizedExtractionResponse {
  success: boolean;
  data: {
    original_extraction: ProductInfo;
    standardized_extraction: ProductInfo;
    standardization_details: {
      brand_standardized: boolean;
      brand_original?: string;
      brand_standardized_name?: string;
      ingredients_standardized: {
        original: string;
        standardized: string;
        confidence: number;
      }[];
    };
    processing_time: number;
  };
  error: string | null;
}

// Phase 2 Week 2: 去重检测响应
export interface DuplicationCheckResponse {
  success: boolean;
  data: {
    entity_name: string;
    entity_type: 'Brand' | 'Ingredient';
    has_duplicates: boolean;
    duplicate_candidates: {
      entity_id: string;
      entity_name: string;
      similarity_score: number;
      confidence_level: 'HIGH' | 'MEDIUM' | 'LOW';
      recommendation: 'MERGE' | 'KEEP_SEPARATE' | 'MANUAL_REVIEW';
    }[];
    duplication_level: 'HIGH' | 'MEDIUM' | 'LOW' | 'NONE';
    processing_time: number;
  };
  error: string | null;
}

// 批量去重检测请求
export interface BatchDuplicationRequest {
  entities: {
    entity_name: string;
    entity_type: 'Brand' | 'Ingredient';
  }[];
}

// 批量去重检测响应
export interface BatchDuplicationResponse {
  success: boolean;
  data: {
    results: DuplicationCheckResponse['data'][];
    summary: {
      total_entities: number;
      duplicates_found: number;
      high_confidence: number;
      medium_confidence: number;
      low_confidence: number;
    };
  };
  error: string | null;
}

export interface Entity {
  id: string;
  name: string;
  type: 'Brand' | 'Ingredient' | 'Product';
  match_score: number;
  // 产品专属属性
  product_type?: string;
  sku?: string;
  benefits?: string;
}

// 实体编辑数据接口
export interface EntityEditingData {
  name?: string;
  // 产品特有字段
  sku?: string;
  product_type?: string;
  benefits?: string;
  // 成分特有字段  
  amount?: number | null;
  unit?: string;
}

// 实体链接候选项
export interface EntityLinkingOption {
  id: string;
  name: string;
  type: 'Brand' | 'Ingredient' | 'Product';
  similarity_score: number;
  match_reason?: string;
  // 产品专属属性
  product_type?: string;
  sku?: string;
  benefits?: string;
}

// 实体链接数据 - 增强版
export interface EntityLinkingData {
  extracted_entity: {
    name: string;
    type: 'Brand' | 'Ingredient' | 'Product';
    // 原始提取数据，用于编辑时的默认值
    original_data?: {
      sku?: string;
      product_type?: string;
      amount?: number;
      unit?: string;
    };
  };
  candidates: EntityLinkingOption[];
  selected_option: 'new' | string; // 'new' 表示创建新实体，否则是候选实体的ID
  status: 'pending' | 'selected' | 'confirmed' | 'searching';
  // 编辑数据（用户修改后的数据）
  editing_data?: EntityEditingData;
  // 验证状态
  validation_errors?: string[];
}

export interface EntitySearchResponse {
  success: boolean;
  data: Entity[];
  error: string | null;
}

export interface ProductSaveRequest {
  product_name: string;
  brand: string;
  product_type: string;
  sku?: string;
  ingredients: Ingredient[];
  benefits?: string;
}

// 增强版产品保存请求 - 支持实体链接
export interface EnhancedProductSaveRequest {
  // 产品信息
  product: {
    name: string;
    type: string;
    sku?: string;
    benefits?: string;
    // 实体链接选择：'new' 或现有实体ID
    entity_link: 'new' | string;
  };
  
  // 品牌链接
  brand: {
    name: string;
    entity_link: 'new' | string;
  };
  
  // 成分链接列表
  ingredients: {
    name: string;
    amount?: number | null;
    unit?: string | null;
    entity_link: 'new' | string;
  }[];
}

export interface ProductSaveResponse {
  success: boolean;
  data: {
    product_id: string;
    message: string;
  } | null;
  error: string | null;
}

export interface HealthResponse {
  status: string;
  project: string;
  version: string;
  services: {
    neo4j: string;
  };
}

// 前端状态类型
export interface AppState {
  // 提取状态
  extractedData: ProductInfo | null;
  isExtracting: boolean;
  extractionError: string | null;
  
  // 表单状态
  formData: ProductInfo | null;
  isSaving: boolean;
  saveError: string | null;
  
  // 实体搜索状态
  brandSuggestions: Entity[];
  ingredientSuggestions: Entity[];
  isSearching: boolean;
  
  
  // 实体链接状态
  showEntityLinking: boolean;
  entityLinkingResults: any[];
  
  // 产品保存成功状态
  productSaved: boolean;
  savedProductName: string | null;
}