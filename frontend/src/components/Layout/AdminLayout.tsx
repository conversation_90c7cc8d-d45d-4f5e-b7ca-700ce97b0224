import React, { useState } from 'react';
import { Menu, Button, Typography, <PERSON>, Badge, Dropdown, Avatar, message } from 'antd';
import type { ItemType } from 'antd/es/menu/interface';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  RobotOutlined,
  FormOutlined,
  HistoryOutlined,
  SettingOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  HomeOutlined,
  ShopOutlined,
  ExperimentOutlined,
  ShoppingOutlined,
  DatabaseOutlined,
  UserOutlined,
  TeamOutlined,
  LogoutOutlined,
  MonitorOutlined,
  ControlOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../../stores/authStore';

const { Title } = Typography;

interface AdminLayoutProps {
  children: React.ReactNode;
}

export const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, isAdmin, logout } = useAuthStore();

  // 菜单项配置
  const menuItems: ItemType[] = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '概览',
    },
    {
      key: '/smart-entry',
      icon: <RobotOutlined />,
      label: '智能录入',
    },
    {
      key: '/traditional-entry',
      icon: <FormOutlined />,
      label: '传统录入',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'entity-management',
      icon: <DatabaseOutlined />,
      label: '实体管理',
      children: [
        {
          key: '/brands',
          icon: <ShopOutlined />,
          label: '品牌管理',
        },
        {
          key: '/ingredients',
          icon: <ExperimentOutlined />,
          label: '成分管理',
        },
        {
          key: '/products',
          icon: <ShoppingOutlined />,
          label: '产品管理',
        },
      ],
    },
    {
      type: 'divider' as const,
    },
    {
      key: '/vectorization',
      icon: <MonitorOutlined />,
      label: '向量化监控',
    },
    {
      key: '/batch-operations',
      icon: <ControlOutlined />,
      label: '批量操作',
    },
    // 管理员专用菜单
    ...(isAdmin ? [
      {
        type: 'divider' as const,
      },
      {
        key: 'admin',
        icon: <UserOutlined />,
        label: '系统管理',
        children: [
          {
            key: '/admin/users',
            icon: <TeamOutlined />,
            label: '用户管理',
          },
        ],
      },
    ] : []),
    {
      type: 'divider' as const,
    },
    {
      key: '/history',
      icon: <HistoryOutlined />,
      label: '录入历史',
      disabled: true, // 暂未开发
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      disabled: true, // 暂未开发
    },
  ];

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  // 处理用户菜单点击
  const handleUserMenuClick = async ({ key }: { key: string }) => {
    switch (key) {
      case 'logout':
        try {
          await logout();
          message.success('登出成功');
          navigate('/login');
        } catch (error) {
          console.error('Logout error:', error);
          message.error('登出失败');
        }
        break;
    }
  };

  // 用户下拉菜单
  const userMenuItems: ItemType[] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      disabled: true,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  return (
    <div style={{ display: 'flex', minHeight: '100vh' }}>
      {/* 左侧菜单 */}
      <div
        style={{
          width: collapsed ? 80 : 200,
          background: '#001529',
          position: 'fixed',
          left: 0,
          top: 0,
          height: '100vh',
          zIndex: 1000,
          overflow: 'auto',
          transition: 'width 0.2s',
        }}
      >
        <div
          style={{
            height: 32,
            margin: 16,
            background: 'rgba(255, 255, 255, 0.3)',
            borderRadius: 6,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontWeight: 'bold',
          }}
        >
          {collapsed ? 'K' : 'KGMS'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </div>
      
      {/* 右侧内容区域 */}
      <div
        style={{
          flex: 1,
          marginLeft: collapsed ? 80 : 200,
          display: 'flex',
          flexDirection: 'column',
          transition: 'margin-left 0.2s',
        }}
      >
        {/* 头部 */}
        <div
          style={{
            background: '#fff',
            padding: '0 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
            height: 64,
          }}
        >
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
              }}
            />
            <Title level={4} style={{ margin: 0 }}>
              知识图谱管理系统
            </Title>
          </Space>
          
          <Space>
            <Badge count="v2.0" style={{ backgroundColor: '#52c41a' }} />
            
            {/* 用户信息和下拉菜单 */}
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer', padding: '8px 12px', borderRadius: 6 }}>
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />} 
                  style={{ backgroundColor: '#1890ff' }}
                />
                <span>{user?.full_name || user?.username}</span>
                {isAdmin && (
                  <Badge 
                    count="管理员" 
                    style={{ 
                      backgroundColor: '#f50',
                      fontSize: '10px',
                      height: '16px',
                      lineHeight: '14px',
                      borderRadius: '8px',
                    }} 
                  />
                )}
              </Space>
            </Dropdown>
          </Space>
        </div>
        
        {/* 主内容区域 */}
        <div
          style={{
            flex: 1,
            margin: 24,
            padding: 24,
            background: '#fff',
            borderRadius: 8,
            overflow: 'auto',
          }}
        >
          {children}
        </div>
      </div>
    </div>
  );
};