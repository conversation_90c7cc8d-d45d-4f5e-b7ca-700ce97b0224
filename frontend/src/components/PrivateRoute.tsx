import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthStore } from '../stores/authStore';

interface PrivateRouteProps {
  children: React.ReactNode;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, loading, getCurrentUser } = useAuthStore();

  useEffect(() => {
    // 如果有 token 但没有用户信息，尝试获取用户信息
    const token = localStorage.getItem('auth_token');
    if (token && !isAuthenticated && !loading) {
      getCurrentUser();
    }
  }, [isAuthenticated, loading, getCurrentUser]);

  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
      }}>
        <Spin size="large" tip="正在验证身份..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    // 重定向到登录页面，并保存当前路径
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default PrivateRoute;