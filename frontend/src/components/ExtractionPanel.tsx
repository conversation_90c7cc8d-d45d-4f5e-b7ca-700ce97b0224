import React, { useState } from 'react';
import { Card, Input, Button, Alert, Typography, Space, Divider, Tag } from 'antd';
import { RobotOutlined, FileTextOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useAppStore } from '../stores/appStore';

const { TextArea } = Input;
const { Title, Text } = Typography;

export const ExtractionPanel: React.FC = () => {
  const [inputText, setInputText] = useState('');
  
  const {
    extractedData,
    isExtracting,
    extractionError,
    extractBasicText,
    clearExtraction,
  } = useAppStore();

  const handleExtract = () => {
    if (inputText.trim()) {
      extractBasicText(inputText.trim()); // 使用基础提取 (v1/extract)
    }
  };

  const handleClear = () => {
    setInputText('');
    clearExtraction();
  };

  const sampleTexts = [
    'Nature\'s Bounty维生素C 1000mg，每片含1000毫克维生素C。具有增强免疫力、抗氧化的功效。产品编号：NB-VC-1000。适合成人每日一片。',
    '汤臣倍健蛋白粉，主要成分包含大豆蛋白质25g、乳清蛋白5g。SKU：TB-PROTEIN-400。适合健身人群，增强体质。',
    '善存多维元素片，含维生素A 800微克、维生素C 100毫克、维生素D3 10微克、钙 200毫克、铁 18毫克。产品型号：CS-MULTI-100。每日一片，补充多种营养。'
  ];

  return (
    <Card 
      title={
        <Space>
          <RobotOutlined />
          <span>AI智能提取</span>
        </Space>
      }
      extra={
        <Button type="link" onClick={handleClear} disabled={isExtracting}>
          清空
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <div>
          <Text type="secondary">
            <FileTextOutlined /> 粘贴产品描述文本，AI将自动提取结构化信息
          </Text>
        </div>

        <TextArea
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          placeholder="请粘贴产品描述文本，例如：&#10;Nature's Bounty维生素C 1000mg，每片含1000毫克维生素C。具有增强免疫力、抗氧化的功效..."
          rows={6}
          maxLength={10000}
          showCount
          disabled={isExtracting}
        />

        <div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            示例文本（点击使用）：
          </Text>
          <div style={{ marginTop: 8 }}>
            {sampleTexts.map((text, index) => (
              <Button
                key={index}
                type="link"
                size="small"
                onClick={() => setInputText(text)}
                style={{ 
                  height: 'auto', 
                  padding: '4px 8px', 
                  textAlign: 'left',
                  whiteSpace: 'normal',
                  display: 'block',
                  marginBottom: 4
                }}
              >
                示例 {index + 1}: {text.slice(0, 50)}...
              </Button>
            ))}
          </div>
        </div>

        <Button
          type="primary"
          size="large"
          icon={<RobotOutlined />}
          onClick={handleExtract}
          loading={isExtracting}
          disabled={!inputText.trim()}
          block
        >
          {isExtracting ? '正在提取中...' : '开始AI提取'}
        </Button>

        {extractionError && (
          <Alert
            message="提取失败"
            description={extractionError}
            type="error"
            showIcon
            closable
          />
        )}

        {extractedData && (
          <>
            <Divider />
            <div>
              <Space align="center" style={{ marginBottom: 16 }}>
                <Title level={5} style={{ margin: 0 }}>✅ AI提取结果</Title>
                <Tag icon={<CheckCircleOutlined />} color="blue">
                  传统提取
                </Tag>
              </Space>
              
              <Card size="small" style={{ backgroundColor: '#f6ffed' }}>
                <Space direction="vertical" style={{ width: '100%' }} size="small">
                  <div>
                    <Space>
                      <Text strong>产品名称:</Text> 
                      <span>{extractedData.product_name}</span>
                    </Space>
                  </div>
                  <div>
                    <Space>
                      <Text strong>品牌:</Text> 
                      <span>{extractedData.brand}</span>
                    </Space>
                  </div>
                  <div><Text strong>产品类型:</Text> {extractedData.product_type}</div>
                  {extractedData.sku && (
                    <div><Text strong>SKU:</Text> {extractedData.sku}</div>
                  )}
                  <div>
                    <Text strong>成分:</Text>
                    <ul style={{ margin: '4px 0', paddingLeft: 20 }}>
                      {extractedData.ingredients.map((ingredient, index) => (
                        <li key={index}>
                          <Space size="small">
                            <span>{ingredient.name}</span>
                            {ingredient.amount && ingredient.unit && (
                              <Text type="secondary">({ingredient.amount}{ingredient.unit})</Text>
                            )}
                          </Space>
                        </li>
                      ))}
                    </ul>
                  </div>
                  {extractedData.benefits && (
                    <div><Text strong>功效:</Text> {extractedData.benefits}</div>
                  )}
                </Space>
              </Card>
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  ✨ 基础AI提取完成，数据已自动填充到右侧表单，请核对并提交保存
                </Text>
              </div>
            </div>
          </>
        )}
      </Space>
    </Card>
  );
};