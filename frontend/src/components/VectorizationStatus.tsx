import React from 'react';
import { Tag, Tooltip, Button, Space } from 'antd';
import { 
  ClockCircleOutlined, 
  LoadingOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined,
  ReloadOutlined 
} from '@ant-design/icons';
import type { VectorizationStatus as VectorizationStatusType } from '../services/vectorizationService';

interface VectorizationStatusProps {
  status: VectorizationStatusType;
  attempts?: number;
  maxAttempts?: number;
  error?: string;
  onRetry?: () => void;
  showRetryButton?: boolean;
  size?: 'small' | 'default';
}

const VectorizationStatus: React.FC<VectorizationStatusProps> = ({
  status,
  attempts = 0,
  maxAttempts = 3,
  error,
  onRetry,
  showRetryButton = true,
  size = 'default'
}) => {
  const getStatusConfig = (status: VectorizationStatusType) => {
    switch (status) {
      case 'PENDING':
        return {
          color: 'default',
          icon: <ClockCircleOutlined />,
          text: '等待向量化',
          description: '实体已创建，等待向量化处理'
        };
      case 'PROCESSING':
        return {
          color: 'processing',
          icon: <LoadingOutlined />,
          text: '向量化中',
          description: '正在生成向量数据，请稍候'
        };
      case 'COMPLETED':
        return {
          color: 'success',
          icon: <CheckCircleOutlined />,
          text: '已完成',
          description: '向量化已完成，支持语义搜索'
        };
      case 'FAILED':
        return {
          color: 'error',
          icon: <ExclamationCircleOutlined />,
          text: '失败',
          description: error || '向量化失败，可以重试'
        };
    }
  };

  const config = getStatusConfig(status);
  const canRetry = status === 'FAILED' && attempts < maxAttempts;
  
  const statusElement = (
    <Tag 
      color={config.color} 
      icon={config.icon}
      style={{ fontSize: size === 'small' ? '12px' : '14px' }}
    >
      {config.text}
      {attempts > 0 && ` (${attempts}/${maxAttempts})`}
    </Tag>
  );

  const content = (
    <div>
      <div>{config.description}</div>
      {attempts > 0 && (
        <div style={{ marginTop: 4, fontSize: 12, opacity: 0.8 }}>
          重试次数: {attempts}/{maxAttempts}
        </div>
      )}
      {error && status === 'FAILED' && (
        <div style={{ marginTop: 4, fontSize: 12, color: '#ff4d4f' }}>
          错误信息: {error}
        </div>
      )}
    </div>
  );

  if (showRetryButton && canRetry && onRetry) {
    return (
      <Space size="small">
        <Tooltip title={content}>
          {statusElement}
        </Tooltip>
        <Button 
          type="text" 
          size="small" 
          icon={<ReloadOutlined />}
          onClick={onRetry}
          title="重试向量化"
        />
      </Space>
    );
  }

  return (
    <Tooltip title={content}>
      {statusElement}
    </Tooltip>
  );
};

export default VectorizationStatus;