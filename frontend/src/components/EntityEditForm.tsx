import React from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Space,
  Typography,
  Alert
} from 'antd';
import type { EntityEditingData } from '../types/api';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface EntityEditFormProps {
  entityType: 'Brand' | 'Ingredient' | 'Product';
  editingData: EntityEditingData;
  validationErrors?: string[];
  onChange: (data: EntityEditingData) => void;
  disabled?: boolean;
}

// 常用单位选项
const UNIT_OPTIONS = [
  { value: '毫克', label: '毫克 (mg)' },
  { value: '微克', label: '微克 (μg)' },
  { value: '克', label: '克 (g)' },
  { value: 'IU', label: '国际单位 (IU)' },
  { value: '片', label: '片' },
  { value: '粒', label: '粒' },
  { value: '滴', label: '滴' },
  { value: '%', label: '百分比 (%)' }
];

// 产品类型选项
const PRODUCT_TYPE_OPTIONS = [
  { value: '保健品', label: '保健品' },
  { value: '营养品', label: '营养品' },
  { value: '药品', label: '药品' },
  { value: '食品', label: '食品' },
  { value: '化妆品', label: '化妆品' },
  { value: '其他', label: '其他' }
];

export const EntityEditForm: React.FC<EntityEditFormProps> = ({
  entityType,
  editingData,
  validationErrors = [],
  onChange,
  disabled = false
}) => {
  // 更新编辑数据
  const handleChange = (field: keyof EntityEditingData, value: any) => {
    onChange({
      ...editingData,
      [field]: value
    });
  };

  // 验证名称
  const validateName = (name: string): boolean => {
    if (!name || name.trim().length === 0) return false;
    if (name.trim().length > 100) return false;
    return true;
  };

  return (
    <div style={{ 
      padding: '12px', 
      backgroundColor: '#fafafa', 
      borderRadius: '6px',
      border: '1px solid #f0f0f0'
    }}>
      <Text type="secondary" style={{ fontSize: '12px', marginBottom: '8px', display: 'block' }}>
        📝 编辑新{entityType === 'Brand' ? '品牌' : entityType === 'Ingredient' ? '成分' : '产品'}信息
      </Text>

      <Form layout="vertical" size="small">
        {/* 名称字段 - 所有实体类型都有 */}
        <Form.Item 
          label={`${entityType === 'Brand' ? '品牌' : entityType === 'Ingredient' ? '成分' : '产品'}名称`}
          validateStatus={!validateName(editingData.name || '') ? 'error' : ''}
          help={!validateName(editingData.name || '') ? '名称不能为空，且长度不超过100字符' : ''}
          style={{ marginBottom: '12px' }}
        >
          <Input
            value={editingData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            placeholder={`请输入${entityType === 'Brand' ? '品牌' : entityType === 'Ingredient' ? '成分' : '产品'}名称`}
            maxLength={100}
            showCount
            disabled={disabled}
          />
        </Form.Item>

        {/* 产品特有字段 */}
        {entityType === 'Product' && (
          <>
            <Form.Item label="SKU编号" style={{ marginBottom: '12px' }}>
              <Input
                value={editingData.sku || ''}
                onChange={(e) => handleChange('sku', e.target.value)}
                placeholder="产品SKU编号（可选）"
                maxLength={50}
                disabled={disabled}
              />
            </Form.Item>
            
            <Form.Item label="产品类型" style={{ marginBottom: '12px' }}>
              <Select
                value={editingData.product_type || '保健品'}
                onChange={(value) => handleChange('product_type', value)}
                style={{ width: '100%' }}
                disabled={disabled}
              >
                {PRODUCT_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            
            <Form.Item label="产品功效" style={{ marginBottom: '12px' }}>
              <TextArea
                value={editingData.benefits || ''}
                onChange={(e) => handleChange('benefits', e.target.value)}
                placeholder="描述产品的主要功效和作用（可选）"
                rows={3}
                maxLength={500}
                showCount
                disabled={disabled}
              />
            </Form.Item>
          </>
        )}

        {/* 成分特有字段 */}
        {entityType === 'Ingredient' && (
          <Space.Compact style={{ display: 'flex', marginBottom: '12px' }}>
            <Form.Item 
              label="含量" 
              style={{ flex: 1, marginBottom: 0, marginRight: '8px' }}
            >
              <InputNumber
                value={editingData.amount}
                onChange={(value) => handleChange('amount', value)}
                placeholder="含量"
                min={0}
                precision={2}
                style={{ width: '100%' }}
                disabled={disabled}
              />
            </Form.Item>
            
            <Form.Item 
              label="单位" 
              style={{ width: '120px', marginBottom: 0 }}
            >
              <Select
                value={editingData.unit || '毫克'}
                onChange={(value) => handleChange('unit', value)}
                style={{ width: '100%' }}
                showSearch
                optionFilterProp="children"
                disabled={disabled}
              >
                {UNIT_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Space.Compact>
        )}
      </Form>

      {/* 验证错误提示 */}
      {validationErrors.length > 0 && (
        <Alert
          message="输入验证错误"
          description={
            <ul style={{ margin: 0, paddingLeft: '16px' }}>
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          }
          type="error"
          style={{ marginTop: '8px' }}
          showIcon
        />
      )}
    </div>
  );
};