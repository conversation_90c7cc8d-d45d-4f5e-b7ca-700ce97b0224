import React, { useState } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Alert, 
  Typography, 
  Space,
  Tag, 
  Tooltip
} from 'antd';
import { 
  RobotOutlined, 
  FileTextOutlined, 
  CheckCircleOutlined
} from '@ant-design/icons';
import { useAppStore } from '../stores/appStore';

const { TextArea } = Input;
const { Text } = Typography;

export const EnhancedExtractionPanel: React.FC = () => {
  const [inputText, setInputText] = useState('');
  
  const {
    extractedData,
    isExtracting,
    extractionError,
    extractText,
    clearExtraction,
  } = useAppStore();

  const handleExtract = () => {
    if (inputText.trim()) {
      extractText(inputText.trim());
    }
  };

  const handleClear = () => {
    setInputText('');
    clearExtraction();
  };


  const sampleTexts = [
    'Nature\'s Bounty维生素C 1000mg，每片含1000毫克维生素C。具有增强免疫力、抗氧化的功效。产品编号：NB-VC-1000。适合成人每日一片。',
    '汤臣倍健蛋白粉，主要成分包含大豆蛋白质25g、乳清蛋白5g。SKU：TB-PROTEIN-400。适合健身人群，增强体质。',
    '善存多维元素片，含维生素A 800微克、维生素C 100毫克、维生素D3 10微克、钙 200毫克、铁 18毫克。产品型号：CS-MULTI-100。每日一片，补充多种营养。'
  ];

  return (
    <>
      <Card 
        title={
          <Space>
            <RobotOutlined />
            <span>AI智能提取</span>
            <Tag color="blue">
              智能提取
            </Tag>
          </Space>
        }
        extra={
          <Button type="link" onClick={handleClear} disabled={isExtracting}>
            清空
          </Button>
        }
      >
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <div>
            <Text type="secondary">
              <FileTextOutlined /> 粘贴产品描述文本，AI将自动提取信息
            </Text>
          </div>

          <TextArea
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请粘贴产品描述文本，例如：&#10;Nature's Bounty维生素C 1000mg，每片含1000毫克维生素C。具有增强免疫力、抗氧化的功效..."
            rows={6}
            maxLength={10000}
            showCount
            disabled={isExtracting}
          />

          <div>
            <Space wrap>
              <Button 
                type="primary" 
                icon={<RobotOutlined />}
                loading={isExtracting}
                onClick={handleExtract}
                disabled={!inputText.trim()}
              >
                {isExtracting ? '提取中...' : '开始AI提取'}
              </Button>
              
            </Space>
          </div>

          {/* 示例文本 */}
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <Tooltip title="点击示例文本快速填入">
                💡 示例文本（点击使用）：
              </Tooltip>
            </Text>
            <div style={{ marginTop: 8 }}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                {sampleTexts.map((sample, index) => (
                  <div 
                    key={index}
                    style={{ 
                      padding: '8px 12px',
                      background: '#f5f5f5',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      border: '1px solid #e8e8e8',
                      transition: 'all 0.2s'
                    }}
                    onMouseOver={(e) => {
                      e.currentTarget.style.background = '#e6f7ff';
                      e.currentTarget.style.borderColor = '#1890ff';
                    }}
                    onMouseOut={(e) => {
                      e.currentTarget.style.background = '#f5f5f5';
                      e.currentTarget.style.borderColor = '#e8e8e8';
                    }}
                    onClick={() => setInputText(sample)}
                  >
                    {sample}
                  </div>
                ))}
              </Space>
            </div>
          </div>

          {extractionError && (
            <Alert
              message="提取失败"
              description={extractionError}
              type="error"
              showIcon
              closable
            />
          )}

          {extractedData && (
            <Card
              title={
                <Space>
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  <span>提取结果</span>
                </Space>
              }
              size="small"
              style={{ background: '#f6ffed', border: '1px solid #b7eb8f' }}
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div>
                  <Text strong>产品名称：</Text>
                  <Text>{extractedData.product_name || '未提取到'}</Text>
                </div>
                <div>
                  <Text strong>品牌：</Text>
                  <Text>{extractedData.brand || '未提取到'}</Text>
                </div>
                <div>
                  <Text strong>产品类型：</Text>
                  <Text>{extractedData.product_type || '未提取到'}</Text>
                </div>
                {extractedData.sku && (
                  <div>
                    <Text strong>SKU：</Text>
                    <Text>{extractedData.sku}</Text>
                  </div>
                )}
                {extractedData.ingredients && extractedData.ingredients.length > 0 && (
                  <div>
                    <Text strong>成分：</Text>
                    <div style={{ marginTop: 4 }}>
                      <Space wrap size="small">
                        {extractedData.ingredients.map((ing, index) => (
                          <Tag key={index} color="blue">
                            {ing.name}
                            {ing.amount && ing.unit && ` ${ing.amount}${ing.unit}`}
                          </Tag>
                        ))}
                      </Space>
                    </div>
                  </div>
                )}
                {extractedData.benefits && (
                  <div>
                    <Text strong>功效：</Text>
                    <Text>{extractedData.benefits}</Text>
                  </div>
                )}
              </Space>
            </Card>
          )}
        </Space>
      </Card>

      {/* 实体链接面板现在移到了右侧栏，不再包含在这里 */}
    </>
  );
};