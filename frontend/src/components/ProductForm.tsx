import React, { useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Alert, 
  Divider,
  Row,
  Col,
  InputNumber,
  Tag,
  message
} from 'antd';
import { SaveOutlined, PlusOutlined, DeleteOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useAppStore } from '../stores/appStore';
import { EntitySelector } from './EntitySelector';

const { TextArea } = Input;

export const ProductForm: React.FC = () => {
  const [form] = Form.useForm();
  
  const {
    formData,
    isSaving,
    saveError,
    setFormData,
    updateFormField,
    saveProduct,
    clearForm,
  } = useAppStore();

  // 当formData变化时更新表单
  useEffect(() => {
    if (formData) {
      form.setFieldsValue(formData);
    }
  }, [formData, form]);

  // 保存表单
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setFormData(values);
      await saveProduct();
      message.success('产品保存成功！');
      // 可以选择是否清空表单
      // clearForm();
      // form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    clearForm();
  };

  // 添加成分
  const addIngredient = () => {
    const ingredients = form.getFieldValue('ingredients') || [];
    const newIngredients = [...ingredients, { name: '', amount: null, unit: 'mg' }];
    form.setFieldsValue({ ingredients: newIngredients });
    updateFormField('ingredients', newIngredients);
  };

  // 删除成分
  const removeIngredient = (index: number) => {
    const ingredients = form.getFieldValue('ingredients') || [];
    const newIngredients = ingredients.filter((_: any, i: number) => i !== index);
    form.setFieldsValue({ ingredients: newIngredients });
    updateFormField('ingredients', newIngredients);
  };

  // 表单字段变化处理
  const handleFieldChange = (field: string, value: any) => {
    updateFormField(field as any, value);
  };

  return (
    <Card 
      title={
        <Space>
          <CheckCircleOutlined />
          <span>产品信息录入</span>
        </Space>
      }
      extra={
        <Space>
          <Button onClick={handleReset} disabled={isSaving}>
            重置
          </Button>
          <Button 
            type="primary" 
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={isSaving}
          >
            保存产品
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          product_type: '保健品',
          ingredients: [{ name: '', amount: null, unit: 'mg' }]
        }}
        onValuesChange={(changedValues, allValues) => {
          // 实时更新store中的数据
          Object.keys(changedValues).forEach(key => {
            handleFieldChange(key, allValues[key]);
          });
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="产品名称"
              name="product_name"
              rules={[
                { required: true, message: '请输入产品名称' },
                { min: 2, max: 100, message: '产品名称长度应在2-100字符之间' }
              ]}
            >
              <Input placeholder="请输入产品名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="品牌"
              name="brand"
              rules={[
                { required: true, message: '请输入品牌名称' },
                { min: 2, max: 50, message: '品牌名称长度应在2-50字符之间' }
              ]}
            >
              <EntitySelector 
                type="Brand" 
                placeholder="输入品牌名称，支持自动完成"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="产品类型"
              name="product_type"
              rules={[{ required: true, message: '请选择产品类型' }]}
            >
              <Select placeholder="请选择产品类型">
                <Select.Option value="保健品">保健品</Select.Option>
                <Select.Option value="营养品">营养品</Select.Option>
                <Select.Option value="维生素">维生素</Select.Option>
                <Select.Option value="矿物质">矿物质</Select.Option>
                <Select.Option value="蛋白粉">蛋白粉</Select.Option>
                <Select.Option value="其他">其他</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="SKU (可选)"
              name="sku"
              rules={[
                { pattern: /^[A-Za-z0-9\-_]*$/, message: 'SKU只能包含字母、数字、中划线和下划线' }
              ]}
            >
              <Input placeholder="产品编号/SKU" />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">
          <Space>
            <span>产品成分</span>
            <Tag color="blue">至少添加一个成分</Tag>
          </Space>
        </Divider>

        <Form.List name="ingredients">
          {(fields, { add, remove }) => (
            <>
              {fields.map((field, index) => (
                <Card 
                  key={field.key} 
                  size="small" 
                  style={{ marginBottom: 12 }}
                  title={`成分 ${index + 1}`}
                  extra={
                    fields.length > 1 && (
                      <Button 
                        type="text" 
                        danger 
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          remove(field.name);
                          removeIngredient(index);
                        }}
                      >
                        删除
                      </Button>
                    )
                  }
                >
                  <Row gutter={12}>
                    <Col span={12}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'name']}
                        rules={[{ required: true, message: '请输入成分名称' }]}
                        label="成分名称"
                      >
                        <EntitySelector 
                          type="Ingredient" 
                          placeholder="输入成分名称，支持自动完成"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'amount']}
                        label="含量"
                      >
                        <InputNumber 
                          placeholder="含量"
                          min={0}
                          precision={2}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...field}
                        name={[field.name, 'unit']}
                        label="单位"
                      >
                        <Select placeholder="单位">
                          <Select.Option value="mg">mg</Select.Option>
                          <Select.Option value="g">g</Select.Option>
                          <Select.Option value="μg">μg</Select.Option>
                          <Select.Option value="IU">IU</Select.Option>
                          <Select.Option value="ml">ml</Select.Option>
                          <Select.Option value="%">%</Select.Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </Card>
              ))}
              
              <Button 
                type="dashed" 
                onClick={() => {
                  add({ name: '', amount: null, unit: 'mg' });
                  addIngredient();
                }}
                block 
                icon={<PlusOutlined />}
              >
                添加成分
              </Button>
            </>
          )}
        </Form.List>

        <Form.Item
          label="产品功效 (可选)"
          name="benefits"
          rules={[
            { max: 500, message: '功效描述不能超过500字符' }
          ]}
        >
          <TextArea 
            rows={3} 
            placeholder="描述产品的主要功效和作用"
            showCount
            maxLength={500}
          />
        </Form.Item>

        {saveError && (
          <Alert
            message="保存失败"
            description={saveError}
            type="error"
            showIcon
            closable
            style={{ marginTop: 16 }}
          />
        )}
      </Form>
    </Card>
  );
};