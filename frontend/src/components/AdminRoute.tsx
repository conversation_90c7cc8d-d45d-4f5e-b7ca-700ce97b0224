import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { StopOutlined } from '@ant-design/icons';
import { useAuthStore } from '../stores/authStore';
import PrivateRoute from './PrivateRoute';

interface AdminRouteProps {
  children: React.ReactNode;
}

const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  const navigate = useNavigate();
  const { isAdmin } = useAuthStore();

  return (
    <PrivateRoute>
      {isAdmin ? (
        <>{children}</>
      ) : (
        <Result
          status="403"
          title="权限不足"
          subTitle="抱歉，您没有权限访问此页面。"
          icon={<StopOutlined />}
          extra={
            <Button type="primary" onClick={() => navigate('/')}>
              返回首页
            </Button>
          }
        />
      )}
    </PrivateRoute>
  );
};

export default AdminRoute;