import React, { useState, useEffect, useMemo } from 'react';
import { AutoComplete, Tag, Typography } from 'antd';
import { useAppStore } from '../stores/appStore';
import type { Entity } from '../types/api';

const { Text } = Typography;

interface EntitySelectorProps {
  type: 'Brand' | 'Ingredient';
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
}

export const EntitySelector: React.FC<EntitySelectorProps> = ({
  type,
  value = '',
  onChange,
  placeholder,
  disabled = false,
}) => {
  const [searchValue, setSearchValue] = useState(value);
  const [isSearching, setIsSearching] = useState(false);

  const {
    brandSuggestions,
    ingredientSuggestions,
    searchBrands,
    searchIngredients,
  } = useAppStore();

  // 获取当前类型的建议列表
  const suggestions = useMemo(() => {
    return type === 'Brand' ? brandSuggestions : ingredientSuggestions;
  }, [type, brandSuggestions, ingredientSuggestions]);

  // 搜索函数
  const handleSearch = async (keyword: string) => {
    setSearchValue(keyword);
    
    if (keyword.length >= 2) {
      setIsSearching(true);
      try {
        if (type === 'Brand') {
          await searchBrands(keyword);
        } else {
          await searchIngredients(keyword);
        }
      } finally {
        setIsSearching(false);
      }
    }
  };

  // 选择建议项
  const handleSelect = (selectedValue: string) => {
    setSearchValue(selectedValue);
    onChange?.(selectedValue);
  };

  // 失焦时更新值
  const handleBlur = () => {
    onChange?.(searchValue);
  };

  // 当外部value变化时同步内部状态
  useEffect(() => {
    setSearchValue(value);
  }, [value]);

  // 转换建议列表为AutoComplete选项
  const options = suggestions.map((entity: Entity) => ({
    value: entity.name,
    label: (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>{entity.name}</span>
        <div style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
          <Tag 
            color={entity.match_score >= 0.9 ? 'green' : entity.match_score >= 0.7 ? 'orange' : 'default'}
            style={{ margin: 0, fontSize: 10 }}
          >
            {Math.round(entity.match_score * 100)}%
          </Tag>
          <Text type="secondary" style={{ fontSize: 10 }}>
            {entity.type === 'Brand' ? '品牌' : '成分'}
          </Text>
        </div>
      </div>
    ),
  }));

  // 如果没有搜索结果且输入了关键词，显示"新建"选项
  if (searchValue.length >= 2 && suggestions.length === 0 && !isSearching) {
    options.push({
      value: searchValue,
      label: (
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>{searchValue}</span>
          <Tag color="blue" style={{ margin: 0, fontSize: 10 }}>
            新建
          </Tag>
        </div>
      ),
    });
  }

  return (
    <AutoComplete
      value={searchValue}
      options={options}
      onSearch={handleSearch}
      onSelect={handleSelect}
      onBlur={handleBlur}
      placeholder={placeholder || `输入${type === 'Brand' ? '品牌' : '成分'}名称`}
      disabled={disabled}
      style={{ width: '100%' }}
      filterOption={false} // 禁用默认过滤，使用服务器端搜索
      notFoundContent={
        isSearching ? (
          <div style={{ padding: '8px 12px', textAlign: 'center' }}>
            <Text type="secondary">搜索中...</Text>
          </div>
        ) : searchValue.length < 2 ? (
          <div style={{ padding: '8px 12px', textAlign: 'center' }}>
            <Text type="secondary">请输入至少2个字符</Text>
          </div>
        ) : (
          <div style={{ padding: '8px 12px', textAlign: 'center' }}>
            <Text type="secondary">
              未找到匹配项，将创建新的{type === 'Brand' ? '品牌' : '成分'}
            </Text>
          </div>
        )
      }
    />
  );
};