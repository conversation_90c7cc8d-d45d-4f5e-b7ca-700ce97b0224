import React, { useState, useEffect } from 'react';
import {
  Card,
  Space,
  Button,
  Typography,
  Tag,
  Radio,
  Progress,
  Alert,
  Tooltip,
  Row,
  Col,
  Badge,
  Divider,
  message,
  Checkbox,
  Form,
  InputNumber,
  Select,
  Input
} from 'antd';
import {
  LinkOutlined,
  SearchOutlined,
  PlusOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  EditOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';
import { EntityEditForm } from './EntityEditForm';
import { useAppStore } from '../stores/appStore';
import type { 
  EntityLinkingData, 
  EntityLinkingOption,
  EntityEditingData,
  ProductSaveRequest 
} from '../types/api';

const { Text } = Typography;

export const EntityLinkingPanel: React.FC = () => {
  // 从appStore获取状态
  const { 
    extractedData, 
    showEntityLinking,
    setEntityLinkingResults,
    clearExtraction
  } = useAppStore();

  // 所有state hooks必须在early return之前调用
  const [linkingData, setLinkingData] = useState<EntityLinkingData[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  
  // 批量选择状态
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isBatchProcessing, setIsBatchProcessing] = useState(false);

  // 批量选择处理函数（必须在useEffect之前定义）
  const handleSelectAll = (checked: boolean) => {
    setIsAllSelected(checked);
    if (checked) {
      setSelectedItems(new Set(linkingData.map((_, index) => index)));
    } else {
      setSelectedItems(new Set());
    }
  };

  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+A 全选
      if (e.ctrlKey && e.key === 'a') {
        e.preventDefault();
        handleSelectAll(true);
      }
      // Escape 取消选择
      if (e.key === 'Escape' && selectedItems.size > 0) {
        setSelectedItems(new Set());
        setIsAllSelected(false);
      }
    };

    if (showEntityLinking) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [showEntityLinking, selectedItems.size]);

  // 初始化实体链接数据
  useEffect(() => {
    if (extractedData && showEntityLinking) {
      const entities: EntityLinkingData[] = [];
      
      // 添加产品实体
      if (extractedData.product_name) {
        entities.push({
          extracted_entity: {
            name: extractedData.product_name,
            type: 'Product',
            original_data: {
              sku: extractedData.sku || undefined,
              product_type: extractedData.product_type || '保健品'
            }
          },
          candidates: [],
          selected_option: 'new',
          status: 'selected',
          // 初始化编辑数据
          editing_data: {
            name: extractedData.product_name,
            sku: extractedData.sku || '',
            product_type: extractedData.product_type || '保健品',
            benefits: extractedData.benefits || ''
          }
        });
      }
      
      // 添加品牌
      if (extractedData.brand) {
        entities.push({
          extracted_entity: {
            name: extractedData.brand,
            type: 'Brand'
          },
          candidates: [],
          selected_option: 'new',
          status: 'selected',
          // 初始化编辑数据
          editing_data: {
            name: extractedData.brand
          }
        });
      }

      // 添加成分
      extractedData.ingredients.forEach(ingredient => {
        entities.push({
          extracted_entity: {
            name: ingredient.name,
            type: 'Ingredient',
            original_data: {
              amount: ingredient.amount || undefined,
              unit: ingredient.unit || undefined
            }
          },
          candidates: [],
          selected_option: 'new',
          status: 'selected',
          // 初始化编辑数据
          editing_data: {
            name: ingredient.name,
            amount: ingredient.amount || undefined,
            unit: ingredient.unit || '毫克'
          }
        });
      });

      setLinkingData(entities);
      
      // 自动开始搜索 - 当面板自动显示时立即开始
      if (entities.length > 0) {
        console.log('初始化实体数据:', entities.map(e => ({ name: e.extracted_entity.name, type: e.extracted_entity.type })));
        setTimeout(() => {
          startEntitySearch(entities);
        }, 500); // 给用户一点视觉过渡时间
      }
    }
  }, [extractedData, showEntityLinking]);

  // 开始实体搜索
  const startEntitySearch = async (entities: EntityLinkingData[]) => {
    setIsSearching(true);
    setCurrentSearchIndex(0);

    const updatedEntities = [...entities];

    for (let i = 0; i < updatedEntities.length; i++) {
      setCurrentSearchIndex(i);
      const entity = updatedEntities[i];
      
      // 更新状态为搜索中
      updatedEntities[i] = { ...entity, status: 'searching' };
      setLinkingData([...updatedEntities]);

      try {
        let candidates: EntityLinkingOption[] = [];
        
        if (entity.extracted_entity.type === 'Product') {
          // 产品搜索的临时实现 - 搜索可能失败，但不影响整体流程
          try {
            const searchResponse = await apiService.searchEntities(
              'Product',
              entity.extracted_entity.name,
              3
            );
            
            if (searchResponse.success && searchResponse.data.length > 0) {
              candidates = searchResponse.data.map(item => ({
                id: item.id,
                name: item.name,
                type: item.type as 'Product',
                similarity_score: item.match_score,
                match_reason: `产品名称相似度 ${Math.round(item.match_score * 100)}%`,
                // 复制产品的额外属性
                product_type: item.product_type,
                sku: item.sku,
                benefits: item.benefits
              }));
            }
          } catch (productSearchError) {
            console.warn('产品搜索失败，后端可能不支持Product类型搜索:', productSearchError);
            // 产品搜索失败，使用空候选列表
            candidates = [];
          }
        } else {
          // 品牌和成分搜索
          const searchResponse = await apiService.searchEntities(
            entity.extracted_entity.type as 'Brand' | 'Ingredient',
            entity.extracted_entity.name,
            3
          );

          if (searchResponse.success && searchResponse.data.length > 0) {
            candidates = searchResponse.data.map(item => ({
              id: item.id,
              name: item.name,
              type: item.type as 'Brand' | 'Ingredient',
              similarity_score: item.match_score,
              match_reason: `相似度匹配 ${Math.round(item.match_score * 100)}%`
            }));
          }
        }

        updatedEntities[i] = {
          ...entity,
          candidates,
          // 如果有高相似度匹配(>0.9)，自动选中第一个，但状态设为selected等待用户确认
          selected_option: candidates.length > 0 && candidates[0].similarity_score > 0.9 
            ? candidates[0].id 
            : 'new',
          // 无论是自动选中相似实体还是默认创建新实体，都设为selected状态等待确认
          status: 'selected'
        };
      } catch (error) {
        console.warn(`搜索实体失败: ${entity.extracted_entity.name}`, error);
        updatedEntities[i] = { ...entity, candidates: [], selected_option: 'new', status: 'selected' };
      }

      setLinkingData([...updatedEntities]);
      
      // 添加延迟避免API调用过于频繁
      if (i < updatedEntities.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    setIsSearching(false);
    setCurrentSearchIndex(-1);
  };

  // 更新实体选择
  const updateEntitySelection = (index: number, selectedOption: string) => {
    const updatedData = [...linkingData];
    updatedData[index] = {
      ...updatedData[index],
      selected_option: selectedOption,
      status: 'selected' // 选择后变为selected状态，等待确认
    };
    setLinkingData(updatedData);
  };

  // 批量选择相关函数（已在上面定义，避免重复）

  const handleItemSelect = (index: number, checked: boolean) => {
    const newSelected = new Set(selectedItems);
    if (checked) {
      newSelected.add(index);
    } else {
      newSelected.delete(index);
    }
    setSelectedItems(newSelected);
    setIsAllSelected(newSelected.size === linkingData.length);
  };

  // 简化的批量确认操作
  const handleBatchConfirm = async () => {
    setIsBatchProcessing(true);
    const selectedCount = selectedItems.size;
    
    try {
      const updatedData = [...linkingData];
      selectedItems.forEach(index => {
        if (updatedData[index].status !== 'confirmed') {
          // 关键修复：只修改status为confirmed，保持用户的selected_option选择不变
          updatedData[index] = {
            ...updatedData[index],
            status: 'confirmed'
            // 不修改 selected_option，保持用户的原有选择
          };
        }
      });
      
      setLinkingData(updatedData);
      setSelectedItems(new Set()); // 清除选择
      setIsAllSelected(false);
      
      message.success(`✅ 已成功批量确认 ${selectedCount} 个实体`);
    } finally {
      setIsBatchProcessing(false);
    }
  };

  // 更新编辑数据
  const updateEditingData = (index: number, editingData: EntityEditingData) => {
    const updatedData = [...linkingData];
    updatedData[index] = {
      ...updatedData[index],
      editing_data: editingData,
      // 清除验证错误
      validation_errors: []
    };
    setLinkingData(updatedData);
  };

  // 验证单个实体
  const validateEntity = (entity: EntityLinkingData): string[] => {
    const errors: string[] = [];
    
    if (entity.selected_option === 'new' && entity.editing_data) {
      // 验证名称
      if (!entity.editing_data.name || entity.editing_data.name.trim().length === 0) {
        errors.push('名称不能为空');
      }
      
      if (entity.editing_data.name && entity.editing_data.name.length > 100) {
        errors.push('名称长度不能超过100字符');
      }

      // 成分特有验证
      if (entity.extracted_entity.type === 'Ingredient') {
        if (entity.editing_data.amount !== undefined && entity.editing_data.amount !== null && entity.editing_data.amount < 0) {
          errors.push('含量不能为负数');
        }
      }

      // 产品特有验证
      if (entity.extracted_entity.type === 'Product') {
        if (entity.editing_data.sku && entity.editing_data.sku.length > 50) {
          errors.push('SKU长度不能超过50字符');
        }
      }
    }
    
    return errors;
  };

  // 确认单个实体
  const confirmEntity = (index: number) => {
    const entity = linkingData[index];
    const errors = validateEntity(entity);
    
    if (errors.length > 0) {
      // 更新验证错误
      const updatedData = [...linkingData];
      updatedData[index] = {
        ...updatedData[index],
        validation_errors: errors
      };
      setLinkingData(updatedData);
      message.error('请修正输入错误后再确认');
      return;
    }

    // 确认成功
    const updatedData = [...linkingData];
    updatedData[index] = {
      ...updatedData[index],
      status: 'confirmed',
      validation_errors: []
    };
    setLinkingData(updatedData);
    message.success(`${entity.extracted_entity.name} 已确认`);
  };

  // 获取相似度颜色
  const getSimilarityColor = (score: number): string => {
    if (score >= 0.9) return 'success';
    if (score >= 0.7) return 'warning';
    return 'error';
  };

  // 计算完成进度（简化模式）
  const getCompletionProgress = () => {
    if (isExistingProductSelected()) {
      // 选择了现有产品，认为100%完成
      return 100;
    }
    
    const confirmed = linkingData.filter(item => item.status === 'confirmed').length;
    return linkingData.length > 0 ? Math.round((confirmed / linkingData.length) * 100) : 0;
  };

  // 获取状态统计
  const getStatusStats = () => {
    const stats = {
      pending: linkingData.filter(item => item.status === 'pending').length,
      selected: linkingData.filter(item => item.status === 'selected').length,
      confirmed: linkingData.filter(item => item.status === 'confirmed').length,
      searching: linkingData.filter(item => item.status === 'searching').length
    };
    return stats;
  };

  // 检查是否选择了现有产品（简化模式）
  const isExistingProductSelected = () => {
    const productEntity = linkingData.find(item => item.extracted_entity.type === 'Product');
    return productEntity && productEntity.selected_option !== 'new';
  };

  // getIngredientData 函数已不需要，成分数据直接使用 linkingData

  // 成分含量数据现在集成到editing_data中，不需要独立的状态管理

  // 将实体链接数据转换为产品保存请求
  const convertToSaveRequest = (linkingData: EntityLinkingData[]): ProductSaveRequest => {
    // 找到产品、品牌和成分数据
    const productEntity = linkingData.find(item => item.extracted_entity.type === 'Product');
    const brandEntity = linkingData.find(item => item.extracted_entity.type === 'Brand');
    const ingredientEntities = linkingData.filter(item => item.extracted_entity.type === 'Ingredient');

    if (!productEntity || !brandEntity) {
      throw new Error('缺少必要的产品或品牌信息');
    }

    // 构造保存请求（使用editing_data中的成分数据）
    const saveRequest: ProductSaveRequest = {
      product_name: productEntity.editing_data?.name || productEntity.extracted_entity.name,
      brand: brandEntity.editing_data?.name || brandEntity.extracted_entity.name,
      product_type: productEntity.editing_data?.product_type || '保健品',
      sku: productEntity.editing_data?.sku || undefined,
      benefits: productEntity.editing_data?.benefits || undefined,
      ingredients: ingredientEntities.map(ing => ({
        name: ing.editing_data?.name || ing.extracted_entity.name,
        amount: ing.editing_data?.amount || ing.extracted_entity.original_data?.amount || null,
        unit: ing.editing_data?.unit || ing.extracted_entity.original_data?.unit || null
      }))
    };

    return saveRequest;
  };

  // 保存产品到数据库
  const saveProductDirectly = async () => {
    try {
      setIsSaving(true);
      
      // 调试信息
      console.log('当前链接数据:', linkingData);
      console.log('实体类型分布:', {
        products: linkingData.filter(item => item.extracted_entity.type === 'Product').length,
        brands: linkingData.filter(item => item.extracted_entity.type === 'Brand').length,
        ingredients: linkingData.filter(item => item.extracted_entity.type === 'Ingredient').length
      });
      
      // 转换数据
      const saveRequest = convertToSaveRequest(linkingData);
      
      console.log('保存产品数据:', saveRequest);
      
      // 调用保存API
      const response = await apiService.saveProduct(saveRequest);
      
      if (response.success) {
        // 保存链接结果
        setEntityLinkingResults(linkingData);
        message.success(`产品 "${saveRequest.product_name}" 保存成功！`);
        
        // 保存成功后清理状态，让用户可以开始新的录入
        setTimeout(() => {
          clearExtraction(); // 清理提取结果，这会隐藏提取结果面板和实体确认面板
        }, 1500); // 1.5秒后清理，让用户看到成功消息
      } else {
        message.error(response.error || '保存失败');
      }
    } catch (error: any) {
      console.error('保存产品失败:', error);
      message.error(error.message || '保存失败，请稍后重试');
    } finally {
      setIsSaving(false);
    }
  };

  // 确认链接并保存
  const handleConfirmLinking = () => {
    const existingProductSelected = isExistingProductSelected();
    
    if (existingProductSelected) {
      // 选择了现有产品，不需要保存，直接清理状态开始新的录入
      const productEntity = linkingData.find(item => item.extracted_entity.type === 'Product');
      const selectedProduct = productEntity?.candidates.find(c => c.id === productEntity.selected_option);
      
      message.success(`已选择现有产品 "${selectedProduct?.name}"，现在可以继续录入新产品`);
      
      // 清理状态，让用户可以开始新的录入
      setTimeout(() => {
        clearExtraction();
      }, 1500);
      
      return;
    }
    
    // 创建新产品的原有逻辑
    const allConfirmed = linkingData.every(item => item.status === 'confirmed');
    if (allConfirmed) {
      saveProductDirectly();
    } else {
      message.warning('请先确认所有实体后再提交');
    }
  };

  // 继续录入新产品功能已集成到确认链接流程中

  // 使用条件渲染而不是early return，确保Hooks顺序一致
  return !extractedData || !showEntityLinking ? null : (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 固定头部 */}
      <Card
        title={
          <Space>
            <LinkOutlined />
            <span>实体链接确认</span>
            <Badge count={linkingData.length} />
            {isSearching && <SyncOutlined spin />}
          </Space>
        }
        extra={
          <Progress 
            type="circle" 
            size={32}
            percent={getCompletionProgress()}
            strokeWidth={8}
          />
        }
        size="small"
        style={{ marginBottom: 0, flexShrink: 0 }}
        bodyStyle={{ padding: '16px 16px 0 16px' }}
      >
        <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
          {/* 头部固定区域 */}
          {/* <div> */}
            <Alert
              message="实体确认 - 必须完成此步骤"
              description="AI已提取实体并搜索相似项。请为每个实体选择操作：创建新实体（可编辑）或链接现有实体。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
          {/* </div> */}
        
          {/* 状态统计 */}
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Text type="secondary">进度统计：</Text>
              <Tag color="processing">搜索中 {getStatusStats().searching}</Tag>
              <Tag color="orange">待选择 {getStatusStats().pending}</Tag>
              <Tag color="blue">已选择 {getStatusStats().selected}</Tag>
              <Tag color="success">已确认 {getStatusStats().confirmed}</Tag>
            </Space>
          </div>

          {/* 批量操作区域 */}
          <div style={{ padding: '12px', backgroundColor: '#fafafa', borderRadius: '6px', marginBottom: 16 }}>
            <Row align="middle" justify="space-between">
              <Col>
                <Space>
                  <Checkbox
                    checked={isAllSelected}
                    indeterminate={selectedItems.size > 0 && !isAllSelected}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    全选 ({selectedItems.size} / {linkingData.length})
                  </Checkbox>
                  {selectedItems.size === 0 && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      💡 提示: Ctrl+A 全选，Esc 取消选择
                    </Text>
                  )}
                  {selectedItems.size > 0 && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      已选择 {selectedItems.size} 项
                    </Text>
                  )}
                </Space>
              </Col>
              <Col>
                <Space>
                  <Button 
                    size="small"
                    disabled={selectedItems.size === 0 || isBatchProcessing}
                    loading={isBatchProcessing}
                    onClick={handleBatchConfirm}
                    icon={<CheckCircleOutlined />}
                    type="primary"
                  >
                    批量确认
                  </Button>
                  <Button 
                    size="small"
                    disabled={selectedItems.size === 0}
                    onClick={() => {
                      setSelectedItems(new Set());
                      setIsAllSelected(false);
                    }}
                  >
                    取消选择
                  </Button>
                </Space>
              </Col>
            </Row>
          </div>

          {/* 滚动区域 - 实体列表 */}
          <div style={{
            flex: 1,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <div 
              style={{
                flex: 1,
                overflowY: 'auto',
                padding: '0 16px',
                maxHeight: '60vh',  // 限制最大高度
                // 美化滚动条样式
                scrollbarWidth: 'thin',
                scrollbarColor: '#bfbfbf #f0f0f0'
              }}
              className="entity-scroll-container"
            >
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                {linkingData
                  .filter(item => {
                    // 当选择了现有产品时，隐藏品牌和成分面板
                    if (isExistingProductSelected()) {
                      return item.extracted_entity.type === 'Product';
                    }
                    return true;
                  })
                  .map((item, _filteredIndex) => {
                    // 找到在原始 linkingData 中的索引
                    const originalIndex = linkingData.findIndex(originalItem => 
                      originalItem.extracted_entity.name === item.extracted_entity.name && 
                      originalItem.extracted_entity.type === item.extracted_entity.type
                    );
                    // 确定卡片边框样式
                    const getBorderStyle = () => {
                      switch (item.status) {
                        case 'confirmed': return { border: '2px solid #52c41a', backgroundColor: '#f6ffed' };
                        case 'selected': return { border: '2px solid #1890ff', backgroundColor: '#f0f8ff' };
                        case 'searching': return { border: '2px solid #faad14', backgroundColor: '#fffbe6' };
                        default: return { border: '1px solid #d9d9d9' };
                      }
                    };

                    return (
                      <Card
                        key={originalIndex}
                        size="small"
                  title={
                    <Row justify="space-between" align="middle">
                      <Col>
                        <Space>
                          <Checkbox
                            checked={selectedItems.has(originalIndex)}
                            onChange={(e) => handleItemSelect(originalIndex, e.target.checked)}
                          />
                          <Tag color={
                            item.extracted_entity.type === 'Product' ? 'purple' :
                            item.extracted_entity.type === 'Brand' ? 'blue' : 'green'
                          }>
                            {item.extracted_entity.type === 'Product' ? '产品' :
                            item.extracted_entity.type === 'Brand' ? '品牌' : '成分'}
                          </Tag>
                          <Text strong>{item.extracted_entity.name}</Text>
                          
                          {/* 状态标签 */}
                          {item.status === 'searching' && currentSearchIndex === originalIndex && (
                            <Tag icon={<SyncOutlined spin />} color="processing">搜索中</Tag>
                          )}
                          {item.status === 'selected' && (
                            <Tag icon={<EditOutlined />} color="blue">已选择</Tag>
                          )}
                          {item.status === 'confirmed' && (
                            <Tag icon={<CheckCircleOutlined />} color="success">已确认</Tag>
                          )}
                        </Space>
                      </Col>
                      
                      {/* 操作按钮 */}
                      <Col>
                        <Space>
                          {item.status === 'selected' && (
                            <Button
                              type="primary"
                              size="small"
                              icon={<SaveOutlined />}
                              onClick={() => confirmEntity(originalIndex)}
                            >
                              确认
                            </Button>
                          )}
                          {item.status === 'confirmed' && (
                            <Button
                              type="default"
                              size="small"
                              onClick={() => {
                                const updatedData = [...linkingData];
                                updatedData[originalIndex] = {
                                  ...updatedData[originalIndex],
                                  status: 'selected'
                                };
                                setLinkingData(updatedData);
                              }}
                            >
                              重新编辑
                            </Button>
                          )}
                        </Space>
                      </Col>
                    </Row>
                  }
                  style={getBorderStyle()}
                >
                  {/* 成分含量表单 - 仅针对成分显示，始终可见 */}
                  {item.extracted_entity.type === 'Ingredient' && (
                    <div style={{
                      padding: '12px',
                      backgroundColor: '#f6ffed',
                      borderRadius: '6px',
                      border: '1px solid #b7eb8f',
                      marginBottom: '16px'
                    }}>
                      <Text strong style={{ display: 'block', marginBottom: '8px', color: '#389e0d' }}>
                        🌿 成分含量信息
                      </Text>
                      <Space.Compact style={{ display: 'flex' }}>
                        <Form.Item label="含量" style={{ flex: 1, marginBottom: 0, marginRight: '8px' }}>
                          <InputNumber
                            value={item.editing_data?.amount}
                            onChange={(value) => updateEditingData(originalIndex, { 
                              ...(item.editing_data || {}), 
                              amount: value ?? null 
                            })}
                            placeholder="含量"
                            min={0}
                            precision={2}
                            style={{ width: '100%' }}
                            disabled={item.status === 'confirmed'}
                          />
                        </Form.Item>
                        
                        <Form.Item label="单位" style={{ width: '120px', marginBottom: 0 }}>
                          <Select
                            value={item.editing_data?.unit || '毫克'}
                            onChange={(value) => updateEditingData(originalIndex, { ...(item.editing_data || {}), unit: value })}
                            style={{ width: '100%' }}
                            disabled={item.status === 'confirmed'}
                          >
                            <Select.Option value="毫克">毫克 (mg)</Select.Option>
                            <Select.Option value="微克">微克 (μg)</Select.Option>
                            <Select.Option value="克">克 (g)</Select.Option>
                            <Select.Option value="IU">国际单位 (IU)</Select.Option>
                            <Select.Option value="片">片</Select.Option>
                            <Select.Option value="粒">粒</Select.Option>
                            <Select.Option value="%">百分比 (%)</Select.Option>
                          </Select>
                        </Form.Item>
                      </Space.Compact>
                      <Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: '4px' }}>
                        📝 上方含量信息将应用于下方选择的成分
                      </Text>
                    </div>
                  )}

                  {/* 选择区域 */}
                  <Radio.Group
                    value={item.selected_option}
                    onChange={(e) => updateEntitySelection(originalIndex, e.target.value)}
                    style={{ width: '100%' }}
                    disabled={item.status === 'confirmed'}
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      {/* 创建新实体选项 */}
                      <Radio value="new">
                        <Space>
                          <PlusOutlined style={{ color: '#1890ff' }} />
                          <Text strong>创建新实体</Text>
                        </Space>
                      </Radio>

                      {/* 编辑表单 - 仅在选择创建新实体时显示 */}
                      {item.selected_option === 'new' && item.editing_data && (
                        <div style={{ marginLeft: 24, marginTop: 8 }}>
                          {item.extracted_entity.type === 'Ingredient' ? (
                            // 成分的编辑表单只显示名称（含量在上方单独显示）
                            <div style={{ 
                              padding: '12px', 
                              backgroundColor: '#fafafa', 
                              borderRadius: '6px',
                              border: '1px solid #f0f0f0'
                            }}>
                              <Text type="secondary" style={{ fontSize: '12px', marginBottom: '8px', display: 'block' }}>
                                📝 编辑新成分信息
                              </Text>
                              <Form layout="vertical" size="small">
                                <Form.Item 
                                  label="成分名称"
                                  validateStatus={!item.editing_data.name || item.editing_data.name.trim().length === 0 ? 'error' : ''}
                                  help={!item.editing_data.name || item.editing_data.name.trim().length === 0 ? '名称不能为空' : ''}
                                  style={{ marginBottom: '12px' }}
                                >
                                  <Input
                                    value={item.editing_data.name}
                                    onChange={(e) => updateEditingData(originalIndex, { ...item.editing_data, name: e.target.value })}
                                    placeholder="请输入成分名称"
                                    disabled={item.status === 'confirmed'}
                                    maxLength={100}
                                    showCount
                                  />
                                </Form.Item>
                              </Form>
                            </div>
                          ) : (
                            // 非成分实体使用原有的EntityEditForm
                            <EntityEditForm
                              entityType={item.extracted_entity.type}
                              editingData={item.editing_data}
                              validationErrors={item.validation_errors}
                              onChange={(data) => updateEditingData(originalIndex, data)}
                              disabled={item.status === 'confirmed'}
                            />
                          )}
                        </div>
                      )}

                      {/* 链接现有实体选项 */}
                      {item.candidates.length > 0 && (
                        <>
                          <Divider style={{ margin: '8px 0' }} />
                          <div style={{ paddingLeft: 0 }}>
                            <Text type="secondary" style={{ fontSize: 12, marginBottom: 8, display: 'block' }}>
                              🔗 找到 {item.candidates.length} 个相似实体：
                            </Text>
                            {item.candidates.map((candidate) => (
                              <div key={candidate.id} style={{ marginBottom: 8 }}>
                                <Radio value={candidate.id}>
                                  <Row style={{ width: '100%' }} align="middle">
                                    <Col span={14}>
                                      <Text>{candidate.name}</Text>
                                    </Col>
                                    <Col span={5}>
                                      <Tag 
                                        color={getSimilarityColor(candidate.similarity_score)}
                                      >
                                        {Math.round(candidate.similarity_score * 100)}%
                                      </Tag>
                                    </Col>
                                    <Col span={5}>
                                      <Tooltip title={
                                        <div>
                                          <div><strong>实体详情:</strong></div>
                                          <div>• 名称: {candidate.name}</div>
                                          <div>• ID: {candidate.id}</div>
                                          {item.extracted_entity.type === 'Product' && (
                                            <>
                                              <div>• 产品类型: {candidate.product_type || '未设置'}</div>
                                              <div>• SKU: {candidate.sku || '未设置'}</div>
                                              <div>• 功效: {candidate.benefits || '未设置'}</div>
                                            </>
                                          )}
                                        </div>
                                      }>
                                        <Button type="link" icon={<SearchOutlined />}>
                                          详情
                                        </Button>
                                      </Tooltip>
                                    </Col>
                                  </Row>
                                </Radio>
                              </div>
                            ))}
                          </div>
                        </>
                      )}

                      {/* 无相似实体提示 */}
                      {item.candidates.length === 0 && item.status !== 'searching' && (
                        <div style={{ paddingLeft: 24 }}>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            💡 未找到相似实体，建议创建新实体
                          </Text>
                        </div>
                      )}
                    </Space>
                  </Radio.Group>
                      </Card>
                    );
                  })}
              </Space>
            </div>

            {/* 固定底部 - 保存操作区域 */}
            <div 
              style={{ 
                marginTop: 16,
                padding: '16px',
                borderTop: '2px solid #f0f0f0',
                backgroundColor: '#fafafa',
                borderRadius: '0 0 6px 6px',
                textAlign: 'center'
              }}
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Button 
                  type="primary" 
                  size="large"
                  onClick={handleConfirmLinking}
                  disabled={!isExistingProductSelected() && getCompletionProgress() < 100}
                  loading={isSaving}
                  icon={<SaveOutlined />}
                  style={{ minWidth: 200 }}
                >
                  {isSaving 
                    ? '正在保存产品...' 
                    : isExistingProductSelected()
                      ? '继续录入新产品'
                      : `确认并保存产品 (${getCompletionProgress()}%)`
                  }
                </Button>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {isExistingProductSelected()
                    ? '✅ 已选择现有产品，点击继续录入新产品'
                    : getCompletionProgress() < 100
                      ? '⚠️ 必须完成所有实体确认才能保存产品'
                      : '✅ 所有实体已确认，现在可以保存产品'
                  }
                </Text>
              </Space>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};