/* 全局样式重置 */
* {
  box-sizing: border-box;
}

#root {
  min-height: 100vh;
}

/* Ant Design 主题颜色调整 */
.ant-layout-header {
  height: 64px;
  line-height: 64px;
}

/* 自定义组件样式 */
.kgms-extraction-panel .ant-card-body {
  padding: 24px;
}

.kgms-form-panel .ant-card-body {
  padding: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    padding: 16px !important;
  }
  
  .ant-layout-header {
    padding: 0 16px !important;
  }
  
  .ant-layout-header .ant-typography {
    font-size: 18px !important;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 实体确认面板滚动容器样式 */
.entity-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #bfbfbf #f0f0f0;
}

.entity-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.entity-scroll-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.entity-scroll-container::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 4px;
}

.entity-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #999999;
}

/* 表单验证样式增强 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-affix-wrapper,
.ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

/* 成功状态样式 */
.kgms-success-highlight {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
}

/* 成分卡片样式 */
.kgms-ingredient-card {
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.kgms-ingredient-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kgms-fade-in {
  animation: fadeInUp 0.3s ease-out;
}
