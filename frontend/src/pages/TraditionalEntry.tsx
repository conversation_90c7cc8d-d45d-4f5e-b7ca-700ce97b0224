import React from 'react';
import { Row, Col, Typography, Space, Alert } from 'antd';
import { ExtractionPanel } from '../components/ExtractionPanel';
import { ProductForm } from '../components/ProductForm';

const { Title } = Typography;

export const TraditionalEntry: React.FC = () => {
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 页面标题和说明 */}
        <div>
          <Title level={2}>传统产品录入 - Phase 1</Title>
          <Alert
            message="传统录入模式"
            description="使用基础AI提取功能，提取后需要手动确认和编辑产品信息，然后保存到知识图谱。适合需要详细编辑产品信息的场景。"
            type="info"
            showIcon
            style={{ marginBottom: 24 }}
          />
        </div>

        {/* 两栏布局 */}
        <Row gutter={24}>
          {/* 左侧：AI提取面板 */}
          <Col span={12}>
            <ExtractionPanel />
          </Col>

          {/* 右侧：产品信息录入表单 */}
          <Col span={12}>
            <ProductForm />
          </Col>
        </Row>
      </Space>
    </div>
  );
};