import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  Typography, 
  Space, 
  Alert, 
  Button, 
  Tag,
  List 
} from 'antd';
import { 
  RobotOutlined,
  FormOutlined, 
  CheckCircleOutlined,
  ClockCircleOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { apiService } from '../services/api';

const { Title, Text } = Typography;

export const Overview: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkSystemHealth();
  }, []);

  const checkSystemHealth = async () => {
    try {
      const health = await apiService.checkHealth();
      setSystemHealth(health);
    } catch (error) {
      console.error('系统健康检查失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const features = [
    {
      title: 'AI智能提取',
      description: 'Phase 2增强版，包含LLM标准化、实体重复检测',
      icon: <RobotOutlined />,
      path: '/smart-entry',
      badge: 'Phase 2',
      badgeColor: '#52c41a'
    },
    {
      title: '传统产品录入',
      description: 'Phase 1基础版，AI提取后手动编辑确认',
      icon: <FormOutlined />,
      path: '/traditional-entry',
      badge: 'Phase 1', 
      badgeColor: '#1890ff'
    }
  ];

  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 页面标题 */}
        <div>
          <Title level={2}>系统概览</Title>
          <Text type="secondary">
            知识图谱管理系统 (KGMS) - 智能产品信息录入平台
          </Text>
        </div>

        {/* 系统状态 */}
        <Card title="系统状态" loading={loading}>
          {systemHealth ? (
            <div>
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={8}>
                  <Statistic
                    title="系统状态"
                    value={systemHealth.status}
                    prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="Neo4j状态"
                    value={systemHealth.services?.neo4j || 'unknown'}
                    prefix={<ApiOutlined />}
                    valueStyle={{ 
                      color: systemHealth.services?.neo4j === 'connected' ? '#52c41a' : '#ff4d4f' 
                    }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="版本"
                    value={systemHealth.version}
                    prefix={<ClockCircleOutlined />}
                  />
                </Col>
              </Row>
              <Button 
                type="primary" 
                icon={<ApiOutlined />}
                onClick={checkSystemHealth}
                loading={loading}
              >
                检查连接
              </Button>
            </div>
          ) : (
            <Alert
              message="系统状态检查失败"
              description="无法获取系统健康状态，请检查后端服务是否正常运行。"
              type="error"
              showIcon
              action={
                <Button size="small" danger onClick={checkSystemHealth}>
                  重试
                </Button>
              }
            />
          )}
        </Card>

        {/* 功能模块 */}
        <Card title="功能模块">
          <List
            grid={{ gutter: 16, column: 2 }}
            dataSource={features}
            renderItem={item => (
              <List.Item>
                <Card 
                  hoverable
                  onClick={() => window.location.hash = item.path}
                  style={{ cursor: 'pointer' }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Space>
                      {item.icon}
                      <Title level={4} style={{ margin: 0 }}>
                        {item.title}
                      </Title>
                      <Tag color={item.badgeColor}>{item.badge}</Tag>
                    </Space>
                    <Text type="secondary">{item.description}</Text>
                  </Space>
                </Card>
              </List.Item>
            )}
          />
        </Card>

        {/* 使用指南 */}
        <Card title="使用指南">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="智能录入模式 (推荐)"
              description={
                <div>
                  <p>• 使用Phase 2增强版AI提取功能</p>
                  <p>• 包含LLM标准化、实体重复检测和置信度评估</p>
                  <p>• 通过实体链接确认界面直接保存，工作流程高效</p>
                  <p>• 适合大批量产品录入和标准化处理</p>
                </div>
              }
              type="success"
              showIcon
            />
            
            <Alert
              message="传统录入模式"
              description={
                <div>
                  <p>• 使用Phase 1基础版AI提取功能</p>
                  <p>• AI提取后需手动确认和编辑产品信息</p>
                  <p>• 提供完整的产品信息编辑界面</p>
                  <p>• 适合需要详细编辑产品信息的场景</p>
                </div>
              }
              type="info"
              showIcon
            />
          </Space>
        </Card>
      </Space>
    </div>
  );
};