import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Row,
  Col,
  Modal,
  Form,
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Typography,
  Statistic,
  Badge,
  InputNumber,
  Spin
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ShoppingOutlined,
  MinusCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { 
  entityManagementService, 
  type ProductInfo, 
  type ProductDetail, 
  type ProductListParams 
} from '../services/entityManagementService';
import VectorizationStatus from '../components/VectorizationStatus';

const { Option } = Select;
const { Title, Text } = Typography;
// const { TextArea } = Input;

const ProductManagement: React.FC = () => {
  const [products, setProducts] = useState<ProductInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  
  // 筛选和搜索状态
  const [searchText, setSearchText] = useState('');
  const [brandFilter, setBrandFilter] = useState('');
  const [ingredientFilter] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'active' | 'unused' | 'duplicate'>('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // 模态框状态
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false); // 区分创建和编辑模式
  const [selectedProduct, setSelectedProduct] = useState<ProductDetail | null>(null);
  
  // 统计数据
  const [stats, setStats] = useState<any>({});
  
  // 品牌数据 (用于创建产品时选择)
  const [brands, setBrands] = useState<any[]>([]);
  
  // 成分数据和搜索
  const [ingredients, setIngredients] = useState<any[]>([]);
  const [loadingIngredients, setLoadingIngredients] = useState(false);
  
  // 表单
  const [form] = Form.useForm();

  // 加载产品列表
  const loadProducts = async (params?: Partial<ProductListParams>) => {
    setLoading(true);
    try {
      const queryParams: ProductListParams = {
        page: currentPage,
        size: pageSize,
        search: searchText || undefined,
        brand_filter: brandFilter || undefined,
        ingredient_filter: ingredientFilter || undefined,
        filter_type: filterType,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...params
      };

      const response = await entityManagementService.getProducts(queryParams);
      
      if (response.success) {
        // 确保数据去重，避免重复的uuid导致React key冲突
        const uniqueProducts = response.data.filter((product, index, self) => 
          index === self.findIndex(p => p.uuid === product.uuid)
        );
        setProducts(uniqueProducts);
        setTotal(response.total);
      } else {
        message.error(response.error || '获取产品列表失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await entityManagementService.getEntityStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 加载品牌列表
  const loadBrands = async () => {
    try {
      const response = await entityManagementService.getBrands({
        page: 1,
        size: 100, // 获取所有品牌
        filter_type: 'all',
        sort_by: 'name',
        sort_order: 'asc'
      });
      if (response.success) {
        setBrands(response.data);
      }
    } catch (error) {
      console.error('Failed to load brands:', error);
    }
  };

  // 搜索成分
  const searchIngredients = async (searchText: string) => {
    if (!searchText || searchText.length < 1) {
      // 搜索为空时，保留当前产品的现有成分选项
      if (isEditing && selectedProduct?.ingredients) {
        const currentIngredients = selectedProduct.ingredients
          .filter(ing => ing.ingredient_id)
          .map(ing => ({
            uuid: ing.ingredient_id,
            name: ing.name
          }));
        setIngredients(currentIngredients);
      } else {
        setIngredients([]);
      }
      return;
    }

    setLoadingIngredients(true);
    try {
      const response = await entityManagementService.getIngredients({
        page: 1,
        size: 20,
        search: searchText,
        filter_type: 'all',
        sort_by: 'name',
        sort_order: 'asc'
      });
      if (response.success) {
        // 获取当前产品已有的成分（编辑模式下）
        const currentIngredients = isEditing && selectedProduct?.ingredients 
          ? selectedProduct.ingredients
              .filter(ing => ing.ingredient_id)
              .map(ing => ({
                uuid: ing.ingredient_id,
                name: ing.name
              }))
          : [];
        
        // 合并搜索结果和现有成分，去重
        const searchResults = response.data || [];
        const allIngredients = [...currentIngredients, ...searchResults];
        const uniqueIngredients = allIngredients.filter(
          (ingredient, index, self) => 
            index === self.findIndex(i => i.uuid === ingredient.uuid)
        );
        
        setIngredients(uniqueIngredients);
      }
    } catch (error) {
      console.error('Failed to search ingredients:', error);
    } finally {
      setLoadingIngredients(false);
    }
  };

  // 初始加载
  useEffect(() => {
    loadProducts();
    loadStats();
    loadBrands();
  }, [currentPage, pageSize, filterType, sortBy, sortOrder]);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
    loadProducts({ page: 1, search: value });
  };

  // 品牌筛选处理
  const handleBrandFilterChange = (value: string) => {
    setBrandFilter(value);
    setCurrentPage(1);
    loadProducts({ page: 1, brand_filter: value });
  };

  // 筛选处理
  const handleFilterChange = (value: 'all' | 'active' | 'unused' | 'duplicate') => {
    setFilterType(value);
    setCurrentPage(1);
  };

  // 查看详情
  const handleViewDetail = async (productId: string) => {
    setLoading(true);
    try {
      const response = await entityManagementService.getProductDetail(productId);
      if (response.success && response.data) {
        setSelectedProduct(response.data);
        setDetailModalVisible(true);
      } else {
        message.error('获取产品详情失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 打开创建模态框
  const openCreateModal = () => {
    setIsEditing(false);
    setSelectedProduct(null);
    form.resetFields();
    
    // 创建模式下清空成分选项，避免缓存污染
    setIngredients([]);
    setLoadingIngredients(false);
    
    setFormModalVisible(true);
  };

  // 打开编辑模态框
  const openEditModal = async (product: ProductInfo) => {
    setIsEditing(true);
    setLoading(true);
    
    try {
      // 重置成分搜索状态，避免缓存污染
      setIngredients([]);
      setLoadingIngredients(false);
      
      // 获取完整的产品详情（包括成分信息）
      const response = await entityManagementService.getProductDetail(product.uuid);
      if (response.success && response.data) {
        const productDetail = response.data;
        setSelectedProduct(productDetail);
        
        // 预加载当前产品的成分到选项中，确保显示正确的名称
        if (productDetail.ingredients && productDetail.ingredients.length > 0) {
          const currentIngredients = productDetail.ingredients
            .filter(ing => ing.ingredient_id) // 过滤掉无效的成分
            .map(ing => ({
              uuid: ing.ingredient_id,
              name: ing.name
            }));
          setIngredients(currentIngredients);
        }
        
        // 预填充表单数据
        form.setFieldsValue({ 
          name: productDetail.name,
          sku: productDetail.sku,
          product_type: productDetail.product_type,
          benefits: productDetail.benefits,
          brand_id: productDetail.brand_id,
          ingredients: productDetail.ingredients || [] // 预填充成分
        });
        setFormModalVisible(true);
      } else {
        message.error('获取产品详情失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 统一的表单提交处理
  const handleFormSubmit = async (values: { 
    name: string; 
    sku?: string; 
    product_type: string; 
    brand_id?: string;
    benefits?: string;
    ingredients?: Array<{
      ingredient_id: string;
      name: string;
      amount?: number;
      unit?: string;
    }>;
  }) => {
    try {
      if (isEditing && selectedProduct) {
        // 编辑模式
        const response = await entityManagementService.updateProduct(selectedProduct.uuid, values);
        if (response.success) {
          message.success('产品更新成功');
        } else {
          message.error(response.error || '更新产品失败');
          return;
        }
      } else {
        // 创建模式 - 支持品牌和成分选择
        const response = await entityManagementService.createProduct({
          ...values,
          brand_id: values.brand_id || '', // 品牌可选
          ingredients: values.ingredients || [] // 成分可选
        });
        if (response.success) {
          message.success('产品创建成功');
        } else {
          message.error(response.error || '创建产品失败');
          return;
        }
      }
      
      // 成功后关闭模态框并刷新数据
      setFormModalVisible(false);
      form.resetFields();
      setSelectedProduct(null);
      setIsEditing(false);
      // 清理成分搜索状态
      setIngredients([]);
      setLoadingIngredients(false);
      // 新增产品后回到第一页显示
      if (!isEditing) {
        setCurrentPage(1);
        loadProducts({ page: 1 });
      } else {
        loadProducts();
      }
      loadStats();
      
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 删除产品
  const handleDeleteProduct = async (productId: string) => {
    try {
      const response = await entityManagementService.deleteProduct(productId);
      if (response.success) {
        message.success('产品删除成功');
        loadProducts();
        loadStats();
      } else {
        message.error(response.error || '删除产品失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 重试向量化
  const handleRetryVectorization = async (_productId: string) => {
    try {
      // 这里需要根据实际API调用相应的重试接口
      // 暂时使用通用的重试接口，实际项目中可能需要调整
      message.success('重试向量化任务已提交');
      loadProducts(); // 刷新数据
    } catch (error) {
      message.error('重试向量化失败');
    }
  };

  // 获取产品类型颜色
  const getProductTypeColor = (type: string) => {
    const colorMap: { [key: string]: string } = {
      '保健品': 'blue',
      '营养品': 'green',
      '药品': 'red',
      '食品': 'orange',
    };
    return colorMap[type] || 'default';
  };

  // 表格列定义
  const columns: ColumnsType<ProductInfo> = [
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      width: 250,
      render: (text: string, record: ProductInfo) => (
        <div>
          <Button
            type="link"
            onClick={() => handleViewDetail(record.uuid)}
            style={{ padding: 0, fontSize: '14px' }}
          >
            {text}
          </Button>
          {record.sku && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              SKU: {record.sku}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '品牌',
      dataIndex: 'brand_name',
      key: 'brand_name',
      sorter: true,
      width: 150,
      render: (text: string) => (
        <Tag color="blue">{text}</Tag>
      ),
    },
    {
      title: '产品类型',
      dataIndex: 'product_type',
      key: 'product_type',
      width: 120,
      render: (type: string) => (
        <Tag color={getProductTypeColor(type)}>{type}</Tag>
      ),
    },
    {
      title: '成分数量',
      dataIndex: 'ingredient_count',
      key: 'ingredient_count',
      width: 120,
      render: (count: number) => (
        <Badge count={count} showZero color="#52c41a" />
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record: ProductInfo) => (
        <Tag color={record.is_active ? 'success' : 'default'}>
          {record.is_active ? '正常' : '停用'}
        </Tag>
      ),
    },
    {
      title: '向量化状态',
      key: 'vectorization_status',
      width: 160,
      render: (_, record: ProductInfo) => (
        <VectorizationStatus
          status={record.vectorization_status}
          attempts={record.vectorization_attempts}
          maxAttempts={3}
          error={record.vectorization_error}
          onRetry={() => handleRetryVectorization(record.uuid)}
          size="small"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: ProductInfo) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              onClick={() => handleViewDetail(record.uuid)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个产品吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDeleteProduct(record.uuid)}
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        <ShoppingOutlined /> 产品管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总产品数" value={stats.total_products || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="品牌覆盖" value={stats.active_brands || 0} suffix="个品牌" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="成分覆盖" value={stats.active_ingredients || 0} suffix="种成分" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic 
              title="平均成分数" 
              value={stats.total_products ? Math.round((stats.active_ingredients || 0) / stats.total_products * 10) / 10 : 0}
              suffix="种/产品"
            />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 操作工具栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Space wrap>
              <Input.Search
                placeholder="搜索产品名称或SKU"
                onSearch={handleSearch}
                style={{ width: 300 }}
                allowClear
              />
              <Input.Search
                placeholder="筛选品牌"
                onSearch={handleBrandFilterChange}
                style={{ width: 200 }}
                allowClear
              />
              <Select
                value={filterType}
                onChange={handleFilterChange}
                style={{ width: 120 }}
              >
                <Option value="all">全部产品</Option>
                <Option value="active">正常产品</Option>
                <Option value="unused">停用产品</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={openCreateModal}
              >
                新增产品
              </Button>
              {/* 导出功能暂时隐藏
              <Button icon={<ExportOutlined />}>
                导出数据
              </Button>
              */}
            </Space>
          </Col>
        </Row>

        {/* 产品列表表格 */}
        <Table
          columns={columns}
          dataSource={products}
          rowKey="uuid"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
          onChange={(_pagination, _filters, sorter: any) => {
            if (sorter.field) {
              setSortBy(sorter.field);
              setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
            }
          }}
        />
      </Card>

      {/* 产品详情模态框 */}
      <Modal
        title="产品详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedProduct(null);
        }}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedProduct && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>产品名称：</Text>
                <div style={{ marginTop: '4px', fontSize: '16px' }}>{selectedProduct.name}</div>
              </Col>
              <Col span={12}>
                <Text strong>状态：</Text>
                <div style={{ marginTop: '4px' }}>
                  <Tag color={selectedProduct.is_active ? 'success' : 'default'}>
                    {selectedProduct.is_active ? '正常' : '停用'}
                  </Tag>
                </div>
              </Col>
              
              {selectedProduct.sku && (
                <Col span={12}>
                  <Text strong>SKU：</Text>
                  <div style={{ marginTop: '4px' }}>{selectedProduct.sku}</div>
                </Col>
              )}
              
              <Col span={12}>
                <Text strong>产品类型：</Text>
                <div style={{ marginTop: '4px' }}>
                  <Tag color={getProductTypeColor(selectedProduct.product_type)}>
                    {selectedProduct.product_type}
                  </Tag>
                </div>
              </Col>
              
              <Col span={12}>
                <Text strong>品牌：</Text>
                <div style={{ marginTop: '4px' }}>
                  <Tag color="blue">{selectedProduct.brand_name}</Tag>
                </div>
              </Col>
              
              <Col span={12}>
                <Text strong>成分数量：</Text>
                <div style={{ marginTop: '4px' }}>
                  <Badge count={selectedProduct.ingredient_count} showZero color="#52c41a" />
                </div>
              </Col>
              
              <Col span={12}>
                <Text strong>创建时间：</Text>
                <div style={{ marginTop: '4px' }}>
                  {new Date(selectedProduct.created_at).toLocaleString('zh-CN')}
                </div>
              </Col>
              
              <Col span={12}>
                <Text strong>更新时间：</Text>
                <div style={{ marginTop: '4px' }}>
                  {new Date(selectedProduct.updated_at).toLocaleString('zh-CN')}
                </div>
              </Col>
              
              <Col span={12}>
                <Text strong>向量化状态：</Text>
                <div style={{ marginTop: '4px' }}>
                  <VectorizationStatus
                    status={selectedProduct.vectorization_status}
                    attempts={selectedProduct.vectorization_attempts}
                    maxAttempts={3}
                    error={selectedProduct.vectorization_error}
                    onRetry={() => handleRetryVectorization(selectedProduct.uuid)}
                    size="default"
                  />
                </div>
              </Col>
              
              {selectedProduct.benefits && (
                <Col span={24}>
                  <Text strong>产品功效：</Text>
                  <div style={{ 
                    marginTop: '4px', 
                    padding: '12px', 
                    backgroundColor: '#f5f5f5', 
                    borderRadius: '6px',
                    wordWrap: 'break-word'
                  }}>
                    {selectedProduct.benefits}
                  </div>
                </Col>
              )}
              
              {selectedProduct.ingredients && selectedProduct.ingredients.length > 0 && (
                <Col span={24}>
                  <Text strong>成分列表：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedProduct.ingredients.map((ingredient: any, index: number) => (
                      <div key={index} style={{ 
                        marginBottom: '8px', 
                        padding: '8px 12px', 
                        backgroundColor: '#f9f9f9', 
                        borderRadius: '4px',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center'
                      }}>
                        <span>{ingredient.name || ingredient.standard || '未知成分'}</span>
                        {(ingredient.amount || ingredient.unit) && (
                          <Tag color="green">
                            {ingredient.amount}{ingredient.unit}
                          </Tag>
                        )}
                      </div>
                    ))}
                  </div>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>

      {/* 产品表单模态框（新增/编辑复用） */}
      <Modal
        title={isEditing ? "编辑产品" : "新增产品"}
        open={formModalVisible}
        onCancel={() => {
          setFormModalVisible(false);
          form.resetFields();
          setSelectedProduct(null);
          setIsEditing(false);
          // 清理成分搜索状态
          setIngredients([]);
          setLoadingIngredients(false);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            label="产品名称"
            name="name"
            rules={[
              { required: true, message: '请输入产品名称' },
              { min: 2, max: 100, message: '产品名称长度为2-100个字符' }
            ]}
          >
            <Input placeholder="请输入产品名称" />
          </Form.Item>
          
          <Form.Item
            label="SKU"
            name="sku"
            rules={[
              { max: 50, message: 'SKU长度不能超过50个字符' }
            ]}
          >
            <Input placeholder="请输入产品SKU（可选）" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="产品类型"
                name="product_type"
                rules={[
                  { required: true, message: '请选择产品类型' }
                ]}
              >
                <Select placeholder="请选择产品类型">
                  <Select.Option value="护肤品">护肤品</Select.Option>
                  <Select.Option value="彩妆">彩妆</Select.Option>
                  <Select.Option value="保健品">保健品</Select.Option>
                  <Select.Option value="洗护用品">洗护用品</Select.Option>
                  <Select.Option value="其他">其他</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="品牌"
                name="brand_id"
                rules={[
                  { required: false, message: '请选择品牌' }
                ]}
              >
                <Select 
                  placeholder="请选择品牌（可选）" 
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {brands.map((brand: any) => (
                    <Select.Option key={brand.uuid} value={brand.uuid}>
                      {brand.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.List name="ingredients">
            {(fields, { add, remove }) => (
              <>
                <Form.Item label="成分列表（可选）">
                  <div style={{ marginBottom: '8px' }}>
                    {fields.map(({ key, name, ...restField }) => (
                      <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                        <Form.Item
                          {...restField}
                          name={[name, 'ingredient_id']}
                          rules={[{ required: true, message: '请选择成分' }]}
                          style={{ width: '200px', marginBottom: 0 }}
                        >
                          <Select
                            placeholder="选择或搜索成分"
                            showSearch
                            loading={loadingIngredients}
                            onSearch={searchIngredients}
                            filterOption={false}
                            notFoundContent={loadingIngredients ? <Spin size="small" /> : null}
                          >
                            {ingredients.map((ingredient, idx) => (
                              <Option key={`${ingredient.uuid}-${idx}`} value={ingredient.uuid}>
                                {ingredient.name}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, 'amount']}
                          style={{ width: '100px', marginBottom: 0 }}
                        >
                          <InputNumber
                            placeholder="用量"
                            min={0}
                            step={0.1}
                          />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, 'unit']}
                          style={{ width: '80px', marginBottom: 0 }}
                        >
                          <Input placeholder="单位" />
                        </Form.Item>
                        <MinusCircleOutlined onClick={() => remove(name)} />
                      </Space>
                    ))}
                    <Form.Item style={{ marginTop: '8px', marginBottom: 0 }}>
                      <Button
                        type="dashed"
                        onClick={() => add()}
                        block
                        icon={<PlusOutlined />}
                      >
                        添加成分
                      </Button>
                    </Form.Item>
                  </div>
                </Form.Item>
              </>
            )}
          </Form.List>
          
          <Form.Item
            label="产品功效"
            name="benefits"
            rules={[
              { max: 500, message: '产品功效描述不能超过500个字符' }
            ]}
          >
            <Input.TextArea 
              rows={4}
              placeholder="请输入产品功效描述（可选）"
              showCount
              maxLength={500}
            />
          </Form.Item>
          
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setFormModalVisible(false);
                form.resetFields();
                setSelectedProduct(null);
                setIsEditing(false);
                // 清理成分搜索状态
                setIngredients([]);
                setLoadingIngredients(false);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {isEditing ? "更新" : "创建产品"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductManagement;