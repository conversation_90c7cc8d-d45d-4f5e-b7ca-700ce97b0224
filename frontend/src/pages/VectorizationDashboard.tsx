import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Progress,
  Alert,
  Table,
  Button,
  Tag,
  Space,
  Spin,
  message,
  Modal,
  Descriptions,
  Empty,
} from 'antd';
import {
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  BugOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import {
  vectorizationService,
  type DashboardOverview,
  type HealthStatus,
  type VectorizationTaskDetail,
  type FailureAnalysis
} from '../services/vectorizationService';
import VectorizationStatus from '../components/VectorizationStatus';

const VectorizationDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [recentTasks, setRecentTasks] = useState<VectorizationTaskDetail[]>([]);
  const [failureAnalysis, setFailureAnalysis] = useState<FailureAnalysis[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // 加载Dashboard数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [overviewRes, healthRes, tasksRes, failuresRes] = await Promise.all([
        vectorizationService.getDashboardOverview(),
        vectorizationService.getHealthStatus(),
        vectorizationService.getTasks({ page: 1, size: 10 }),
        vectorizationService.getFailureAnalysis()
      ]);

      if (overviewRes.success) {
        setOverview(overviewRes.data);
      }

      if (healthRes.success) {
        setHealthStatus(healthRes.data);
      }

      if (tasksRes.success) {
        setRecentTasks(tasksRes.data);
      }

      if (failuresRes.success) {
        setFailureAnalysis(failuresRes.data);
      }
    } catch (error) {
      message.error('加载Dashboard数据失败');
      console.error('Dashboard load error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 重试失败任务
  const handleRetryTask = async (taskId: string) => {
    try {
      const result = await vectorizationService.retryTask(taskId);
      if (result.success) {
        message.success('重试任务已提交');
        await loadDashboardData(); // 刷新数据
      } else {
        message.error(result.error || '重试失败');
      }
    } catch (error) {
      message.error('重试任务失败');
    }
  };

  // 批量重试失败任务
  const handleBatchRetry = async () => {
    Modal.confirm({
      title: '批量重试',
      content: '确定要重试所有失败的向量化任务吗？',
      onOk: async () => {
        try {
          const result = await vectorizationService.batchRetry({ max_attempts_exceeded: false });
          if (result.success) {
            message.success(`已重试 ${result.data.retried_count} 个任务`);
            await loadDashboardData();
          } else {
            message.error(result.error || '批量重试失败');
          }
        } catch (error) {
          message.error('批量重试失败');
        }
      }
    });
  };

  // 获取健康状态颜色和图标
  const getHealthStatusConfig = (status: string) => {
    switch (status) {
      case 'healthy':
        return { color: 'success', icon: <CheckCircleOutlined />, text: '健康' };
      case 'warning':
        return { color: 'warning', icon: <WarningOutlined />, text: '警告' };
      case 'critical':
        return { color: 'error', icon: <BugOutlined />, text: '严重' };
      default:
        return { color: 'default', icon: <ExclamationCircleOutlined />, text: '未知' };
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // 任务表格列定义
  const taskColumns: ColumnsType<VectorizationTaskDetail> = [
    {
      title: '实体名称',
      dataIndex: 'entity_name',
      key: 'entity_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'entity_type',
      key: 'entity_type',
      width: 80,
      render: (type: string) => (
        <Tag color="blue">{type === 'brand' ? '品牌' : type === 'ingredient' ? '成分' : '产品'}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: any, record: VectorizationTaskDetail) => (
        <VectorizationStatus
          status={status}
          attempts={record.attempts}
          maxAttempts={record.max_attempts}
          error={record.error_message}
          onRetry={() => handleRetryTask(record.task_id)}
          size="small"
        />
      ),
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: number) => (
        <Tag color={priority > 5 ? 'red' : priority > 3 ? 'orange' : 'green'}>
          {priority > 5 ? '高' : priority > 3 ? '中' : '低'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString('zh-CN'),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载向量化监控数据...</div>
      </div>
    );
  }

  const healthConfig = healthStatus ? getHealthStatusConfig(healthStatus.overall_status) : null;

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <h2>向量化监控Dashboard</h2>
          <p style={{ color: '#666', margin: 0 }}>实时监控向量化任务状态和系统健康</p>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={refreshing}>
            刷新数据
          </Button>
          <Button type="primary" danger onClick={handleBatchRetry}>
            批量重试失败任务
          </Button>
        </Space>
      </div>

      {/* 系统健康警报 */}
      {healthStatus && healthStatus.issues.length > 0 && (
        <Alert
          message="系统健康警报"
          description={
            <ul style={{ marginBottom: 0 }}>
              {healthStatus.issues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          }
          type="warning"
          showIcon
          closable
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 概览统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总实体数"
              value={overview?.summary.total_entities || 0}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="已向量化"
              value={overview?.summary.vectorized_entities || 0}
              suffix={`/ ${overview?.summary.total_entities || 0}`}
            />
            <Progress 
              percent={overview?.summary.overall_coverage || 0} 
              size="small" 
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="队列积压"
              value={overview?.summary.queue_size || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: (overview?.summary.queue_size || 0) > 10 ? '#cf1322' : undefined }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="系统健康"
              value={healthConfig?.text || '未知'}
              prefix={healthConfig?.icon}
              valueStyle={{ color: healthConfig?.color === 'error' ? '#cf1322' : healthConfig?.color === 'warning' ? '#faad14' : '#389e0d' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 覆盖率详情和性能指标 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="覆盖率详情" size="small">
            {overview?.coverage_by_type.map((item) => (
              <div key={item.entity_type} style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
                  <span>{item.entity_type === 'brand' ? '品牌' : item.entity_type === 'ingredient' ? '成分' : '产品'}</span>
                  <span>{item.coverage_percentage.toFixed(1)}%</span>
                </div>
                <Progress 
                  percent={item.coverage_percentage} 
                  size="small"
                  status={item.coverage_percentage < 50 ? 'exception' : item.coverage_percentage < 80 ? 'active' : 'success'}
                />
                <div style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
                  已完成: {item.vectorized}, 待处理: {item.pending}, 失败: {item.failed}
                </div>
              </div>
            ))}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="性能指标" size="small">
            {healthStatus?.performance && (
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="平均向量化时间">
                  {healthStatus.performance.avg_vectorization_time}秒
                </Descriptions.Item>
                <Descriptions.Item label="队列吞吐量">
                  {healthStatus.performance.queue_throughput} 任务/小时
                </Descriptions.Item>
                <Descriptions.Item label="成功率">
                  <span style={{ color: healthStatus.performance.success_rate > 90 ? '#52c41a' : '#faad14' }}>
                    {healthStatus.performance.success_rate.toFixed(1)}%
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="活跃工作线程">
                  {healthStatus.performance.active_workers}
                </Descriptions.Item>
              </Descriptions>
            )}
          </Card>
        </Col>
      </Row>

      {/* 最近任务和失败分析 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={14}>
          <Card title="最近任务" size="small">
            <Table
              columns={taskColumns}
              dataSource={recentTasks}
              rowKey="task_id"
              size="small"
              pagination={false}
              scroll={{ x: 600 }}
              locale={{
                emptyText: <Empty description="暂无任务记录" />
              }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={10}>
          <Card title="失败分析" size="small">
            {failureAnalysis.length > 0 ? (
              <div>
                {failureAnalysis.map((analysis) => (
                  <div key={analysis.entity_type} style={{ marginBottom: 16 }}>
                    <div style={{ fontWeight: 500, marginBottom: 8 }}>
                      {analysis.entity_type === 'brand' ? '品牌' : analysis.entity_type === 'ingredient' ? '成分' : '产品'}
                      <Tag color="red" style={{ marginLeft: 8 }}>
                        {analysis.total_failures} 次失败
                      </Tag>
                    </div>
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                      重试成功率: {analysis.retry_success_rate.toFixed(1)}%
                    </div>
                    {analysis.common_errors.slice(0, 2).map((error, index) => (
                      <div key={index} style={{ fontSize: 12, color: '#ff4d4f' }}>
                        • {error.error} ({error.count} 次)
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ) : (
              <Empty description="暂无失败记录" />
            )}
          </Card>
        </Col>
      </Row>

      {/* 推荐操作 */}
      {healthStatus && healthStatus.recommendations.length > 0 && (
        <Card title="系统推荐" style={{ marginTop: 16 }} size="small">
          <ul style={{ marginBottom: 0 }}>
            {healthStatus.recommendations.map((rec, index) => (
              <li key={index}>{rec}</li>
            ))}
          </ul>
        </Card>
      )}
    </div>
  );
};

export default VectorizationDashboard;