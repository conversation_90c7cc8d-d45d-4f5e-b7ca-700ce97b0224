import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Select,
  DatePicker,
  Button,
  Table,
  Tag,
  Space,
  Modal,
  Alert,
  Progress,
  message,
  Descriptions,
  Divider,
  Statistic,
  Typography,
  Badge,
  Tooltip,
  Radio
} from 'antd';
import {
  FilterOutlined,
  DeleteOutlined,
  MergeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { 
  vectorizationService, 
  type EntityType, 
  type BatchOperationType, 
  type BatchOperationRequest
} from '../services/vectorizationService';
import { 
  entityManagementService, 
  type BrandInfo, 
  type IngredientInfo, 
  type ProductInfo
} from '../services/entityManagementService';
import VectorizationStatus from '../components/VectorizationStatus';

const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { Option } = Select;

// 实体联合类型
type EntityInfo = BrandInfo | IngredientInfo | ProductInfo;

// 筛选参数接口
interface FilterParams {
  entity_type?: EntityType;
  vectorization_status?: string;
  date_range?: [string, string];
  search?: string;
}

// 批量操作结果
interface OperationResult {
  total: number;
  success: number;
  failed: number;
  details: Array<{
    entity_id: string;
    entity_name: string;
    status: 'success' | 'failed' | 'skipped' | 'error';
    message: string;
  }>;
}

const BatchOperations: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [entities, setEntities] = useState<EntityInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<EntityInfo[]>([]);
  
  // 筛选参数
  const [filterParams, setFilterParams] = useState<FilterParams>({
    entity_type: 'Brand'
  });
  
  // 操作相关状态
  const [operationModalVisible, setOperationModalVisible] = useState(false);
  const [operationType, setOperationType] = useState<BatchOperationType>('start_vectorization');
  const [operationData, setOperationData] = useState<Record<string, any>>({});
  const [operationProgress, setOperationProgress] = useState(0);
  const [operationResult, setOperationResult] = useState<OperationResult | null>(null);
  const [executionModalVisible, setExecutionModalVisible] = useState(false);
  const [vectorizationTaskIds, setVectorizationTaskIds] = useState<string[]>([]);
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null);
  const [targetEntityId, setTargetEntityId] = useState<string>('');

  // 递归加载所有数据
  const loadAllPages = async (entityType: EntityType, search: string = '', page: number = 1, allData: EntityInfo[] = []): Promise<EntityInfo[]> => {
    let result;
    
    switch (entityType) {
      case 'Brand':
        result = await entityManagementService.getBrands({ 
          page, 
          size: 100,
          search,
          filter_type: 'all',
          sort_by: 'created_at',
          sort_order: 'desc'
        });
        break;
      case 'Ingredient':
        result = await entityManagementService.getIngredients({ 
          page, 
          size: 100,
          search,
          filter_type: 'all',
          sort_by: 'created_at',
          sort_order: 'desc'
        });
        break;
      case 'Product':
        result = await entityManagementService.getProducts({ 
          page, 
          size: 100,
          search,
          filter_type: 'all',
          sort_by: 'created_at',
          sort_order: 'desc'
        });
        break;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
    
    if (!result.success) {
      throw new Error(result.error || 'API调用失败');
    }
    
    const currentData = [...allData, ...result.data];
    
    // 如果当前页数据少于100，说明已经是最后一页
    if (result.data.length < 100) {
      return currentData;
    }
    
    // 递归加载下一页
    return loadAllPages(entityType, search, page + 1, currentData);
  };

  // 加载实体数据
  const loadEntities = async () => {
    if (!filterParams.entity_type) return;
    
    try {
      setLoading(true);
      
      // 加载所有页面的数据
      const allEntities = await loadAllPages(
        filterParams.entity_type, 
        filterParams.search || ''
      );
      
      let filteredEntities = allEntities;
      
      // 按向量化状态筛选
      if (filterParams.vectorization_status) {
        filteredEntities = filteredEntities.filter(entity => 
          entity.vectorization_status === filterParams.vectorization_status
        );
      }
      
      // 按日期范围筛选
      if (filterParams.date_range) {
        const [startDate, endDate] = filterParams.date_range;
        filteredEntities = filteredEntities.filter(entity => {
          const createdAt = dayjs(entity.created_at);
          return createdAt.isAfter(dayjs(startDate)) && createdAt.isBefore(dayjs(endDate));
        });
      }
      
      setEntities(filteredEntities);
    } catch (error) {
      message.error('加载数据时发生错误');
      console.error('Load entities error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 重置选择
  const resetSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  // 筛选变化处理
  const handleFilterChange = (key: keyof FilterParams, value: any) => {
    setFilterParams(prev => ({ ...prev, [key]: value }));
    resetSelection();
  };

  // 应用筛选
  const applyFilters = () => {
    loadEntities();
  };

  // 获取选中实体中待向量化的数量
  const getPendingCount = () => {
    return selectedRows.filter(row => row.vectorization_status === 'PENDING').length;
  };

  // 获取选中实体中已完成向量化的数量
  const getCompletedCount = () => {
    return selectedRows.filter(row => row.vectorization_status === 'COMPLETED').length;
  };

  // 快速筛选只显示待向量化的实体
  const showOnlyPending = () => {
    handleFilterChange('vectorization_status', 'PENDING');
    loadEntities();
  };

  // 查询向量化任务进度
  const checkVectorizationProgress = async (taskIds: string[]) => {
    try {
      const tasks = await vectorizationService.getTasks({ page: 1, size: 100 });
      if (tasks.success) {
        const relevantTasks = tasks.data.filter(task => taskIds.includes(task.task_id));
        const completed = relevantTasks.filter(task => task.status === 'COMPLETED' || task.status === 'FAILED').length;
        const total = taskIds.length;
        
        return {
          completed,
          total,
          progress: total > 0 ? Math.round((completed / total) * 100) : 0,
          tasks: relevantTasks
        };
      }
    } catch (error) {
      console.error('查询向量化进度失败:', error);
    }
    return { completed: 0, total: taskIds.length, progress: 0, tasks: [] };
  };

  // 开始进度监控
  const startProgressMonitoring = (taskIds: string[]) => {
    if (progressInterval) {
      clearInterval(progressInterval);
    }

    const interval = setInterval(async () => {
      const progress = await checkVectorizationProgress(taskIds);
      setOperationProgress(progress.progress);

      // 如果全部完成，停止轮询
      if (progress.completed === progress.total) {
        clearInterval(interval);
        setProgressInterval(null);
        
        // 更新最终结果
        const successCount = progress.tasks.filter(task => task.status === 'COMPLETED').length;
        const failedCount = progress.tasks.filter(task => task.status === 'FAILED').length;
        
        setOperationResult({
          total: progress.total,
          success: successCount,
          failed: failedCount,
          details: progress.tasks.map(task => ({
            entity_id: task.entity_id,
            entity_name: task.entity_name,
            status: task.status === 'COMPLETED' ? 'success' as const : 'failed' as const,
            message: task.status === 'COMPLETED' ? '向量化完成' : task.error_message || '向量化失败'
          }))
        });

        // 刷新实体列表
        setTimeout(() => {
          loadEntities();
          resetSelection();
        }, 1000);
      }
    }, 2000); // 每2秒查询一次

    setProgressInterval(interval);
  };

  // 进度监控在组件卸载时自动清理

  // 批量选择配置
  const rowSelection: TableRowSelection<EntityInfo> = {
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: EntityInfo[]) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
    getCheckboxProps: (record: EntityInfo) => ({
      disabled: false, // 可以根据需要设置禁用条件
      name: record.name,
    }),
  };

  // 执行批量操作
  const executeBatchOperation = async () => {
    if (selectedRows.length === 0) {
      message.warning('请选择要操作的实体');
      return;
    }

    if (!filterParams.entity_type) {
      message.error('请选择实体类型');
      return;
    }

    try {
      setOperationModalVisible(false);
      setExecutionModalVisible(true);
      setOperationProgress(0);

      const request: BatchOperationRequest = {
        entity_type: filterParams.entity_type,
        operation_type: operationType,
        entity_ids: selectedRows.map(row => row.uuid),
        operation_data: operationType === 'merge' 
          ? { ...operationData, target_entity_id: targetEntityId }
          : operationData
      };

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setOperationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const result = await vectorizationService.batchOperation(request);

      if (result.success) {
        // 如果是向量化操作，使用进度监控
        if (operationType === 'start_vectorization' && result.task_ids && result.task_ids.length > 0) {
          setVectorizationTaskIds(result.task_ids);
          setOperationProgress(0);
          
          // 显示初始结果
          setOperationResult({
            total: result.processed_count,
            success: result.success_count,
            failed: result.failed_count,
            details: result.details
          });
          
          // 智能消息提示
          if (result.failed_count > 0) {
            message.success(`向量化任务已提交！已加入队列: ${result.success_count} 个，跳过已完成: ${result.failed_count} 个`);
          } else {
            message.success(`向量化任务已提交！共 ${result.success_count} 个实体已加入处理队列`);
          }
          
          // 开始进度监控
          startProgressMonitoring(result.task_ids);
        } else {
          // 非向量化操作的处理
          clearInterval(progressInterval);
          setOperationProgress(100);
          
          setOperationResult({
            total: result.processed_count,
            success: result.success_count,
            failed: result.failed_count,
            details: result.details
          });
          
          message.success(`批量操作完成！成功: ${result.success_count}, 失败: ${result.failed_count}`);
          
          // 刷新数据
          setTimeout(() => {
            loadEntities();
            resetSelection();
          }, 1000);
        }
      } else {
        clearInterval(progressInterval);
        setOperationProgress(0);
        message.error(result.error || '批量操作失败');
        setOperationResult({
          total: selectedRows.length,
          success: 0,
          failed: selectedRows.length,
          details: selectedRows.map(row => ({
            entity_id: row.uuid,
            entity_name: row.name,
            status: 'failed' as const,
            message: result.error || '操作失败'
          }))
        });
      }
    } catch (error) {
      message.error('执行批量操作时发生错误');
      console.error('Batch operation error:', error);
    }
  };

  // 获取操作类型显示名称
  const getOperationTypeName = (type: BatchOperationType) => {
    switch (type) {
      case 'delete': return '删除';
      case 'merge': return '合并';
      case 'start_vectorization': return '开始向量化';
      default: return type;
    }
  };

  // 获取实体类型显示名称
  const getEntityTypeName = (type: EntityType) => {
    switch (type) {
      case 'Brand': return '品牌';
      case 'Ingredient': return '成分';
      case 'Product': return '产品';
      default: return type;
    }
  };

  // 表格列定义
  const columns: ColumnsType<EntityInfo> = [
    {
      title: '实体名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '向量化状态',
      key: 'vectorization_status',
      width: 160,
      render: (_, record) => (
        <VectorizationStatus
          status={record.vectorization_status}
          attempts={record.vectorization_attempts}
          maxAttempts={3}
          error={record.vectorization_error}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm'),
    },
  ];

  useEffect(() => {
    loadEntities();
  }, []);

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>批量操作管理</Title>
        <Text type="secondary">对实体进行批量删除、合并和状态更新操作</Text>
      </div>

      {/* 筛选面板 */}
      <Card title="筛选条件" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong>实体类型</Text>
              <Select
                style={{ width: '100%', marginTop: 8 }}
                value={filterParams.entity_type}
                onChange={(value) => handleFilterChange('entity_type', value)}
              >
                <Option value="Brand">品牌</Option>
                <Option value="Ingredient">成分</Option>
                <Option value="Product">产品</Option>
              </Select>
            </div>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div>
              <Text strong>向量化状态</Text>
              <Select
                style={{ width: '100%', marginTop: 8 }}
                value={filterParams.vectorization_status}
                onChange={(value) => handleFilterChange('vectorization_status', value)}
                allowClear
                placeholder="全部状态"
              >
                <Option value="PENDING">等待中</Option>
                <Option value="PROCESSING">处理中</Option>
                <Option value="COMPLETED">已完成</Option>
                <Option value="FAILED">失败</Option>
              </Select>
            </div>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <div>
              <Text strong>创建时间范围</Text>
              <RangePicker
                style={{ width: '100%', marginTop: 8 }}
                value={filterParams.date_range ? [dayjs(filterParams.date_range[0]), dayjs(filterParams.date_range[1])] : null}
                onChange={(dates) => {
                  if (dates) {
                    handleFilterChange('date_range', [dates[0]?.toISOString(), dates[1]?.toISOString()]);
                  } else {
                    handleFilterChange('date_range', undefined);
                  }
                }}
              />
            </div>
          </Col>
          <Col xs={24} sm={12} md={4}>
            <div style={{ marginTop: 32 }}>
              <Button
                type="primary"
                icon={<FilterOutlined />}
                onClick={applyFilters}
                loading={loading}
                style={{ width: '100%' }}
              >
                应用筛选
              </Button>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 操作面板 */}
      <Card title="批量操作" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col>
            <Space direction="vertical" size={4}>
              <div>
                <Text strong>已选择: </Text>
                <Text type={selectedRows.length > 0 ? 'success' : 'secondary'}>
                  {selectedRows.length} 个实体
                </Text>
              </div>
              {selectedRows.length > 0 && (
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    待向量化: {getPendingCount()} 个，已完成: {getCompletedCount()} 个
                  </Text>
                </div>
              )}
            </Space>
          </Col>
          <Col flex="auto">
            {getPendingCount() === 0 && selectedRows.length > 0 && (
              <Alert
                message="所选实体均已向量化"
                description="选中的实体都已完成向量化，无需重复处理。可点击右侧按钮快速筛选待向量化实体。"
                type="info"
                showIcon
                style={{ fontSize: '12px' }}
              />
            )}
          </Col>
          <Col>
            <Space>
              <Tooltip title={getPendingCount() === 0 && selectedRows.length > 0 ? '所选实体均已向量化' : ''}>
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={() => {
                    setOperationType('start_vectorization');
                    setOperationData({});
                    setOperationModalVisible(true);
                  }}
                  disabled={selectedRows.length === 0 || getPendingCount() === 0}
                >
                  开始向量化
                  {selectedRows.length > 0 && getPendingCount() > 0 && (
                    <Badge count={getPendingCount()} showZero={false} style={{ marginLeft: 8 }} />
                  )}
                </Button>
              </Tooltip>
              <Button
                icon={<FilterOutlined />}
                onClick={showOnlyPending}
                disabled={filterParams.vectorization_status === 'PENDING'}
              >
                仅显示待处理
              </Button>
              <Button
                icon={<MergeOutlined />}
                onClick={() => {
                  setOperationType('merge');
                  setOperationData({});
                  setTargetEntityId(selectedRows[0]?.uuid || ''); // 默认选择第一个实体
                  setOperationModalVisible(true);
                }}
                disabled={selectedRows.length < 2}
              >
                合并实体
              </Button>
              <Button
                icon={<DeleteOutlined />}
                danger
                onClick={() => {
                  setOperationType('delete');
                  setOperationData({});
                  setOperationModalVisible(true);
                }}
                disabled={selectedRows.length === 0}
              >
                删除实体
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card title={`${getEntityTypeName(filterParams.entity_type || 'Brand')}列表 (${entities.length})`}>
        <Table
          columns={columns}
          dataSource={entities}
          rowKey="uuid"
          rowSelection={rowSelection}
          loading={loading}
          style={{ fontSize: '12px' }}
          pagination={{
            total: entities.length,
            pageSize: 50,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 操作确认弹窗 */}
      <Modal
        title={`确认${getOperationTypeName(operationType)}`}
        open={operationModalVisible}
        onOk={executeBatchOperation}
        onCancel={() => setOperationModalVisible(false)}
        width={600}
        okText="确认执行"
        cancelText="取消"
        okButtonProps={{ 
          danger: operationType === 'delete',
          disabled: operationType === 'merge' && !targetEntityId
        }}
      >
        <Alert
          message={`即将对 ${selectedRows.length} 个${getEntityTypeName(filterParams.entity_type || 'Brand')}执行${getOperationTypeName(operationType)}操作`}
          description={
            operationType === 'start_vectorization' 
              ? `系统将只处理状态为"等待向量化"的实体，其他状态的实体将自动跳过。实际处理: ${getPendingCount()} 个，跳过: ${getCompletedCount()} 个`
              : operationType === 'delete' 
                ? '删除操作不可逆，请确认所选实体确实需要删除'
                : '合并操作将保留第一个实体，其他实体的关系将转移到第一个实体上'
          }
          type={operationType === 'delete' ? 'error' : operationType === 'start_vectorization' ? 'info' : 'warning'}
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Descriptions title="操作详情" style={{ fontSize: '12px' }} column={2}>
          <Descriptions.Item label="实体类型">
            {getEntityTypeName(filterParams.entity_type || 'Brand')}
          </Descriptions.Item>
          <Descriptions.Item label="操作类型">
            {getOperationTypeName(operationType)}
          </Descriptions.Item>
          <Descriptions.Item label="选中数量">
            {selectedRows.length} 个
          </Descriptions.Item>
          <Descriptions.Item label="操作时间">
            {dayjs().format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          {operationType === 'start_vectorization' && (
            <>
              <Descriptions.Item label="待处理">
                <Text type="warning">{getPendingCount()} 个</Text>
              </Descriptions.Item>
              <Descriptions.Item label="将跳过">
                <Text type="secondary">{getCompletedCount()} 个</Text>
              </Descriptions.Item>
            </>
          )}
        </Descriptions>

        <Divider />

        {/* 合并操作特殊界面 */}
        {operationType === 'merge' ? (
          <div>
            <Alert
              message="请选择要保留的实体"
              description="其他实体的数据和关系将被合并到保留的实体上，然后删除其他实体。此操作不可逆。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Text strong>选择要保留的实体:</Text>
            <div style={{ marginTop: 12, maxHeight: 300, overflow: 'auto' }}>
              <Radio.Group 
                value={targetEntityId} 
                onChange={(e) => setTargetEntityId(e.target.value)}
                style={{ width: '100%' }}
              >
                {selectedRows.map((row, index) => (
                  <div key={row.uuid} style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
                    <Radio value={row.uuid} style={{ alignItems: 'flex-start' }}>
                      <div style={{ marginLeft: 8 }}>
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                          <Text strong>{row.name}</Text>
                          {index === 0 && (
                            <Tag color="blue" style={{ fontSize: '12px', marginLeft: 8 }}>推荐</Tag>
                          )}
                        </div>
                        <div style={{ display: 'flex', gap: 16, fontSize: '12px' }}>
                          <Text type="secondary">创建时间: {dayjs(row.created_at).format('YYYY-MM-DD HH:mm')}</Text>
                          {'product_count' in row && row.product_count !== undefined && (
                            <Text type="secondary">关联产品: {row.product_count} 个</Text>
                          )}
                          {row.vectorization_status && (
                            <Tag 
                              style={{ fontSize: '12px' }} 
                              color={
                                row.vectorization_status === 'COMPLETED' ? 'green' : 
                                row.vectorization_status === 'PENDING' ? 'orange' :
                                row.vectorization_status === 'PROCESSING' ? 'blue' : 'red'
                              }
                            >
                              {row.vectorization_status === 'PENDING' ? '待向量化' : 
                               row.vectorization_status === 'COMPLETED' ? '已完成' :
                               row.vectorization_status === 'PROCESSING' ? '处理中' : '失败'}
                            </Tag>
                          )}
                        </div>
                      </div>
                    </Radio>
                  </div>
                ))}
              </Radio.Group>
            </div>
          </div>
        ) : (
          /* 其他操作的实体列表显示 */
          <div style={{ maxHeight: 200, overflow: 'auto' }}>
            <Text strong>将要操作的实体:</Text>
            {selectedRows.slice(0, 10).map((row, index) => (
              <div key={row.uuid} style={{ padding: '4px 0', display: 'flex', alignItems: 'center' }}>
                <Text style={{ flex: 1 }}>{index + 1}. {row.name}</Text>
                {row.vectorization_status && (
                  <Tag 
                    style={{ fontSize: '12px', marginLeft: 8 }} 
                    color={
                      row.vectorization_status === 'COMPLETED' ? 'green' : 
                      row.vectorization_status === 'PENDING' ? 'orange' :
                      row.vectorization_status === 'PROCESSING' ? 'blue' : 'red'
                    }
                  >
                    {row.vectorization_status === 'PENDING' ? '待向量化' : 
                     row.vectorization_status === 'COMPLETED' ? '已完成' :
                     row.vectorization_status === 'PROCESSING' ? '处理中' : '失败'}
                  </Tag>
                )}
                {operationType === 'start_vectorization' && row.vectorization_status !== 'PENDING' && (
                  <Text type="secondary" style={{ fontSize: '12px', marginLeft: 8 }}>将跳过</Text>
                )}
              </div>
            ))}
            {selectedRows.length > 10 && (
              <Text type="secondary">... 还有 {selectedRows.length - 10} 个实体</Text>
            )}
          </div>
        )}
      </Modal>

      {/* 执行结果弹窗 */}
      <Modal
        title="批量操作执行结果"
        open={executionModalVisible}
        onCancel={() => setExecutionModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setExecutionModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Progress
            type="circle"
            percent={operationProgress}
            status={operationProgress === 100 ? 'success' : 'active'}
            size={120}
            format={(percent) => `${percent}%`}
          />
          <div style={{ marginTop: 16 }}>
            <Text strong>
              {operationProgress === 100 
                ? '操作完成' 
                : operationType === 'start_vectorization' && vectorizationTaskIds.length > 0
                  ? '向量化处理中...'
                  : '正在执行...'
              }
            </Text>
            {operationType === 'start_vectorization' && operationProgress > 0 && operationProgress < 100 && (
              <div style={{ marginTop: 8 }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  正在处理向量化任务，请稍候...
                </Text>
              </div>
            )}
          </div>
        </div>

        {operationResult && (
          <>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={8}>
                <Statistic
                  title="总计"
                  value={operationResult.total}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="成功"
                  value={operationResult.success}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="失败"
                  value={operationResult.failed}
                  prefix={<WarningOutlined />}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>

            <Divider />

            <div style={{ maxHeight: 300, overflow: 'auto' }}>
              <Text strong>操作详情:</Text>
              {operationResult.details.map((detail) => (
                <div key={detail.entity_id} style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                  <Space>
                    <Tag color={detail.status === 'success' ? 'green' : 'red'}>
                      {detail.status === 'success' ? '成功' : '失败'}
                    </Tag>
                    <Text strong>{detail.entity_name}</Text>
                    <Text type="secondary">{detail.message}</Text>
                  </Space>
                </div>
              ))}
            </div>
          </>
        )}
      </Modal>
    </div>
  );
};

export default BatchOperations;