import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Row,
  Col,
  Modal,
  Form,
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Typography,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { 
  entityManagementService, 
  type IngredientInfo, 
  type IngredientDetail, 
  type IngredientListParams 
} from '../services/entityManagementService';
import VectorizationStatus from '../components/VectorizationStatus';

const { Option } = Select;
const { Title, Text } = Typography;

const IngredientManagement: React.FC = () => {
  const [ingredients, setIngredients] = useState<IngredientInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  
  // 筛选和搜索状态
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'active' | 'unused' | 'duplicate'>('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // 模态框状态
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedIngredient, setSelectedIngredient] = useState<IngredientDetail | null>(null);
  
  // 表单
  const [form] = Form.useForm();
  
  // 统计数据
  const [stats, setStats] = useState<any>({});

  // 加载成分列表
  const loadIngredients = async (params?: Partial<IngredientListParams>) => {
    setLoading(true);
    try {
      const queryParams: IngredientListParams = {
        page: currentPage,
        size: pageSize,
        search: searchText || undefined,
        filter_type: filterType,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...params
      };

      const response = await entityManagementService.getIngredients(queryParams);
      
      if (response.success) {
        // 确保数据去重，避免重复的uuid导致React key冲突
        const uniqueIngredients = response.data.filter((ingredient, index, self) => 
          index === self.findIndex(i => i.uuid === ingredient.uuid)
        );
        setIngredients(uniqueIngredients);
        setTotal(response.total);
      } else {
        message.error(response.error || '获取成分列表失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await entityManagementService.getEntityStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadIngredients();
    loadStats();
  }, [currentPage, pageSize, filterType, sortBy, sortOrder]);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
    loadIngredients({ page: 1, search: value });
  };

  // 筛选处理
  const handleFilterChange = (value: 'all' | 'active' | 'unused' | 'duplicate') => {
    setFilterType(value);
    setCurrentPage(1);
  };

  // 查看详情
  const handleViewDetail = async (ingredientId: string) => {
    setLoading(true);
    try {
      const response = await entityManagementService.getIngredientDetail(ingredientId);
      if (response.success && response.data) {
        setSelectedIngredient(response.data);
        setDetailModalVisible(true);
      } else {
        message.error('获取成分详情失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 打开创建模态框
  const openCreateModal = () => {
    setIsEditing(false);
    form.resetFields();
    setSelectedIngredient(null);
    setFormModalVisible(true);
  };

  // 打开编辑模态框
  const openEditModal = (ingredient: IngredientInfo) => {
    setIsEditing(true);
    setSelectedIngredient(ingredient as IngredientDetail);
    form.setFieldsValue({ name: ingredient.name });
    setFormModalVisible(true);
  };

  // 统一的表单提交处理
  const handleFormSubmit = async (values: { name: string }) => {
    try {
      if (isEditing && selectedIngredient) {
        // 编辑模式
        const response = await entityManagementService.updateIngredient(selectedIngredient.uuid, values);
        if (response.success) {
          message.success('成分更新成功');
        } else {
          message.error(response.error || '更新成分失败');
          return;
        }
      } else {
        // 创建模式
        const response = await entityManagementService.createIngredient(values);
        if (response.success) {
          message.success('成分创建成功');
        } else {
          message.error(response.error || '创建成分失败');
          return;
        }
      }
      
      // 成功后关闭模态框并刷新数据
      setFormModalVisible(false);
      form.resetFields();
      setSelectedIngredient(null);
      setIsEditing(false);
      // 新增成分后回到第一页显示
      if (!isEditing) {
        setCurrentPage(1);
        loadIngredients({ page: 1 });
      } else {
        loadIngredients();
      }
      loadStats();
      
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 删除成分
  const handleDeleteIngredient = async (ingredientId: string) => {
    try {
      const response = await entityManagementService.deleteIngredient(ingredientId);
      if (response.success) {
        message.success('成分删除成功');
        loadIngredients();
        loadStats();
      } else {
        message.error(response.error || '删除成分失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 重试向量化
  const handleRetryVectorization = async (ingredientId: string) => {
    void ingredientId; // 参数暂未使用，预留给未来API实现
    try {
      // 这里需要根据实际API调用相应的重试接口
      // 暂时使用通用的重试接口，实际项目中可能需要调整
      message.success('重试向量化任务已提交');
      loadIngredients(); // 刷新数据
    } catch (error) {
      message.error('重试向量化失败');
    }
  };


  // 表格列定义
  const columns: ColumnsType<IngredientInfo> = [
    {
      title: '成分名称',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (text: string, record: IngredientInfo) => (
        <Space>
          <Button
            type="link"
            onClick={() => handleViewDetail(record.uuid)}
            style={{ padding: 0 }}
          >
            {text}
          </Button>
          {!record.is_active && (
            <Tag color="orange">未使用</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '产品数量',
      dataIndex: 'product_count',
      key: 'product_count',
      sorter: true,
      width: 120,
      render: (count: number) => (
        <Tag color={count > 0 ? 'green' : 'default'}>
          {count} 个产品
        </Tag>
      ),
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record: IngredientInfo) => (
        <Tag color={record.is_active ? 'success' : 'default'}>
          {record.is_active ? '活跃' : '未使用'}
        </Tag>
      ),
    },
    {
      title: '向量化状态',
      key: 'vectorization_status',
      width: 160,
      render: (_, record: IngredientInfo) => (
        <VectorizationStatus
          status={record.vectorization_status}
          attempts={record.vectorization_attempts}
          maxAttempts={3}
          error={record.vectorization_error}
          onRetry={() => handleRetryVectorization(record.uuid)}
          size="small"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: IngredientInfo) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              onClick={() => handleViewDetail(record.uuid)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个成分吗？"
            description={record.is_active ? "该成分有关联产品，无法删除" : undefined}
            onConfirm={() => handleDeleteIngredient(record.uuid)}
            disabled={record.is_active}
          >
            <Tooltip title={record.is_active ? "有关联产品，无法删除" : "删除"}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                disabled={record.is_active}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        成分管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总成分数" value={stats.total_ingredients || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="活跃成分" value={stats.active_ingredients || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="未使用成分" value={stats.unused_ingredients || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总产品数" value={stats.total_products || 0} />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 操作工具栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Input.Search
                placeholder="搜索成分名称"
                onSearch={handleSearch}
                style={{ width: 300 }}
                allowClear
              />
              <Select
                value={filterType}
                onChange={handleFilterChange}
                style={{ width: 150 }}
              >
                <Option value="all">全部成分</Option>
                <Option value="active">活跃成分</Option>
                <Option value="unused">未使用成分</Option>
                <Option value="duplicate">疑似重复</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={openCreateModal}
              >
                新增成分
              </Button>
              {/* 导出功能暂时隐藏
              <Button icon={<ExportOutlined />}>
                导出数据
              </Button>
              */}
            </Space>
          </Col>
        </Row>

        {/* 成分列表表格 */}
        <Table
          columns={columns}
          dataSource={ingredients}
          rowKey="uuid"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
          onChange={(_pagination, _filters, sorter: any) => {
            if (sorter.field) {
              setSortBy(sorter.field);
              setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
            }
          }}
        />
      </Card>

      {/* 成分表单模态框（新增/编辑复用） */}
      <Modal
        title={isEditing ? "编辑成分" : "新增成分"}
        open={formModalVisible}
        onCancel={() => {
          setFormModalVisible(false);
          form.resetFields();
          setSelectedIngredient(null);
          setIsEditing(false);
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            label="成分名称"
            name="name"
            rules={[
              { required: true, message: '请输入成分名称' },
              { min: 1, max: 30, message: '成分名称长度为1-30个字符' }
            ]}
          >
            <Input placeholder="请输入成分名称" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setFormModalVisible(false);
                form.resetFields();
                setSelectedIngredient(null);
                setIsEditing(false);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {isEditing ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 成分详情模态框 */}
      <Modal
        title="成分详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedIngredient(null);
        }}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedIngredient && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>成分名称：</Text>
                <div>{selectedIngredient.name}</div>
              </Col>
              <Col span={12}>
                <Text strong>状态：</Text>
                <div>
                  <Tag color={selectedIngredient.is_active ? 'success' : 'default'}>
                    {selectedIngredient.is_active ? '活跃' : '未使用'}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <Text strong>产品数量：</Text>
                <div>{selectedIngredient.product_count} 个</div>
              </Col>
              <Col span={12}>
                <Text strong>创建时间：</Text>
                <div>{new Date(selectedIngredient.created_at).toLocaleString('zh-CN')}</div>
              </Col>
              <Col span={12}>
                <Text strong>向量化状态：</Text>
                <div>
                  <VectorizationStatus
                    status={selectedIngredient.vectorization_status}
                    attempts={selectedIngredient.vectorization_attempts}
                    maxAttempts={3}
                    error={selectedIngredient.vectorization_error}
                    onRetry={() => handleRetryVectorization(selectedIngredient.uuid)}
                    size="default"
                  />
                </div>
              </Col>
              
              {selectedIngredient.total_amount && (
                <Col span={12}>
                  <Text strong>总含量：</Text>
                  <div>{selectedIngredient.total_amount} {selectedIngredient.common_unit}</div>
                </Col>
              )}
              
              {selectedIngredient.products && selectedIngredient.products.length > 0 && (
                <Col span={24}>
                  <Text strong>关联产品：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedIngredient.products.map((product, index) => (
                      <Tag key={index} style={{ marginBottom: '4px' }}>
                        {product}
                      </Tag>
                    ))}
                  </div>
                </Col>
              )}
              
              {selectedIngredient.related_brands && selectedIngredient.related_brands.length > 0 && (
                <Col span={24}>
                  <Text strong>相关品牌：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedIngredient.related_brands.map((brand, index) => (
                      <Tag key={index} color="blue" style={{ marginBottom: '4px' }}>
                        {brand}
                      </Tag>
                    ))}
                  </div>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default IngredientManagement;