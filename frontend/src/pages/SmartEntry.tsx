import React from 'react';
import { Typography, Space, Alert, Row, Col } from 'antd';
import { EnhancedExtractionPanel } from '../components/EnhancedExtractionPanel';
import { EntityLinkingPanel } from '../components/EntityLinkingPanel';

const { Title } = Typography;

export const SmartEntry: React.FC = () => {
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* 页面标题和说明 */}
        <div>
          <Title level={2}>智能产品录入</Title>
          <Alert
            message="智能录入模式"
            description="使用AI提取功能快速识别产品信息。提取完成后将自动进入实体确认步骤，必须完成实体确认才能保存产品，确保数据质量。"
            type="success" 
            showIcon
            style={{ marginBottom: 24 }}
          />
        </div>

        {/* 左右分栏布局 */}
        <Row gutter={24} style={{ minHeight: '75vh', alignItems: 'stretch' }}>
          {/* 左侧：AI提取面板 */}
          <Col xs={24} lg={12} style={{ display: 'flex', flexDirection: 'column' }}>
            <div style={{ height: '100%' }}>
              <EnhancedExtractionPanel />
            </div>
          </Col>
          
          {/* 右侧：实体确认面板 */}
          <Col xs={24} lg={12} style={{ display: 'flex', flexDirection: 'column' }}>
            <div style={{ height: '100%', minHeight: '75vh' }}>
              <EntityLinkingPanel />
            </div>
          </Col>
        </Row>
      </Space>
    </div>
  );
};