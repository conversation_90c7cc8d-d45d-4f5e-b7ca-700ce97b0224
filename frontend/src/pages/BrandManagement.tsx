import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Row,
  Col,
  Modal,
  Form,
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Typography,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { entityManagementService, type BrandInfo as ServiceBrandInfo, type BrandDetail as ServiceBrandDetail } from '../services/entityManagementService';
import { vectorizationService } from '../services/vectorizationService';
import VectorizationStatus from '../components/VectorizationStatus';

const { Option } = Select;
const { Title, Text } = Typography;

// 使用服务层定义的类型（已包含向量化状态）
type BrandInfo = ServiceBrandInfo;
type BrandDetail = ServiceBrandDetail;

interface BrandListParams {
  page: number;
  size: number;
  search?: string;
  filter_type: 'all' | 'active' | 'unused' | 'duplicate';
  sort_by: string;
  sort_order: 'asc' | 'desc';
}

const BrandManagement: React.FC = () => {
  const [brands, setBrands] = useState<BrandInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  
  // 筛选和搜索状态
  const [searchText, setSearchText] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'active' | 'unused' | 'duplicate'>('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // 模态框状态
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<BrandDetail | null>(null);
  
  // 批量操作状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [batchLoading, setBatchLoading] = useState(false);
  
  // 表单
  const [form] = Form.useForm();
  
  // 统计数据
  const [stats, setStats] = useState<any>({});

  // 加载品牌列表
  const loadBrands = async (params?: Partial<BrandListParams>) => {
    setLoading(true);
    try {
      const queryParams: BrandListParams = {
        page: currentPage,
        size: pageSize,
        search: searchText || undefined,
        filter_type: filterType,
        sort_by: sortBy,
        sort_order: sortOrder,
        ...params
      };

      const response = await entityManagementService.getBrands(queryParams);
      
      if (response.success) {
        // 确保数据去重，避免重复的uuid导致React key冲突
        const uniqueBrands = response.data.filter((brand, index, self) => 
          index === self.findIndex(b => b.uuid === brand.uuid)
        );
        setBrands(uniqueBrands);
        setTotal(response.total);
      } else {
        message.error(response.error || '获取品牌列表失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await entityManagementService.getEntityStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadBrands();
    loadStats();
  }, [currentPage, pageSize, filterType, sortBy, sortOrder]);

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
    loadBrands({ page: 1, search: value });
  };

  // 筛选处理
  const handleFilterChange = (value: 'all' | 'active' | 'unused' | 'duplicate') => {
    setFilterType(value);
    setCurrentPage(1);
  };

  // 查看详情
  const handleViewDetail = async (brandId: string) => {
    setLoading(true);
    try {
      const response = await entityManagementService.getBrandDetail(brandId);
      if (response.success && response.data) {
        setSelectedBrand(response.data);
        setDetailModalVisible(true);
      } else {
        message.error('获取品牌详情失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 打开创建模态框
  const openCreateModal = () => {
    setIsEditing(false);
    form.resetFields();
    setSelectedBrand(null);
    setFormModalVisible(true);
  };

  // 打开编辑模态框
  const openEditModal = (brand: BrandInfo) => {
    setIsEditing(true);
    setSelectedBrand(brand as BrandDetail);
    form.setFieldsValue({ name: brand.name });
    setFormModalVisible(true);
  };

  // 统一的表单提交处理
  const handleFormSubmit = async (values: { name: string }) => {
    try {
      if (isEditing && selectedBrand) {
        // 编辑模式
        const response = await entityManagementService.updateBrand(selectedBrand.uuid, values);
        if (response.success) {
          message.success('品牌更新成功');
        } else {
          message.error(response.error || '更新品牌失败');
          return;
        }
      } else {
        // 创建模式
        const response = await entityManagementService.createBrand(values);
        if (response.success) {
          message.success('品牌创建成功');
        } else {
          message.error(response.error || '创建品牌失败');
          return;
        }
      }
      
      // 成功后关闭模态框并刷新数据
      setFormModalVisible(false);
      form.resetFields();
      setSelectedBrand(null);
      setIsEditing(false);
      // 新增品牌后回到第一页显示
      if (!isEditing) {
        setCurrentPage(1);
        loadBrands({ page: 1 });
      } else {
        loadBrands();
      }
      loadStats();
      
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 删除品牌
  const handleDeleteBrand = async (brandId: string) => {
    try {
      const response = await entityManagementService.deleteBrand(brandId);
      if (response.success) {
        message.success('品牌删除成功');
        loadBrands();
        loadStats();
      } else {
        message.error(response.error || '删除品牌失败');
      }
    } catch (error) {
      message.error('网络错误，请稍后重试');
    }
  };

  // 重试向量化
  const handleRetryVectorization = async (_entityId: string, entityName: string) => {
    try {
      // 这里调用向量化服务的重试API（将来实现）
      message.success(`${entityName} 的向量化重试已提交`);
      // 刷新数据以更新状态
      setTimeout(() => {
        loadBrands();
      }, 1000);
    } catch (error) {
      message.error('重试向量化失败');
    }
  };

  // 批量删除品牌
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的品牌');
      return;
    }
    
    Modal.confirm({
      title: '批量删除品牌',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个品牌吗？此操作无法撤销。`,
      onOk: async () => {
        setBatchLoading(true);
        try {
          const response = await entityManagementService.batchOperation({
            entity_type: 'Brand',
            operation_type: 'delete',
            entity_ids: selectedRowKeys
          });
          
          if (response.success) {
            message.success(`成功删除 ${response.data.success_count} 个品牌`);
            if (response.data.failed_count > 0) {
              message.warning(`${response.data.failed_count} 个品牌删除失败`);
            }
            setSelectedRowKeys([]);
            loadBrands();
            loadStats();
          } else {
            message.error(response.error || '批量删除失败');
          }
        } catch (error) {
          message.error('批量删除操作失败');
        } finally {
          setBatchLoading(false);
        }
      }
    });
  };

  // 批量重试向量化
  const handleBatchRetryVectorization = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要重试的品牌');
      return;
    }
    
    Modal.confirm({
      title: '批量重试向量化',
      content: `确定要重试选中的 ${selectedRowKeys.length} 个品牌的向量化任务吗？`,
      onOk: async () => {
        setBatchLoading(true);
        try {
          const response = await vectorizationService.batchRetry({
            entity_type: 'brand',
            limit: selectedRowKeys.length
          });
          
          if (response.success) {
            message.success(`已提交 ${response.data.retried_count} 个重试任务`);
            setSelectedRowKeys([]);
            loadBrands();
          } else {
            message.error(response.error || '批量重试失败');
          }
        } catch (error) {
          message.error('批量重试操作失败');
        } finally {
          setBatchLoading(false);
        }
      }
    });
  };

  // 批量更新状态
  const handleBatchUpdateStatus = async (isActive: boolean) => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要更新的品牌');
      return;
    }
    
    const statusText = isActive ? '启用' : '禁用';
    Modal.confirm({
      title: `批量${statusText}品牌`,
      content: `确定要${statusText}选中的 ${selectedRowKeys.length} 个品牌吗？`,
      onOk: async () => {
        setBatchLoading(true);
        try {
          const response = await entityManagementService.batchOperation({
            entity_type: 'Brand',
            operation_type: 'update_status',
            entity_ids: selectedRowKeys,
            operation_data: { is_active: isActive }
          });
          
          if (response.success) {
            message.success(`成功${statusText} ${response.data.success_count} 个品牌`);
            if (response.data.failed_count > 0) {
              message.warning(`${response.data.failed_count} 个品牌更新失败`);
            }
            setSelectedRowKeys([]);
            loadBrands();
            loadStats();
          } else {
            message.error(response.error || `批量${statusText}失败`);
          }
        } catch (error) {
          message.error(`批量${statusText}操作失败`);
        } finally {
          setBatchLoading(false);
        }
      }
    });
  };

  // 表格列定义
  const columns: ColumnsType<BrandInfo> = [
    {
      title: '品牌名称',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
      render: (text: string, record: BrandInfo) => (
        <Space>
          <Button
            type="link"
            onClick={() => handleViewDetail(record.uuid)}
            style={{ padding: 0 }}
          >
            {text}
          </Button>
          {!record.is_active && (
            <Tag color="orange">未使用</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '产品数量',
      dataIndex: 'product_count',
      key: 'product_count',
      sorter: true,
      width: 120,
      render: (count: number) => (
        <Tag color={count > 0 ? 'green' : 'default'}>
          {count} 个产品
        </Tag>
      ),
    },
    {
      title: '使用状态',
      key: 'status',
      width: 100,
      render: (_, record: BrandInfo) => (
        <Tag color={record.is_active ? 'success' : 'default'}>
          {record.is_active ? '活跃' : '未使用'}
        </Tag>
      ),
    },
    {
      title: '向量化状态',
      key: 'vectorization_status',
      width: 150,
      render: (_, record: BrandInfo) => (
        <VectorizationStatus
          status={record.vectorization_status}
          attempts={record.vectorization_attempts}
          error={record.vectorization_error}
          onRetry={() => handleRetryVectorization(record.uuid, record.name)}
          size="small"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      width: 180,
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record: BrandInfo) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              onClick={() => handleViewDetail(record.uuid)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openEditModal(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个品牌吗？"
            description={record.is_active ? "该品牌有关联产品，无法删除" : undefined}
            onConfirm={() => handleDeleteBrand(record.uuid)}
            disabled={record.is_active}
          >
            <Tooltip title={record.is_active ? "有关联产品，无法删除" : "删除"}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                disabled={record.is_active}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        品牌管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic title="总品牌数" value={stats.total_brands || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="活跃品牌" value={stats.active_brands || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="未使用品牌" value={stats.unused_brands || 0} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总产品数" value={stats.total_products || 0} />
          </Card>
        </Col>
      </Row>

      <Card>
        {/* 操作工具栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col span={14}>
            <Space wrap>
              <Input.Search
                placeholder="搜索品牌名称"
                onSearch={handleSearch}
                style={{ width: 300 }}
                allowClear
              />
              <Select
                value={filterType}
                onChange={handleFilterChange}
                style={{ width: 150 }}
              >
                <Option value="all">全部品牌</Option>
                <Option value="active">活跃品牌</Option>
                <Option value="unused">未使用品牌</Option>
                <Option value="duplicate">疑似重复</Option>
              </Select>
            </Space>
          </Col>
          <Col span={10}>
            <Space wrap>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={openCreateModal}
              >
                新增品牌
              </Button>
            </Space>
          </Col>
        </Row>
        
        {/* 批量操作栏 */}
        {selectedRowKeys.length > 0 && (
          <Row style={{ marginBottom: 16, padding: '12px', backgroundColor: '#f0f2ff', borderRadius: '6px' }}>
            <Col span={24}>
              <Space wrap>
                <span style={{ color: '#1890ff', fontWeight: 500 }}>
                  已选择 {selectedRowKeys.length} 个品牌
                </span>
                <Button 
                  size="small" 
                  onClick={() => setSelectedRowKeys([])}
                >
                  取消选择
                </Button>
                <Button 
                  size="small" 
                  type="primary" 
                  loading={batchLoading}
                  onClick={handleBatchRetryVectorization}
                >
                  批量重试向量化
                </Button>
                <Button 
                  size="small" 
                  loading={batchLoading}
                  onClick={() => handleBatchUpdateStatus(true)}
                >
                  批量启用
                </Button>
                <Button 
                  size="small" 
                  loading={batchLoading}
                  onClick={() => handleBatchUpdateStatus(false)}
                >
                  批量禁用
                </Button>
                <Button 
                  size="small" 
                  danger 
                  loading={batchLoading}
                  onClick={handleBatchDelete}
                >
                  批量删除
                </Button>
              </Space>
            </Col>
          </Row>
        )}

        {/* 品牌列表表格 */}
        <Table
          columns={columns}
          dataSource={brands}
          rowKey="uuid"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedRowKeys(selectedRowKeys as string[]);
            },
            getCheckboxProps: (_record: BrandInfo) => ({
              disabled: loading || batchLoading,
            }),
          }}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
            },
          }}
          onChange={(_pagination, _filters, sorter: any) => {
            if (sorter.field) {
              setSortBy(sorter.field);
              setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
            }
          }}
        />
      </Card>

      {/* 品牌表单模态框（新增/编辑复用） */}
      <Modal
        title={isEditing ? "编辑品牌" : "新增品牌"}
        open={formModalVisible}
        onCancel={() => {
          setFormModalVisible(false);
          form.resetFields();
          setSelectedBrand(null);
          setIsEditing(false);
        }}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            label="品牌名称"
            name="name"
            rules={[
              { required: true, message: '请输入品牌名称' },
              { min: 2, max: 50, message: '品牌名称长度为2-50个字符' }
            ]}
          >
            <Input placeholder="请输入品牌名称" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setFormModalVisible(false);
                form.resetFields();
                setSelectedBrand(null);
                setIsEditing(false);
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {isEditing ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 品牌详情模态框 */}
      <Modal
        title="品牌详情"
        open={detailModalVisible}
        onCancel={() => {
          setDetailModalVisible(false);
          setSelectedBrand(null);
        }}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedBrand && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Text strong>品牌名称：</Text>
                <div>{selectedBrand.name}</div>
              </Col>
              <Col span={12}>
                <Text strong>状态：</Text>
                <div>
                  <Tag color={selectedBrand.is_active ? 'success' : 'default'}>
                    {selectedBrand.is_active ? '活跃' : '未使用'}
                  </Tag>
                </div>
              </Col>
              <Col span={12}>
                <Text strong>产品数量：</Text>
                <div>{selectedBrand.product_count} 个</div>
              </Col>
              <Col span={12}>
                <Text strong>创建时间：</Text>
                <div>{new Date(selectedBrand.created_at).toLocaleString('zh-CN')}</div>
              </Col>
              <Col span={12}>
                <Text strong>向量化状态：</Text>
                <div>
                  <VectorizationStatus
                    status={selectedBrand.vectorization_status}
                    attempts={selectedBrand.vectorization_attempts}
                    error={selectedBrand.vectorization_error}
                    onRetry={() => handleRetryVectorization(selectedBrand.uuid, selectedBrand.name)}
                  />
                </div>
              </Col>
              
              {selectedBrand.products && selectedBrand.products.length > 0 && (
                <Col span={24}>
                  <Text strong>关联产品：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedBrand.products.map((product, index) => (
                      <Tag key={index} style={{ marginBottom: '4px' }}>
                        {product}
                      </Tag>
                    ))}
                  </div>
                </Col>
              )}
              
              {selectedBrand.related_ingredients && selectedBrand.related_ingredients.length > 0 && (
                <Col span={24}>
                  <Text strong>相关成分：</Text>
                  <div style={{ marginTop: '8px' }}>
                    {selectedBrand.related_ingredients.map((ingredient, index) => (
                      <Tag key={index} color="blue" style={{ marginBottom: '4px' }}>
                        {ingredient}
                      </Tag>
                    ))}
                  </div>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default BrandManagement;