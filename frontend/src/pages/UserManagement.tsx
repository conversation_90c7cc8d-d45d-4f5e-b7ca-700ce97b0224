import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Row,
  Col,
  Modal,
  Form,
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Typography,
  Statistic,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuthStore } from '../stores/authStore';
import type { User, UserCreateRequest, UserUpdateRequest } from '../types/auth';

const { Option } = Select;
const { Title } = Typography;

const UserManagement: React.FC = () => {
  const {
    users,
    userStats,
    userListLoading,
    totalUsers,
    currentPage,
    pageSize,
    getUsers,
    createUser,
    updateUser,
    updateUserStatus,
    deleteUser,
    getUserStats,
    setCurrentPage,
    setPageSize,
  } = useAuthStore();

  const [searchText, setSearchText] = useState('');
  const [filterActive, setFilterActive] = useState<boolean | undefined>();
  const [filterAdmin, setFilterAdmin] = useState<boolean | undefined>();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();
  const [showPasswordField, setShowPasswordField] = useState(false);

  // 初始加载数据
  useEffect(() => {
    loadData();
  }, []);

  // 加载数据
  const loadData = async () => {
    await Promise.all([
      getUsers({
        page: currentPage,
        size: pageSize,
        search: searchText || undefined,
        is_active: filterActive,
        is_admin: filterAdmin,
      }),
      getUserStats(),
    ]);
  };

  // 搜索处理
  const handleSearch = (value: string) => {
    setSearchText(value);
    setCurrentPage(1);
    getUsers({
      page: 1,
      size: pageSize,
      search: value || undefined,
      is_active: filterActive,
      is_admin: filterAdmin,
    });
  };

  // 筛选处理
  const handleFilterChange = () => {
    setCurrentPage(1);
    getUsers({
      page: 1,
      size: pageSize,
      search: searchText || undefined,
      is_active: filterActive,
      is_admin: filterAdmin,
    });
  };

  // 打开新建用户模态框
  const openCreateModal = () => {
    setEditingUser(null);
    form.resetFields();
    form.setFieldsValue({ is_admin: false });
    setModalVisible(true);
  };

  // 打开编辑用户模态框
  const openEditModal = (user: User) => {
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      full_name: user.full_name,
      is_admin: user.is_admin,
      is_active: user.is_active,
    });
    setShowPasswordField(false);
    setModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      if (editingUser) {
        // 编辑用户
        const updateData: UserUpdateRequest = {
          email: values.email || undefined,
          full_name: values.full_name || undefined,
          is_admin: values.is_admin,
          is_active: values.is_active,
          password: values.password || undefined,
        };
        await updateUser(editingUser.id, updateData);
        message.success('用户更新成功');
      } else {
        // 新建用户
        const createData: UserCreateRequest = {
          username: values.username,
          email: values.email || undefined,
          password: values.password,
          full_name: values.full_name || undefined,
          is_admin: values.is_admin || false,
        };
        await createUser(createData);
        message.success('用户创建成功');
      }
      setModalVisible(false);
      form.resetFields();
      setEditingUser(null);
      setShowPasswordField(false);
    } catch (error: any) {
      message.error(error.message);
    }
  };

  // 处理用户状态切换
  const handleStatusChange = async (userId: number, isActive: boolean) => {
    try {
      await updateUserStatus(userId, isActive);
      message.success(`用户${isActive ? '启用' : '禁用'}成功`);
    } catch (error: any) {
      message.error(error.message);
    }
  };

  // 处理用户删除
  const handleDelete = async (userId: number) => {
    try {
      await deleteUser(userId);
      message.success('用户删除成功');
    } catch (error: any) {
      message.error(error.message);
    }
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string, record: User) => (
        <Space>
          <UserOutlined />
          <span>{text}</span>
          {record.is_admin && (
            <Tag color="red" style={{ fontSize: '12px' }}>管理员</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (text: string) => text || '-',
    },
    {
      title: '全名',
      dataIndex: 'full_name',
      key: 'full_name',
      render: (text: string) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean, record: User) => (
        <Switch
          size="small"
          checked={isActive}
          onChange={(checked) => handleStatusChange(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      render: (date: string) => 
        date ? new Date(date).toLocaleString('zh-CN') : '从未登录',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: User) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openEditModal(record)}
              size="small"
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2} style={{ marginBottom: '24px' }}>
        用户管理
      </Title>

      {/* 统计卡片 */}
      {userStats && (
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic title="总用户数" value={userStats.total_users} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="活跃用户" value={userStats.active_users} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="管理员" value={userStats.admin_users} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="普通用户" value={userStats.regular_users} />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        {/* 操作工具栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Input.Search
                placeholder="搜索用户名、邮箱或全名"
                onSearch={handleSearch}
                style={{ width: 300 }}
                allowClear
              />
              <Select
                placeholder="状态"
                value={filterActive}
                onChange={(value) => {
                  setFilterActive(value);
                  setTimeout(handleFilterChange, 0);
                }}
                style={{ width: 120 }}
                allowClear
              >
                <Option value={true}>启用</Option>
                <Option value={false}>禁用</Option>
              </Select>
              <Select
                placeholder="角色"
                value={filterAdmin}
                onChange={(value) => {
                  setFilterAdmin(value);
                  setTimeout(handleFilterChange, 0);
                }}
                style={{ width: 120 }}
                allowClear
              >
                <Option value={true}>管理员</Option>
                <Option value={false}>普通用户</Option>
              </Select>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadData}
                loading={userListLoading}
              >
                刷新
              </Button>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={openCreateModal}
            >
              新建用户
            </Button>
          </Col>
        </Row>

        {/* 用户列表表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={userListLoading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalUsers,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, size) => {
              setCurrentPage(page);
              if (size !== pageSize) {
                setPageSize(size);
              }
              getUsers({
                page,
                size: size || pageSize,
                search: searchText || undefined,
                is_active: filterActive,
                is_admin: filterAdmin,
              });
            },
          }}
        />
      </Card>

      {/* 新建/编辑用户模态框 */}
      <Modal
        title={editingUser ? "编辑用户" : "新建用户"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setEditingUser(null);
          setShowPasswordField(false);
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {!editingUser && (
            <Form.Item
              label="用户名"
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 1, max: 50, message: '用户名长度为1-50个字符' },
              ]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>
          )}

          <Form.Item
            label="邮箱"
            name="email"
            rules={[
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邮箱（可选）" />
          </Form.Item>

          {!editingUser && (
            <Form.Item
              label="密码"
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6个字符' },
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          )}

          <Form.Item
            label="全名"
            name="full_name"
            rules={[
              { max: 100, message: '全名长度不能超过100个字符' },
            ]}
          >
            <Input placeholder="请输入全名（可选）" />
          </Form.Item>

          <Form.Item
            label="角色"
            name="is_admin"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="管理员"
              unCheckedChildren="普通用户"
            />
          </Form.Item>

          {editingUser && (
            <>
              <Form.Item
                label="状态"
                name="is_active"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </Form.Item>
              
              <Form.Item label="修改密码">
                <Button 
                  type="link" 
                  onClick={() => setShowPasswordField(!showPasswordField)}
                  style={{ padding: 0 }}
                >
                  {showPasswordField ? '取消修改密码' : '修改密码'}
                </Button>
              </Form.Item>
              
              {showPasswordField && (
                <Form.Item
                  label="新密码"
                  name="password"
                  rules={[
                    { min: 6, message: '密码至少6个字符' },
                  ]}
                >
                  <Input.Password placeholder="请输入新密码" />
                </Form.Item>
              )}
            </>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingUser ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;