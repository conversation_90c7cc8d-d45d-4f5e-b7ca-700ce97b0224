# KGMS Frontend Nginx Configuration

server {
    listen 80;
    server_name localhost;

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # React应用的根目录
    root /usr/share/nginx/html;
    index index.html;

    # 静态文件缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }

    # API请求代理到后端（生产环境中由主Nginx处理）
    location /api {
        # 这里是占位符，生产环境中由主Nginx配置代理
        return 404;
    }

    # React Router支持 - 所有路由都返回index.html
    location / {
        try_files $uri $uri/ /index.html;
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}