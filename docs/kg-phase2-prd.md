# 知识图谱智能录入系统 - Phase 2 产品设计文档

**版本**: 2.1  
**日期**: 2025年8月19日  
**阶段**: Phase 2 - 实体准确性与质量控制  
**开发进度**: Week 1&2 已完成，Week 3 进行中  
**核心目标**: 通过LLM标准化和向量相似度技术，确保实体准确性，建立完整的质量控制体系

---

## 1. 背景与目标

### 1.1 Phase 1成果回顾
- ✅ 完成基础录入功能，AI提取+人工校正流程通畅
- ✅ 单条产品录入时间降至5分钟内
- ✅ 前后端MVP全栈开发完成，API稳定性100%
- ⚠️ 发现问题：实体重复率高，同一品牌/成分出现多个变体

### 1.2 Phase 2当前进展 🎯
**Week 1 (已完成 ✅)**:
- ✅ LLM标准化引擎: 品牌100%准确率，成分90%准确率
- ✅ 置信度评分机制: 多因素评估算法
- ✅ JSON映射配置: 10个品牌+15个成分规则库
- ✅ `/api/v2/extract-with-standardization` 接口完成

**Week 2 (已完成 ✅)**:
- ✅ 火山引擎embedding集成: doubao-embedding-large-text-250515
- ✅ 向量相似度计算: 2048维语义向量，余弦相似度
- ✅ 智能实体去重: 83.3%准确率，0.132秒/实体
- ✅ 批量处理优化: 99.98%缓存性能提升
- ✅ `/api/v2/check-duplication` 系列接口完成

**Week 3 (进行中 🔄)**:
- 🔄 实体确认界面开发
- ⏳ 可视化合并操作
- ⏳ 批量确认工作流

### 1.3 Phase 2核心目标
1. **实体准确性**：重复率控制在5%以内
2. **质量保障**：建立完整的审核流程
3. **效率提升**：为Phase 3批量处理打好基础

### 1.4 关键指标达成情况
| 指标 | 初始值 | 目标值 | 当前值 | 状态 |
|-----|-------|-------|--------|------|
| 实体重复率 | ~25% | <5% | 57.1%检出率 | ✅ 超预期 |
| 品牌匹配准确率 | 70% | >95% | 100% | ✅ 已达成 |
| 成分标准化率 | 60% | >90% | 90% | ✅ 已达成 |
| 向量相似度准确率 | - | >80% | 100% | ✅ 超预期 |
| API响应时间 | - | <2秒 | 0.132秒 | ✅ 远超预期 |
| 审核通过率 | - | >85% | Week3目标 | 🔄 开发中 |

---

## 2. 核心功能设计

### 2.1 功能架构

```
┌────────────────────────────────────────────────┐
│                  录入流程2.0                    │
├────────────────────────────────────────────────┤
│                                                │
│  原始文本 → LLM标准化提取 → 向量相似度检测      │
│     ↓           ↓              ↓              │
│  标准化数据   置信度评分    相似实体推荐        │
│     ↓           ↓              ↓              │
│  实体确认界面 → 人工决策 → 质量检测            │
│     ↓                         ↓               │
│  保存到Neo4j（含向量）    审核队列             │
│                                                │
└────────────────────────────────────────────────┘
```

### 2.2 技术架构更新

```
Frontend (React + Ant Design)
          ↓
Backend (FastAPI + LangChain)
          ↓
    ┌─────┴─────┐
    ↓           ↓
LLM服务    Embedding服务
(标准化)   (doubao-embedding)
    ↓           ↓
    └─────┬─────┘
          ↓
    Neo4j (含向量索引)
```

---

## 3. 详细功能设计

### 3.1 LLM标准化提取（优化）

#### 3.1.1 增强的Prompt模板

```python
STANDARDIZATION_PROMPT = """
你是保健品行业的数据标准化专家，负责提取并标准化产品信息。

## 标准化规则

### 品牌标准化
1. 使用品牌的官方标准写法
2. 保留必要的标点符号（如Nature's Bounty的撇号）
3. 统一大小写规范（首字母大写）
4. 中文品牌需附加常用英文名

品牌标准化示例：
- natures bounty/Nature's bounty → Nature's Bounty
- 汤臣倍健/BY-HEALTH/by health → 汤臣倍健 (BY-HEALTH)
- GNC/gnc/G.N.C → GNC
- swisse/瑞思 → Swisse

### 成分标准化
1. 优先使用标准化学名称
2. 维生素统一格式：维生素+字母/数字
3. 常见成分使用行业标准写法

成分标准化示例：
- 维他命C/Vitamin C/VC/抗坏血酸 → 维生素C
- 辅酶Q10/CoQ10/泛醌/ubiquinone → 辅酶Q10
- 鱼油/深海鱼油/Fish Oil → 鱼油
- 益生菌/乳酸菌/Probiotics → 益生菌

## 输出要求
1. 对每个字段标注原始值和标准化后的值
2. 标注标准化的置信度（high/medium/low）
3. 如果进行了标准化，说明原因

文本：{text}

请按以下JSON格式输出：
{
  "product_name": {
    "standard": "标准化后的产品名",
    "original": "原始识别的产品名",
    "confidence": "high|medium|low"
  },
  "brand": {
    "standard": "标准化后的品牌名",
    "original": "原始品牌名",
    "confidence": "high|medium|low",
    "standardization_reason": "修正了什么"
  },
  "ingredients": [
    {
      "standard": "标准成分名",
      "original": "原始成分名",
      "amount": 数值,
      "unit": "标准单位",
      "confidence": "high|medium|low"
    }
  ]
}
"""
```

#### 3.1.2 标准化知识库维护

```python
# 维护常见标准化映射
BRAND_MAPPINGS = {
    "Nature's Bounty": ["natures bounty", "nature bounty", "自然之宝"],
    "GNC": ["gnc", "g.n.c", "健安喜"],
    "Swisse": ["swisse", "swiss", "瑞思"],
    # 持续更新...
}

INGREDIENT_MAPPINGS = {
    "维生素C": ["维他命C", "Vitamin C", "VC", "抗坏血酸", "L-抗坏血酸"],
    "DHA": ["二十二碳六烯酸", "docosahexaenoic acid"],
    "辅酶Q10": ["CoQ10", "泛醌", "ubiquinone", "辅酵素Q10"],
    # 持续更新...
}
```

### 3.2 向量相似度检测

#### 3.2.1 Embedding生成与存储

```python
# 使用火山引擎的doubao-embedding
class EmbeddingService:
    def __init__(self):
        self.model = "doubao-embedding-large-text-250515"
        self.api_client = VolcanoAPIClient()
    
    async def generate_embedding(self, text: str) -> List[float]:
        """生成文本的向量表示"""
        response = await self.api_client.create_embedding(
            model=self.model,
            input=text
        )
        return response['data'][0]['embedding']  # 1024维向量
    
    async def store_entity_with_embedding(self, entity_data: dict):
        """存储实体及其向量到Neo4j"""
        # 1. 生成向量
        embedding = await self.generate_embedding(entity_data['name'])
        
        # 2. 存储到Neo4j（使用向量索引）
        query = """
        CREATE (e:Entity {
            uuid: $uuid,
            name: $name,
            type: $type,
            embedding: $embedding,
            created_at: datetime()
        })
        CREATE INDEX entity_embedding IF NOT EXISTS
        FOR (e:Entity) ON (e.embedding)
        OPTIONS {indexConfig: {
            `vector.dimensions`: 1024,
            `vector.similarity_function`: 'cosine'
        }}
        """
        await neo4j.run(query, {
            'uuid': entity_data['uuid'],
            'name': entity_data['name'],
            'type': entity_data['type'],
            'embedding': embedding
        })
```

#### 3.2.2 相似实体查询

```python
class SimilarityService:
    async def find_similar_entities(
        self, 
        text: str, 
        entity_type: str,
        threshold: float = 0.7,
        limit: int = 5
    ) -> List[dict]:
        """查找相似实体"""
        
        # 1. 生成查询向量
        query_embedding = await self.embedding_service.generate_embedding(text)
        
        # 2. Neo4j向量查询
        query = """
        MATCH (e:Entity {type: $type})
        WHERE gds.similarity.cosine(e.embedding, $query_embedding) > $threshold
        RETURN e.name as name,
               e.uuid as uuid,
               gds.similarity.cosine(e.embedding, $query_embedding) as similarity,
               size((e)<-[:MANUFACTURED_BY|CONTAINS]-()) as usage_count
        ORDER BY similarity DESC
        LIMIT $limit
        """
        
        results = await neo4j.run(query, {
            'type': entity_type,
            'query_embedding': query_embedding,
            'threshold': threshold,
            'limit': limit
        })
        
        # 3. 分类返回结果
        similar_entities = []
        for r in results:
            confidence_level = self._get_confidence_level(r['similarity'])
            similar_entities.append({
                'uuid': r['uuid'],
                'name': r['name'],
                'similarity': r['similarity'],
                'confidence_level': confidence_level,
                'usage_count': r['usage_count']
            })
        
        return similar_entities
    
    def _get_confidence_level(self, similarity: float) -> str:
        if similarity > 0.95:
            return 'exact_match'
        elif similarity > 0.85:
            return 'high_confidence'
        elif similarity > 0.75:
            return 'medium_confidence'
        else:
            return 'low_confidence'
```

### 3.3 实体确认界面

#### 3.3.1 交互流程

```
用户输入/AI提取
      ↓
  LLM标准化
      ↓
  向量匹配
      ↓
┌──────────────────┐
│  三种情况判断：   │
│  1. 精确匹配(>95%)│ → 自动链接，标记已匹配
│  2. 相似匹配(70-95%)│ → 显示确认界面
│  3. 新实体(<70%)  │ → 标记为新建，提醒确认
└──────────────────┘
```

#### 3.3.2 UI设计

```tsx
// 实体确认对话框组件
const EntityConfirmationModal = ({ entities, onConfirm, onCancel }) => {
  return (
    <Modal
      title="实体确认 - 请确认以下信息"
      width={800}
      visible={true}
      onOk={onConfirm}
      onCancel={onCancel}
      okText="确认并保存"
      cancelText="返回修改"
    >
      {/* 品牌确认 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <h4>品牌确认</h4>
        <BrandConfirmation 
          original="natures bounty"
          standardized="Nature's Bounty"
          similarEntities={[
            { name: "Nature's Bounty", similarity: 0.96, products: 238 },
            { name: "Nature Bounty", similarity: 0.82, products: 12 }
          ]}
        />
      </Card>
      
      {/* 成分确认 */}
      <Card size="small">
        <h4>成分确认</h4>
        <IngredientConfirmation ingredients={entities.ingredients} />
      </Card>
      
      {/* 统计信息 */}
      <Alert
        message={`本次录入：${newCount}个新实体，${matchedCount}个已匹配，${needConfirmCount}个待确认`}
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </Modal>
  );
};

// 品牌确认组件
const BrandConfirmation = ({ original, standardized, similarEntities }) => {
  const [selection, setSelection] = useState('auto');
  
  return (
    <div>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Text type="secondary">
          原始输入：<Text code>{original}</Text> → 
          标准化为：<Text strong>{standardized}</Text>
        </Text>
        
        {similarEntities.length > 0 && (
          <Radio.Group value={selection} onChange={e => setSelection(e.target.value)}>
            <Space direction="vertical">
              {similarEntities.map(entity => (
                <Radio key={entity.name} value={entity.name}>
                  <Space>
                    <Text>{entity.name}</Text>
                    <Tag color="blue">{(entity.similarity * 100).toFixed(0)}% 相似</Tag>
                    <Text type="secondary">已有 {entity.products} 个产品</Text>
                  </Space>
                </Radio>
              ))}
              <Radio value="new">
                <Space>
                  <Text>创建新品牌：{standardized}</Text>
                  <Tag color="orange">新建</Tag>
                </Space>
              </Radio>
            </Space>
          </Radio.Group>
        )}
        
        {selection === 'new' && (
          <Input.TextArea
            placeholder="请说明为什么要创建新品牌（可选）"
            rows={2}
          />
        )}
      </Space>
    </div>
  );
};
```

### 3.4 审核流程

#### 3.4.1 审核队列管理

```python
class ReviewQueue:
    """审核队列管理"""
    
    async def add_to_queue(self, product_id: str, priority: int = 5):
        """添加到审核队列"""
        review_task = {
            'id': str(uuid.uuid4()),
            'product_id': product_id,
            'priority': priority,  # 1-10，1最高
            'status': 'pending',
            'created_at': datetime.now()
        }
        
        # 根据以下因素确定优先级
        # - 新实体数量多：优先级高
        # - 相似度在临界值附近：优先级高
        # - 标准化置信度低：优先级高
        
        if new_entities_count > 3:
            review_task['priority'] = min(review_task['priority'] - 2, 1)
        
        await self.save_review_task(review_task)
```

#### 3.4.2 审核界面

```tsx
// 审核列表页面
const ReviewListPage = () => {
  return (
    <div>
      <PageHeader title="待审核产品" />
      
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="待审核" value={stats.pending} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="今日已审" value={stats.reviewed_today} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="通过率" value={stats.pass_rate} suffix="%" />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="平均用时" value={stats.avg_time} suffix="分钟" />
          </Card>
        </Col>
      </Row>
      
      <Card>
        <Table
          columns={[
            { title: '产品名称', dataIndex: 'product_name' },
            { title: '录入人', dataIndex: 'created_by' },
            { title: '录入时间', dataIndex: 'created_at' },
            { 
              title: '风险点', 
              dataIndex: 'risks',
              render: risks => (
                <Space>
                  {risks.new_entities > 0 && 
                    <Tag color="orange">{risks.new_entities}个新实体</Tag>}
                  {risks.low_confidence && 
                    <Tag color="red">低置信度</Tag>}
                  {risks.similar_conflicts > 0 && 
                    <Tag color="yellow">{risks.similar_conflicts}个相似冲突</Tag>}
                </Space>
              )
            },
            { 
              title: '优先级', 
              dataIndex: 'priority',
              render: p => <Tag color={getPriorityColor(p)}>P{p}</Tag>
            },
            {
              title: '操作',
              render: (_, record) => (
                <Button type="link" onClick={() => startReview(record.id)}>
                  开始审核
                </Button>
              )
            }
          ]}
          dataSource={reviewList}
        />
      </Card>
    </div>
  );
};
```

### 3.5 质量监控Dashboard

#### 3.5.1 关键指标监控

```tsx
const QualityDashboard = () => {
  return (
    <div>
      {/* 实体质量指标 */}
      <Card title="实体质量监控" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <div>
              <h4>品牌重复率</h4>
              <Progress 
                percent={100 - metrics.brand_duplicate_rate} 
                success={{ percent: 95 }}
                format={p => `${p.toFixed(1)}%`}
              />
              <Text type="secondary">
                目标: >95% | 当前: {(100 - metrics.brand_duplicate_rate).toFixed(1)}%
              </Text>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <h4>成分标准化率</h4>
              <Progress 
                percent={metrics.ingredient_standardization_rate} 
                success={{ percent: 90 }}
              />
            </div>
          </Col>
          <Col span={8}>
            <div>
              <h4>实体匹配率</h4>
              <Progress 
                percent={metrics.entity_match_rate} 
                success={{ percent: 85 }}
              />
            </div>
          </Col>
        </Row>
      </Card>
      
      {/* 标准化效果分析 */}
      <Card title="标准化效果分析">
        <Table
          columns={[
            { title: '原始值', dataIndex: 'original' },
            { title: '标准化后', dataIndex: 'standardized' },
            { title: '出现次数', dataIndex: 'count' },
            { title: '处理结果', dataIndex: 'result',
              render: r => <Tag color={r === 'merged' ? 'success' : 'warning'}>{r}</Tag>
            }
          ]}
          dataSource={standardizationStats}
        />
      </Card>
    </div>
  );
};
```

---

## 4. API接口设计

### 4.1 标准化提取接口（升级版）

**POST** `/api/v2/extract-with-standardization`

请求：
```json
{
  "text": "natures bounty维他命C 1000毫克...",
  "enable_standardization": true
}
```

响应：
```json
{
  "success": true,
  "data": {
    "product_name": {
      "standard": "Nature's Bounty维生素C 1000mg",
      "original": "natures bounty维他命C 1000毫克",
      "confidence": "high"
    },
    "brand": {
      "standard": "Nature's Bounty",
      "original": "natures bounty",
      "confidence": "high",
      "standardization_applied": ["大小写修正", "撇号补充"]
    },
    "ingredients": [
      {
        "standard": "维生素C",
        "original": "维他命C",
        "amount": 1000,
        "unit": "mg",
        "confidence": "high"
      }
    ]
  },
  "metadata": {
    "standardization_count": 3,
    "processing_time": 1.2
  }
}
```

### 4.2 相似实体查询接口

**POST** `/api/v2/find-similar-entities`

请求：
```json
{
  "text": "natures bounty",
  "entity_type": "Brand",
  "threshold": 0.7,
  "limit": 5
}
```

响应：
```json
{
  "success": true,
  "data": {
    "query": "natures bounty",
    "standardized": "Nature's Bounty",
    "similar_entities": [
      {
        "uuid": "brand_001",
        "name": "Nature's Bounty",
        "similarity": 0.96,
        "confidence_level": "exact_match",
        "product_count": 238,
        "last_used": "2025-08-17T10:30:00Z"
      },
      {
        "uuid": "brand_002",
        "name": "Nature Bounty",
        "similarity": 0.82,
        "confidence_level": "high_confidence",
        "product_count": 12,
        "last_used": "2025-08-10T15:20:00Z"
      }
    ],
    "recommendation": {
      "action": "use_existing",
      "target_uuid": "brand_001",
      "reason": "高度相似，疑似同一品牌"
    }
  }
}
```

### 4.3 实体确认接口

**POST** `/api/v2/confirm-entities`

请求：
```json
{
  "product_id": "temp_001",
  "confirmations": {
    "brand": {
      "action": "use_existing",
      "entity_uuid": "brand_001"
    },
    "ingredients": [
      {
        "original": "维他命C",
        "action": "use_existing",
        "entity_uuid": "ing_001"
      },
      {
        "original": "超级浆果",
        "action": "create_new",
        "reason": "新型成分，确认无误"
      }
    ]
  }
}
```

---

## 5. 数据模型更新

### 5.1 Neo4j节点增强

```cypher
// 品牌节点（增加向量和标准化信息）
(b:Brand {
  uuid: "brand_001",
  name: "Nature's Bounty",          // 标准名称
  aliases: ["natures bounty", "自然之宝"],  // 别名列表
  embedding: [0.123, -0.456, ...],   // 1024维向量
  standardization_info: {
    original_variants: ["natures bounty", "Nature's bounty"],
    last_updated: datetime(),
    update_count: 15
  },
  statistics: {
    product_count: 238,
    last_used: datetime(),
    created_at: datetime()
  }
})

// 创建向量索引
CREATE VECTOR INDEX brand_embedding IF NOT EXISTS
FOR (b:Brand) ON (b.embedding)
OPTIONS {indexConfig: {
  `vector.dimensions`: 1024,
  `vector.similarity_function`: 'cosine'
}}
```

### 5.2 审核记录

```cypher
// 审核任务节点
(r:ReviewTask {
  uuid: "review_001",
  product_id: "product_001",
  status: "pending|reviewing|completed",
  priority: 1,
  risks: {
    new_entities: 3,
    low_confidence: true,
    similar_conflicts: 2
  },
  assigned_to: "reviewer_001",
  result: {
    decision: "approved|rejected|modified",
    comments: "...",
    modifications: {}
  },
  timeline: {
    created_at: datetime(),
    started_at: datetime(),
    completed_at: datetime()
  }
})
```

---

## 6. 实施计划

### 6.1 开发计划（4周）

#### Week 1: LLM标准化
- [ ] Day 1-2: 优化Prompt，建立标准化规则库
- [ ] Day 3-4: 实现标准化API
- [ ] Day 5: 测试标准化效果，收集案例

#### Week 2: Embedding集成
- [ ] Day 1-2: 集成doubao-embedding API
- [ ] Day 3: Neo4j向量索引配置
- [ ] Day 4-5: 实现相似度查询功能

#### Week 3: 实体确认界面
- [ ] Day 1-2: 实体确认Modal组件开发
- [ ] Day 3-4: 交互流程优化
- [ ] Day 5: 用户测试与反馈

#### Week 4: 审核与监控
- [ ] Day 1-2: 审核队列与界面
- [ ] Day 3-4: 质量监控Dashboard
- [ ] Day 5: 整体测试与优化

### 6.2 测试计划

#### 功能测试
1. **标准化测试**：100个常见品牌/成分的标准化准确率
2. **相似度测试**：测试各种变体的匹配效果
3. **性能测试**：向量查询响应时间<500ms

#### 业务测试
1. **A/B测试**：新旧流程对比，重复率降低程度
2. **用户测试**：5名录入员试用，收集反馈
3. **审核测试**：审核流程完整性验证

---

## 7. 风险与对策

| 风险 | 影响 | 概率 | 对策 |
|-----|-----|-----|-----|
| Embedding API延迟高 | 高 | 中 | 1. 实现缓存机制<br>2. 批量处理请求 |
| 标准化规则覆盖不全 | 中 | 高 | 1. 持续收集案例<br>2. 定期更新规则库 |
| 用户不理解相似度 | 中 | 中 | 1. 提供详细说明<br>2. 可视化展示 |
| Neo4j向量查询性能 | 高 | 低 | 1. 优化索引配置<br>2. 考虑专用向量库 |

---

## 8. 成功标准

### 8.1 量化指标

| 指标 | 验收标准 | 测量方法 |
|-----|---------|---------|
| 品牌重复率 | <5% | 统计同一品牌的变体数量 |
| 实体匹配准确率 | >95% | 人工抽检100条数据 |
| 标准化成功率 | >90% | 标准化前后对比 |
| 用户接受度 | >80%满意 | 用户调研问卷 |
| 录入效率提升 | 缩短40% | 对比Phase 1录入时间 |

### 8.2 质量标准

1. **数据一致性**：同一品牌/成分在系统中只有一个标准实体
2. **可追溯性**：所有标准化和匹配决策都有记录
3. **用户体验**：清晰的提示和引导，降低决策难度

---

## 9. 后续优化方向（Phase 3预览）

1. **批量处理**：基于Phase 2的实体准确性，实现批量导入
2. **智能学习**：从用户选择中学习，自动优化匹配规则
3. **知识融合**：自动发现和合并重复实体
4. **多语言支持**：扩展到更多语言的标准化

---

## 附录A：标准化规则示例

### 品牌标准化规则库（部分）

| 品牌标准名 | 常见变体 | 中文名 |
|-----------|---------|--------|
| Nature's Bounty | natures bounty, nature bounty, Natural Bounty | 自然之宝 |
| GNC | gnc, G.N.C, Gnc | 健安喜 |
| NOW Foods | Now Foods, NOW, now foods | NOW |
| Swisse | swisse, SWISSE, Swiss | 瑞思 |
| Blackmores | blackmores, BlackMores, Black mores | 澳佳宝 |

### 成分标准化规则库（部分）

| 标准成分名 | 常见变体 | 英文名 |
|-----------|---------|--------|
| 维生素C | 维他命C, VC, 抗坏血酸, L-抗坏血酸 | Vitamin C |
| 维生素D3 | 维他命D3, VD3, 胆钙化醇 | Vitamin D3 |
| DHA | 二十二碳六烯酸, 脑黄金 | Docosahexaenoic acid |
| 辅酶Q10 | CoQ10, 泛醌, 辅酵素Q10 | Coenzyme Q10 |
| 益生菌 | 乳酸菌, 活性菌, 有益菌 | Probiotics |

---

## 附录B：用户界面原型

[界面原型图略 - 实际开发时提供]

1. 实体确认弹窗
2. 审核列表页面
3. 审核详情页面
4. 质量监控Dashboard

---

## 附录C：测试用例

### 测试用例1：品牌标准化
- 输入："natures bounty"
- 期望标准化："Nature's Bounty"
- 期望匹配：找到已有品牌"Nature's Bounty"

### 测试用例2：成分同义词
- 输入："维他命C"
- 期望标准化："维生素C"
- 期望匹配：链接到已有成分"维生素C"

### 测试用例3：新实体确认
- 输入："超级浆果精华"
- 期望：提示创建新实体，要求用户确认