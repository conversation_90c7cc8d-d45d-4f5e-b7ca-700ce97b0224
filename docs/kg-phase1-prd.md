# 知识图谱智能录入系统 - Phase 1 产品设计文档

**版本**: 1.0  
**日期**: 2025年8月17日  
**阶段**: Phase 1 - MVP核心功能  
**预计开发周期**: 4周  

---

## 1. 产品概述

### 1.1 阶段目标
在第一阶段，我们将构建一个最小可行产品(MVP)，实现知识图谱数据录入的核心工作流。目标是验证"表单为主，AI为辅"的产品理念，并确保基础录入流程可用。

### 1.2 核心指标
- **功能完成度**: 100%核心功能可用
- **AI提取准确率**: ≥70%（可通过人工校正达到100%）
- **单个产品录入时间**: ≤5分钟
- **系统稳定性**: 99%可用性

### 1.3 用户角色
Phase 1仅支持一种角色：**数据录入员**

---

## 2. 用户故事与验收标准

### Story 1: AI辅助信息提取
**作为**数据录入员  
**我想要**将产品描述文本粘贴后自动提取结构化信息  
**以便**减少手动输入的工作量  

**验收标准**：
- ✓ 可以粘贴任意长度的产品描述文本（最长10000字符）
- ✓ 点击按钮后3秒内返回提取结果
- ✓ 提取准确率达到70%以上
- ✓ 支持中英文混合文本

### Story 2: 表单编辑与校正
**作为**数据录入员  
**我想要**编辑和修正AI提取的信息  
**以便**确保数据的准确性  

**验收标准**：
- ✓ 所有字段都可以手动编辑
- ✓ 必填字段有明确标识
- ✓ 输入时有实时格式验证
- ✓ 可以添加多个成分项

### Story 3: 实体链接
**作为**数据录入员  
**我想要**在输入品牌和成分时看到已存在的实体  
**以便**保持数据的一致性  

**验收标准**：
- ✓ 输入2个字符后显示匹配建议
- ✓ 显示前5个最相关的结果
- ✓ 可以选择现有实体或创建新实体
- ✓ 响应时间<500ms

### Story 4: 数据保存
**作为**数据录入员  
**我想要**保存录入的产品信息  
**以便**构建知识图谱  

**验收标准**：
- ✓ 点击保存后2秒内完成
- ✓ 保存成功有明确提示
- ✓ 保存失败有错误提示和重试选项
- ✓ 支持"保存并新建"快捷操作

---

## 3. 功能详细设计

### 3.1 页面布局

```
+----------------------------------------------------------+
|                    产品智能录入系统                        |
+----------------------------------------------------------+
|                                                          |
|  [AI提取区]                                              |
|  +--------------------------------------------------+    |
|  | 粘贴产品原始资料:                                  |    |
|  | +----------------------------------------------+ |    |
|  | |                                              | |    |
|  | |  (多行文本输入框，高度200px)                   | |    |
|  | |                                              | |    |
|  | +----------------------------------------------+ |    |
|  |                                                  |    |
|  |        [ 清空文本 ]  [ AI 自动填充表单 ]           |    |
|  +--------------------------------------------------+    |
|                                                          |
|  [表单编辑区]                                            |
|  +--------------------------------------------------+    |
|  | 基础信息                                          |    |
|  | 产品名称*: [___________________]                 |    |
|  | 品牌*:     [___________________] ▼               |    |
|  | 产品类型:  [保健品        ] ▼                     |    |
|  | SKU:      [___________________]                  |    |
|  |                                                  |    |
|  | 成分信息                                          |    |
|  | +----------------------------------------------+ |    |
|  | | 成分名称        含量      单位     操作       | |    |
|  | | [_________]    [____]   [____]   [删除]     | |    |
|  | | [_________]    [____]   [____]   [删除]     | |    |
|  | +----------------------------------------------+ |    |
|  | [+ 添加成分]                                      |    |
|  |                                                  |    |
|  | 功效描述                                          |    |
|  | +----------------------------------------------+ |    |
|  | |                                              | |    |
|  | |  (多行文本，高度100px)                        | |    |
|  | +----------------------------------------------+ |    |
|  +--------------------------------------------------+    |
|                                                          |
|  [操作区]                                                |
|  [ 清空表单 ]  [ 保存 ]  [ 保存并新建 ]                   |
|                                                          |
+----------------------------------------------------------+
```

### 3.2 交互流程设计

#### 3.2.1 AI提取流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant L as LLM API
    
    U->>F: 粘贴文本
    U->>F: 点击"AI自动填充"
    F->>F: 显示加载状态
    F->>B: POST /api/extract
    B->>L: 调用提取API
    L-->>B: 返回JSON结果
    B->>B: 数据格式化
    B-->>F: 返回结构化数据
    F->>F: 自动填充表单
    F->>U: 显示填充结果
```

#### 3.2.2 实体链接流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant DB as Neo4j
    
    U->>F: 输入品牌/成分(≥2字符)
    F->>F: 防抖300ms
    F->>B: GET /api/entities/search
    B->>DB: 查询匹配实体
    DB-->>B: 返回结果
    B-->>F: 返回Top 5匹配
    F->>U: 显示下拉建议
    U->>F: 选择或继续输入
```

### 3.3 字段定义

| 字段名 | 类型 | 必填 | 验证规则 | 说明 |
|-------|------|------|----------|------|
| product_name | string | 是 | 2-100字符 | 产品名称 |
| brand | string | 是 | 2-50字符 | 品牌名，支持实体链接 |
| product_type | enum | 是 | 枚举值 | 保健品/药品/食品 |
| sku | string | 否 | 字母数字 | 产品SKU编码 |
| ingredients | array | 是 | 至少1项 | 成分列表 |
| ├─ name | string | 是 | 2-50字符 | 成分名称，支持实体链接 |
| ├─ amount | number | 否 | 正数 | 含量数值 |
| └─ unit | string | 否 | 2-10字符 | 单位（mg/g/ml等） |
| benefits | text | 否 | 最多500字 | 功效描述 |

### 3.4 状态管理

#### 3.4.1 页面状态
```javascript
{
  // AI提取区
  extraction: {
    rawText: "",          // 原始文本
    isLoading: false,     // 加载状态
    error: null          // 错误信息
  },
  
  // 表单数据
  formData: {
    product_name: "",
    brand: "",
    product_type: "保健品",
    sku: "",
    ingredients: [
      { name: "", amount: "", unit: "" }
    ],
    benefits: ""
  },
  
  // 实体链接
  entitySuggestions: {
    brand: [],           // 品牌建议列表
    ingredients: {}      // 成分建议 {index: [...]}
  },
  
  // 表单状态
  form: {
    isDirty: false,      // 是否有未保存修改
    isSubmitting: false, // 提交中
    errors: {}          // 字段错误
  }
}
```

---

## 4. API接口设计

### 4.1 AI信息提取接口

**POST** `/api/v1/extract`

请求：
```json
{
  "text": "Nature's Bounty维生素C 1000mg，每片含1000毫克维生素C..."
}
```

响应：
```json
{
  "success": true,
  "data": {
    "product_name": "Nature's Bounty维生素C 1000mg",
    "brand": "Nature's Bounty",
    "product_type": "保健品",
    "ingredients": [
      {
        "name": "维生素C",
        "amount": 1000,
        "unit": "mg"
      }
    ],
    "benefits": "增强免疫力，抗氧化"
  },
  "confidence": 0.85
}
```

### 4.2 实体搜索接口

**GET** `/api/v1/entities/search`

请求参数：
- `type`: 实体类型 (Brand/Ingredient)
- `keyword`: 搜索关键词
- `limit`: 返回数量（默认5）

响应：
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid-1",
      "name": "Nature's Bounty",
      "type": "Brand",
      "match_score": 0.95
    }
  ]
}
```

### 4.3 产品保存接口

**POST** `/api/v1/products`

请求：
```json
{
  "product_name": "Nature's Bounty维生素C 1000mg",
  "brand": "Nature's Bounty",
  "brand_id": "uuid-1",  // 如果链接到已存在品牌
  "product_type": "保健品",
  "sku": "NB-VC-1000",
  "ingredients": [
    {
      "name": "维生素C",
      "ingredient_id": "uuid-2",  // 如果链接到已存在成分
      "amount": 1000,
      "unit": "mg"
    }
  ],
  "benefits": "增强免疫力，抗氧化"
}
```

响应：
```json
{
  "success": true,
  "data": {
    "product_id": "uuid-new-product",
    "message": "产品保存成功"
  }
}
```

---

## 5. LLM Prompt设计

### 5.1 系统Prompt

```
你是一个保健品行业的数据分析专家，专门负责从非结构化文本中提取产品信息。

任务要求：
1. 仔细分析提供的产品描述文本
2. 提取所有相关的产品信息
3. 严格按照指定的JSON格式返回
4. 如果某个字段无法确定，使用null值
5. 金额数字只保留数值部分，单位单独提取

输出格式：
{
  "product_name": "产品完整名称",
  "brand": "品牌名称",
  "product_type": "保健品|药品|食品",
  "sku": "产品编码",
  "ingredients": [
    {
      "name": "成分名称",
      "amount": 数值,
      "unit": "单位"
    }
  ],
  "benefits": "功效描述"
}

示例输入：
"GNC葡萄籽精华胶囊，每粒含100mg葡萄籽提取物，50mg维生素C。具有抗氧化、美容养颜的功效。产品编号：GNC-GS-100。"

示例输出：
{
  "product_name": "GNC葡萄籽精华胶囊",
  "brand": "GNC",
  "product_type": "保健品",
  "sku": "GNC-GS-100",
  "ingredients": [
    {"name": "葡萄籽提取物", "amount": 100, "unit": "mg"},
    {"name": "维生素C", "amount": 50, "unit": "mg"}
  ],
  "benefits": "抗氧化、美容养颜"
}
```

---

## 6. 数据模型设计

### 6.1 Neo4j节点设计

```cypher
// 产品节点
(p:Product {
  uuid: "uuid",
  name: "产品名称",
  sku: "SKU编码",
  product_type: "保健品",
  benefits: "功效描述",
  created_at: datetime(),
  updated_at: datetime()
})

// 品牌节点
(b:Brand {
  uuid: "uuid",
  name: "品牌名称",
  created_at: datetime()
})

// 成分节点
(i:Ingredient {
  uuid: "uuid",
  name: "成分名称",
  created_at: datetime()
})

// 关系：产品-品牌
(p:Product)-[:MANUFACTURED_BY]->(b:Brand)

// 关系：产品-成分
(p:Product)-[:CONTAINS {
  amount: 1000,
  unit: "mg"
}]->(i:Ingredient)
```

### 6.2 Cypher操作示例

```cypher
// 保存产品的事务操作
MERGE (b:Brand {name: $brand_name})
CREATE (p:Product {
  uuid: $product_uuid,
  name: $product_name,
  sku: $sku,
  product_type: $product_type,
  benefits: $benefits,
  created_at: datetime()
})
MERGE (p)-[:MANUFACTURED_BY]->(b)

// 批量处理成分
UNWIND $ingredients as ingredient
MERGE (i:Ingredient {name: ingredient.name})
CREATE (p)-[:CONTAINS {
  amount: ingredient.amount,
  unit: ingredient.unit
}]->(i)
```

---

## 7. 错误处理与提示

### 7.1 错误类型与处理

| 错误类型 | 用户提示 | 处理方式 |
|---------|---------|---------|
| AI提取失败 | "AI提取失败，请检查文本格式或手动填写" | 保持表单空白，允许手动输入 |
| 网络超时 | "网络连接超时，请重试" | 显示重试按钮 |
| 必填字段为空 | "请填写产品名称" | 标红空字段，阻止提交 |
| 保存失败 | "保存失败：[具体原因]" | 保持表单数据，显示重试 |
| 重复产品 | "该产品可能已存在，是否继续？" | 显示确认对话框 |

### 7.2 加载状态设计

- **AI提取中**: 按钮显示"提取中..."，禁用点击，显示进度动画
- **实体搜索中**: 下拉框显示"搜索中..."
- **保存中**: 按钮显示"保存中..."，禁用所有操作

---

## 8. 性能要求

### 8.1 响应时间
- AI提取：≤3秒
- 实体搜索：≤500ms
- 表单保存：≤2秒
- 页面加载：≤1秒

### 8.2 并发处理
- 支持10个用户同时操作
- 单用户可同时打开3个标签页

### 8.3 数据限制
- 原始文本：最大10,000字符
- 产品名称：最大100字符
- 成分数量：最多50个
- 功效描述：最大500字符

---

## 9. 测试用例

### 9.1 功能测试

**测试用例1：AI提取基础功能**
1. 输入包含所有字段的标准文本
2. 点击"AI自动填充"
3. 验证所有字段正确填充
4. 预期：准确率>70%

**测试用例2：实体链接**
1. 在品牌字段输入"Na"
2. 等待建议列表出现
3. 选择"Nature's Bounty"
4. 预期：字段自动完成，显示已链接状态

**测试用例3：数据保存**
1. 填写完整表单
2. 点击"保存"
3. 预期：2秒内保存成功，显示成功提示

### 9.2 异常测试

**测试用例4：空文本提取**
1. 不输入任何文本
2. 点击"AI自动填充"
3. 预期：提示"请输入产品描述文本"

**测试用例5：网络断开**
1. 断开网络
2. 执行任何操作
3. 预期：显示"网络连接失败"提示

---

## 10. 部署与发布

### 10.1 环境要求
- **前端**: Node.js 18+, Vue 3.x / React 18.x
- **后端**: Python 3.9+, FastAPI
- **数据库**: Neo4j 4.4+
- **LLM**: Gemini API / GPT-4 API

### 10.2 部署清单
- [ ] 环境变量配置（API密钥等）
- [ ] 数据库初始化脚本
- [ ] 基础实体数据导入
- [ ] 用户培训材料准备
- [ ] 监控告警配置

### 10.3 发布计划
- **Week 1-2**: 后端开发与API联调
- **Week 2-3**: 前端开发与集成
- **Week 3-4**: 测试与修复
- **Week 4**: 部署与培训

---

## 11. 验收标准

### 11.1 功能验收
- [ ] 所有User Story验收标准通过
- [ ] AI提取准确率≥70%
- [ ] 实体链接功能正常
- [ ] 数据保存到Neo4j成功

### 11.2 性能验收
- [ ] 响应时间满足要求
- [ ] 并发测试通过
- [ ] 4小时稳定性测试无崩溃

### 11.3 可用性验收
- [ ] 5名录入员试用反馈良好
- [ ] 平均单个产品录入时间≤5分钟
- [ ] 错误提示清晰明确

---

## 12. 风险与对策

| 风险 | 概率 | 影响 | 对策 |
|-----|-----|-----|-----|
| LLM API限流 | 中 | 高 | 实现请求队列和缓存机制 |
| AI提取准确率低 | 中 | 中 | 准备更多样本优化Prompt |
| 用户学习成本高 | 低 | 中 | 制作视频教程和操作手册 |
| Neo4j性能瓶颈 | 低 | 高 | 优化查询语句，建立索引 |

---

## 附录A：UI原型图

[此处应插入实际的UI设计图]

## 附录B：测试数据样例

```json
{
  "test_case_1": {
    "input": "Swisse钙片+维生素D3，每片含钙600mg，维生素D3 500IU，促进钙吸收，强健骨骼。",
    "expected_output": {
      "product_name": "Swisse钙片+维生素D3",
      "brand": "Swisse",
      "ingredients": [
        {"name": "钙", "amount": 600, "unit": "mg"},
        {"name": "维生素D3", "amount": 500, "unit": "IU"}
      ]
    }
  }
}
```