# KGMS 用户认证与管理模块 PRD

## 1. 项目背景与目标

### 1.1 项目背景
- KGMS 知识图谱管理系统已完成核心业务功能开发
- 业务部门需要提前学习和使用该后台系统
- 作为内部管理系统，需要用户认证和基础用户管理功能
- 当前系统无认证机制，所有功能完全开放

### 1.2 项目目标
- **安全性**：添加用户认证机制，保护系统资源
- **可管理性**：管理员能够管理用户账号和状态
- **可追踪性**：记录用户登录行为，便于审计
- **易用性**：简洁的登录流程，不影响业务操作体验

## 2. 产品功能设计

### 2.1 功能架构图
```
KGMS 用户认证系统
├── 用户认证模块
│   ├── 用户登录
│   ├── 用户登出
│   └── 登录状态验证
├── 用户管理模块（仅管理员）
│   ├── 用户列表查看
│   ├── 创建新用户
│   ├── 启用/禁用用户
│   └── 用户信息编辑
└── 系统集成
    ├── 路由权限控制
    ├── 界面权限控制
    └── API权限保护
```

### 2.2 用户角色定义

#### 2.2.1 管理员（Admin）
- **权限范围**：系统所有功能 + 用户管理
- **业务功能**：产品管理、品牌管理、成分管理
- **管理功能**：用户管理、系统配置
- **创建方式**：系统初始化时创建默认管理员，后续由现有管理员创建

#### 2.2.2 普通用户（User）
- **权限范围**：系统业务功能
- **业务功能**：产品管理、品牌管理、成分管理
- **限制**：无法访问用户管理功能
- **创建方式**：仅由管理员在后台创建

### 2.3 核心功能模块

#### 2.3.1 用户登录功能
**功能描述**：用户通过用户名/密码进行身份验证

**界面设计**：
- 简洁的登录表单（用户名、密码、登录按钮）
- 登录状态提示（成功/失败/加载中）
- 错误信息展示
- 记住登录状态选项

**业务规则**：
- 仅支持用户名登录方式
- 密码错误次数限制（预留，本版本不实现）
- 登录成功后记录最后登录时间
- 禁用用户无法登录

#### 2.3.2 用户管理功能
**功能描述**：管理员对系统用户进行增删改查操作

**界面设计**：
- 用户列表表格（用户名、邮箱、全名、角色、状态、最后登录时间、操作）
- 创建用户弹窗表单
- 用户状态切换（启用/禁用）
- 搜索和筛选功能

**业务规则**：
- 仅管理员可访问用户管理页面
- 管理员不能禁用自己的账号
- 新创建用户默认为普通用户
- 用户名不能重复，邮箱如果填写则不能重复

#### 2.3.3 系统集成功能
**功能描述**：将认证机制集成到现有系统中

**界面变更**：
- 添加登录页面作为系统入口
- 主界面添加用户信息显示和登出功能
- 管理员菜单中添加"用户管理"选项
- 未登录用户重定向到登录页

## 3. 用户体验设计

### 3.1 用户登录流程
```
用户访问系统 → 检查登录状态 → 未登录重定向到登录页 → 输入凭证 → 验证成功 → 进入系统主页
                                                ↓
                                              验证失败 → 显示错误信息
```

### 3.2 用户管理流程
```
管理员登录 → 访问用户管理 → 查看用户列表 → 创建/编辑用户 → 保存成功 → 刷新列表
```

### 3.3 权限控制体验
- **渐进式权限**：非管理员用户看不到用户管理入口，避免困惑
- **友好提示**：权限不足时给出明确的提示信息
- **无感知保护**：正常业务操作不受认证影响

## 4. 数据模型设计

### 4.1 用户表（User）
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Integer | 是 | 主键，自增ID |
| username | String(50) | 是 | 用户名，唯一 |
| email | String(100) | 否 | 邮箱，唯一（如果填写） |
| hashed_password | String(255) | 是 | 加密后的密码 |
| full_name | String(100) | 否 | 用户全名 |
| is_active | Boolean | 是 | 是否启用，默认true |
| is_admin | Boolean | 是 | 是否管理员，默认false |
| created_at | DateTime | 是 | 创建时间 |
| updated_at | DateTime | 是 | 更新时间 |
| last_login_at | DateTime | 否 | 最后登录时间 |

### 4.2 数据约束
- `username` 必须唯一
- `email` 如果填写则必须唯一，可以为空
- `hashed_password` 使用 bcrypt 加密
- 软删除机制（通过 `is_active` 控制）
- 至少保留一个管理员账号

## 5. 业务数据流

### 5.1 用户登录数据流
```
1. 前端提交登录请求 {username, password}
2. 后端验证用户凭证
3. 验证通过：生成JWT token，更新last_login_at
4. 返回 {token, user_info, expires_in}
5. 前端存储token，更新用户状态
6. 后续请求在Header中携带token
```

### 5.2 用户管理数据流
```
管理员操作 → 权限验证 → 数据库操作 → 返回结果 → 前端更新
```

### 5.3 权限验证数据流
```
请求到达 → 提取JWT token → 验证token有效性 → 解析用户信息 → 检查权限 → 允许/拒绝访问
```

## 6. 架构调整

### 6.1 后端架构调整

#### 6.1.1 新增依赖
- `sqlalchemy`: ORM框架，处理用户数据
- `alembic`: 数据库迁移工具
- 已有`python-jose`和`passlib`满足JWT和密码加密需求

#### 6.1.2 数据库架构
- **双数据库模式**：
  - SQLite：存储用户认证数据（用户、角色、权限）
  - Neo4j：继续存储业务数据（产品、品牌、成分）
- **数据隔离**：认证数据和业务数据完全分离
- **连接管理**：两个独立的数据库连接池

#### 6.1.3 服务架构调整
```
FastAPI 应用
├── 认证中间件层
│   ├── JWT验证中间件
│   └── 权限检查中间件
├── 业务API层（现有）
│   ├── 产品管理API
│   ├── 品牌管理API
│   └── 成分管理API
├── 认证API层（新增）
│   ├── 用户认证API
│   └── 用户管理API
└── 数据访问层
    ├── SQLite服务（新增）
    └── Neo4j服务（现有）
```

### 6.2 前端架构调整

#### 6.2.1 路由架构
```
应用路由
├── 公共路由
│   └── /login (登录页)
├── 受保护路由
│   ├── / (概览页)
│   ├── /smart-entry (智能录入)
│   ├── /traditional-entry (传统录入)
│   ├── /products (产品管理)
│   ├── /brands (品牌管理)
│   ├── /ingredients (成分管理)
│   └── /admin/* (管理员路由)
└── 管理员专用路由
    └── /admin/users (用户管理)
```

#### 6.2.2 状态管理扩展
```typescript
// 扩展现有的 appStore
interface AppState {
  // 现有业务状态...
  
  // 新增用户认证状态
  auth: {
    user: User | null;
    token: string | null;
    isAuthenticated: boolean;
    isAdmin: boolean;
    loading: boolean;
  };
}
```

#### 6.2.3 组件架构调整
- **路由守卫**：PrivateRoute 组件保护需要登录的页面
- **权限组件**：AdminRoute 组件保护管理员专用页面
- **布局组件**：AdminLayout 添加用户信息和登出功能
- **认证组件**：LoginPage、UserManagement 等新页面

### 6.3 API设计

#### 6.3.1 认证相关API
```
POST /auth/login - 用户登录
POST /auth/logout - 用户登出
GET /auth/me - 获取当前用户信息
POST /auth/refresh - 刷新token
```

#### 6.3.2 用户管理API（需要管理员权限）
```
GET /admin/users - 获取用户列表
POST /admin/users - 创建新用户
GET /admin/users/{id} - 获取用户详情
PUT /admin/users/{id} - 更新用户信息
PUT /admin/users/{id}/status - 启用/禁用用户
DELETE /admin/users/{id} - 删除用户（软删除）
```

## 7. 实施计划

### 7.1 开发阶段划分

#### Phase 1: 后端基础（优先级：高）
- SQLite数据库配置
- User模型和认证服务
- JWT中间件和权限检查
- 用户认证API开发

#### Phase 2: 前端认证（优先级：高）
- 登录页面开发
- 认证状态管理
- 路由守卫实现
- 与后端API集成

#### Phase 3: 用户管理（优先级：中）
- 用户管理后端API
- 用户管理前端界面
- 管理员权限控制
- 系统初始化脚本

#### Phase 4: 系统集成（优先级：中）
- 现有页面认证集成
- 用户体验优化
- 错误处理完善
- 测试和文档

### 7.2 里程碑
- **M1**：用户能够登录系统并访问业务功能
- **M2**：管理员能够创建和管理用户账号
- **M3**：系统完整的权限控制和用户管理功能

## 8. 非功能需求

### 8.1 安全需求
- 密码使用bcrypt加密存储
- JWT token设置合理的过期时间
- 敏感操作需要权限验证
- 防止SQL注入和XSS攻击

### 8.2 性能需求
- 登录响应时间 < 1秒
- 权限验证响应时间 < 100ms
- 支持并发用户数：50+
- SQLite数据库文件大小预估 < 100MB

### 8.3 可用性需求
- 登录界面简洁明了
- 错误信息友好易懂
- 支持主流浏览器（Chrome、Firefox、Safari）
- 响应式设计，支持不同屏幕尺寸

### 8.4 维护需求
- 数据库迁移机制
- 日志记录和监控
- 配置文件管理
- 备份和恢复机制

## 9. 风险评估

### 9.1 技术风险
- **数据库迁移**：从无认证到有认证的系统迁移
- **权限控制**：确保现有功能不受影响
- **双数据库**：SQLite和Neo4j的数据一致性

### 9.2 业务风险
- **用户接受度**：新增登录步骤可能影响用户体验
- **管理复杂性**：用户管理增加系统管理负担
- **初始化**：需要创建初始管理员账号

### 9.3 缓解措施
- 详细的测试计划，确保功能稳定性
- 分阶段发布，逐步验证功能
- 完善的文档和培训材料
- 数据备份和回滚方案

## 10. 成功标准

### 10.1 功能标准
- ✅ 用户能够成功登录和登出
- ✅ 管理员能够创建和管理用户
- ✅ 权限控制正确执行，无越权访问
- ✅ 所有现有业务功能正常工作

### 10.2 质量标准
- ✅ 登录成功率 > 99%
- ✅ 权限验证准确率 100%
- ✅ 零安全漏洞
- ✅ 用户体验满意度 > 90%

---

**文档版本**: 1.0  
**创建日期**: 2025年1月  
**最后更新**: 2025年1月  
**状态**: 待评审