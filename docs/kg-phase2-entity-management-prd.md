# 知识图谱管理系统 - 实体数据管理与图可视化 PRD

**版本**: 1.0  
**日期**: 2025年8月23日  
**项目**: KGMS Phase 2 扩展 - 实体管理模块  
**目标**: 提供完整的实体数据管理能力和图数据可视化功能

---

## 1. 背景与目标

### 1.1 项目背景

基于Phase 2智能录入系统的成功实现，系统已具备：
- ✅ LLM标准化提取能力
- ✅ 向量相似度检测
- ✅ 实体链接确认流程
- ✅ 批量操作功能

随着数据量增长，需要专门的管理界面来维护已保存的实体数据质量，并提供可视化分析能力。

### 1.2 核心目标

1. **实体数据管理**：提供品牌、产品、成分的完整CRUD功能
2. **数据质量控制**：主动识别和解决数据重复、缺失等问题
3. **可视化分析**：类似Neo4j Browser的图数据探索体验
4. **业务洞察**：通过图分析发现产品-品牌-成分关系模式

### 1.3 成功指标

| 指标 | 目标值 | 测量方法 |
|-----|-------|---------|
| 数据重复率 | <3% | 自动检测算法 |
| 管理操作效率 | 提升60% | 对比人工SQL操作时间 |
| 图加载性能 | <2秒 | 1000节点子图 |
| 用户满意度 | >85% | 功能使用调研 |

---

## 2. 功能设计

### 2.1 整体架构

```
┌─────────────────────────────────────────────────────┐
│                实体管理系统                          │
├─────────────────────────────────────────────────────┤
│                                                     │
│  实体数据管理     数据质量监控     图数据可视化      │
│      ↓               ↓               ↓             │
│   CRUD操作        重复检测        节点关系图         │
│   批量处理        数据清理        交互式探索         │
│   搜索过滤        质量报告        路径分析           │
│                                                     │
└─────────────────────────────────────────────────────┘
                           ↓
                    Neo4j 知识图谱
```

### 2.2 功能模块详设

## 3. 实体数据管理模块

### 3.1 品牌管理

#### 3.1.1 品牌列表界面

```tsx
const BrandManagement = () => {
  return (
    <PageContainer>
      <Card title="品牌管理">
        {/* 操作工具栏 */}
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Space>
              <Input.Search 
                placeholder="搜索品牌名称" 
                onSearch={handleSearch}
                style={{ width: 300 }}
              />
              <Select placeholder="筛选条件" style={{ width: 150 }}>
                <Option value="active">活跃品牌</Option>
                <Option value="unused">未使用品牌</Option>
                <Option value="duplicate">疑似重复</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button type="primary" icon={<PlusOutlined />}>
                新增品牌
              </Button>
              <Button icon={<MergeOutlined />}>
                批量合并
              </Button>
              <Button icon={<ExportOutlined />}>
                导出数据
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 品牌列表表格 */}
        <Table
          columns={[
            { 
              title: '品牌名称', 
              dataIndex: 'name',
              sorter: true,
              render: (name, record) => (
                <Space>
                  <Text strong>{name}</Text>
                  {record.aliases?.length > 0 && (
                    <Tooltip title={`别名: ${record.aliases.join(', ')}`}>
                      <Tag size="small" color="blue">
                        +{record.aliases.length}
                      </Tag>
                    </Tooltip>
                  )}
                </Space>
              )
            },
            { 
              title: '关联产品', 
              dataIndex: 'product_count',
              sorter: true,
              render: count => <Badge count={count} showZero />
            },
            { 
              title: '创建时间', 
              dataIndex: 'created_at',
              sorter: true,
              render: date => moment(date).format('YYYY-MM-DD')
            },
            { 
              title: '最后使用', 
              dataIndex: 'last_used',
              sorter: true,
              render: date => date ? moment(date).fromNow() : '未使用'
            },
            {
              title: '数据质量',
              render: (_, record) => (
                <Space>
                  {record.quality_score > 0.8 && 
                    <Tag color="green">优</Tag>}
                  {record.quality_score <= 0.8 && record.quality_score > 0.6 && 
                    <Tag color="orange">中</Tag>}
                  {record.quality_score <= 0.6 && 
                    <Tag color="red">差</Tag>}
                  {record.has_duplicates && 
                    <Tag color="red">重复</Tag>}
                </Space>
              )
            },
            {
              title: '操作',
              render: (_, record) => (
                <Space>
                  <Button 
                    type="link" 
                    icon={<EyeOutlined />}
                    onClick={() => viewBrandDetail(record.uuid)}
                  >
                    查看
                  </Button>
                  <Button 
                    type="link" 
                    icon={<EditOutlined />}
                    onClick={() => editBrand(record.uuid)}
                  >
                    编辑
                  </Button>
                  <Button 
                    type="link" 
                    icon={<ShareAltOutlined />}
                    onClick={() => viewBrandGraph(record.uuid)}
                  >
                    关系图
                  </Button>
                  <Popconfirm 
                    title="确定删除此品牌？"
                    onConfirm={() => deleteBrand(record.uuid)}
                  >
                    <Button 
                      type="link" 
                      danger 
                      icon={<DeleteOutlined />}
                    >
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              )
            }
          ]}
          dataSource={brands}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
        />
      </Card>
    </PageContainer>
  );
};
```

#### 3.1.2 品牌详情/编辑界面

```tsx
const BrandDetailModal = ({ visible, brandId, onCancel, onSave }) => {
  return (
    <Modal
      title="品牌详情"
      open={visible}
      width={800}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>取消</Button>,
        <Button key="save" type="primary" onClick={onSave}>保存</Button>
      ]}
    >
      <Tabs defaultActiveKey="basic">
        <TabPane tab="基本信息" key="basic">
          <Form layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="品牌名称" required>
                  <Input placeholder="输入品牌标准名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="品牌代码">
                  <Input placeholder="品牌代码/缩写" />
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item label="别名管理">
              <AliasManager 
                aliases={brandData.aliases}
                onAliasesChange={handleAliasesChange}
              />
            </Form.Item>
            
            <Form.Item label="品牌描述">
              <Input.TextArea rows={4} placeholder="品牌介绍和描述" />
            </Form.Item>
          </Form>
        </TabPane>
        
        <TabPane tab="关联产品" key="products">
          <Table
            size="small"
            columns={[
              { title: '产品名称', dataIndex: 'name' },
              { title: 'SKU', dataIndex: 'sku' },
              { title: '产品类型', dataIndex: 'product_type' },
              { title: '创建时间', dataIndex: 'created_at' }
            ]}
            dataSource={brandData.products}
          />
        </TabPane>
        
        <TabPane tab="数据统计" key="stats">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic title="关联产品数" value={brandData.stats.product_count} />
            </Col>
            <Col span={8}>
              <Statistic title="成分种类数" value={brandData.stats.ingredient_count} />
            </Col>
            <Col span={8}>
              <Statistic title="质量评分" value={brandData.quality_score} precision={2} />
            </Col>
          </Row>
          
          <Divider />
          
          <Title level={5}>使用趋势</Title>
          <Line
            data={brandData.usage_trend}
            xField="date"
            yField="usage_count"
            height={200}
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};
```

### 3.2 成分管理

#### 3.2.1 成分列表功能特点
- **标准化状态**：显示是否已标准化，标准化历史
- **使用频次**：在多少产品中使用
- **单位管理**：常用单位统计，单位标准化建议
- **成分分类**：维生素、矿物质、提取物等分类管理

#### 3.2.2 成分合并功能

```tsx
const IngredientMergeWizard = () => {
  return (
    <Modal title="成分合并向导" width={1000}>
      <Steps current={currentStep}>
        <Step title="选择成分" />
        <Step title="确认合并" />
        <Step title="完成合并" />
      </Steps>
      
      {currentStep === 0 && (
        <div>
          <Alert 
            message="检测到以下成分可能重复，请选择要合并的成分"
            type="info"
            style={{ marginBottom: 16 }}
          />
          
          <Table
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: selectedIngredients,
              onChange: setSelectedIngredients
            }}
            columns={[
              { 
                title: '成分名称', 
                dataIndex: 'name',
                render: (name, record) => (
                  <Space>
                    <Text>{name}</Text>
                    <Tag color="blue">相似度: {record.similarity}%</Tag>
                  </Space>
                )
              },
              { title: '使用次数', dataIndex: 'usage_count' },
              { title: '标准化状态', dataIndex: 'standardized' },
              { 
                title: '常用单位', 
                render: (_, record) => (
                  <Space>
                    {record.common_units.map(unit => 
                      <Tag key={unit}>{unit}</Tag>
                    )}
                  </Space>
                )
              }
            ]}
            dataSource={duplicateIngredients}
          />
        </div>
      )}
      
      {currentStep === 1 && (
        <MergePreview 
          sourceIngredients={selectedIngredients}
          onPreviewChange={handlePreviewChange}
        />
      )}
    </Modal>
  );
};
```

### 3.3 产品管理

#### 3.3.1 产品列表高级功能
- **多维度筛选**：按品牌、产品类型、成分、创建时间筛选
- **批量操作**：批量删除、修改分类、导出数据
- **关系完整性检查**：检查孤立产品、缺失品牌关系等
- **产品详情页面**：显示完整的知识图谱关系

---

## 4. 数据质量监控模块

### 4.1 质量监控Dashboard

#### 4.1.1 核心指标展示

```tsx
const QualityDashboard = () => {
  return (
    <PageContainer>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        {/* 总体质量指标 */}
        <Col span={6}>
          <Card>
            <Statistic
              title="数据总量"
              value={stats.total_entities}
              prefix={<DatabaseOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="数据质量分数"
              value={stats.quality_score}
              precision={1}
              suffix="/100"
              valueStyle={{ color: getQualityColor(stats.quality_score) }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="重复实体数"
              value={stats.duplicate_count}
              valueStyle={{ color: stats.duplicate_count > 0 ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="孤立节点数"
              value={stats.orphan_count}
              valueStyle={{ color: stats.orphan_count > 0 ? '#faad14' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 实体类型分布 */}
      <Card title="实体类型分布" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Pie
              data={[
                { type: '品牌', value: stats.brand_count },
                { type: '产品', value: stats.product_count },
                { type: '成分', value: stats.ingredient_count }
              ]}
              angleField="value"
              colorField="type"
              height={200}
            />
          </Col>
          <Col span={16}>
            <Table
              size="small"
              columns={[
                { title: '实体类型', dataIndex: 'type' },
                { title: '数量', dataIndex: 'count' },
                { title: '增长', dataIndex: 'growth', render: growth => 
                  <Tag color={growth > 0 ? 'green' : 'red'}>
                    {growth > 0 ? '+' : ''}{growth}%
                  </Tag>
                },
                { title: '质量分数', dataIndex: 'quality' },
                { title: '重复数', dataIndex: 'duplicates' }
              ]}
              dataSource={stats.entity_breakdown}
              pagination={false}
            />
          </Col>
        </Row>
      </Card>

      {/* 数据质量问题 */}
      <Card title="数据质量问题" style={{ marginBottom: 16 }}>
        <List
          dataSource={qualityIssues}
          renderItem={issue => (
            <List.Item
              actions={[
                <Button type="link" onClick={() => fixIssue(issue.id)}>
                  修复
                </Button>,
                <Button type="link" onClick={() => ignoreIssue(issue.id)}>
                  忽略
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Badge 
                    status={issue.severity === 'high' ? 'error' : 'warning'}
                  />
                }
                title={issue.title}
                description={issue.description}
              />
            </List.Item>
          )}
        />
      </Card>
    </PageContainer>
  );
};
```

### 4.2 自动化数据清理

#### 4.2.1 重复检测算法
```python
class DuplicateDetector:
    """重复实体检测器"""
    
    async def detect_brand_duplicates(self, threshold: float = 0.85):
        """检测品牌重复"""
        query = """
        MATCH (b1:KGMS_Brand), (b2:KGMS_Brand)
        WHERE b1.uuid < b2.uuid
        WITH b1, b2, 
             gds.similarity.cosine(b1.embedding, b2.embedding) as similarity
        WHERE similarity > $threshold
        RETURN b1.name, b2.name, similarity
        ORDER BY similarity DESC
        """
        return await self.neo4j.run_query(query, threshold=threshold)
    
    async def detect_orphan_entities(self):
        """检测孤立实体"""
        queries = {
            'orphan_brands': """
            MATCH (b:KGMS_Brand)
            WHERE NOT (b)<-[:MANUFACTURED_BY]-()
            RETURN b.uuid, b.name, 'orphan_brand' as issue_type
            """,
            'orphan_ingredients': """
            MATCH (i:KGMS_Ingredient)
            WHERE NOT (i)<-[:CONTAINS]-()
            RETURN i.uuid, i.name, 'orphan_ingredient' as issue_type
            """
        }
        
        issues = []
        for issue_type, query in queries.items():
            results = await self.neo4j.run_query(query)
            issues.extend(results)
        return issues
```

---

## 5. 图数据可视化模块

### 5.1 技术选型

#### 5.1.1 推荐方案：vis-network
- **优势**：轻量级(160KB)，性能优秀，交互丰富
- **特点**：物理仿真布局，支持动态更新
- **兼容性**：与React + Ant Design 完美集成

#### 5.1.2 依赖配置
```json
{
  "dependencies": {
    "vis-network": "^9.1.9",
    "vis-data": "^7.1.9",
    "@types/vis-network": "^4.25.4"
  }
}
```

### 5.2 图可视化界面设计

#### 5.2.1 主界面布局

```tsx
const GraphVisualization = () => {
  return (
    <PageContainer>
      <Row gutter={16} style={{ height: 'calc(100vh - 200px)' }}>
        {/* 左侧控制面板 */}
        <Col span={6}>
          <Card title="图控制面板" style={{ height: '100%' }}>
            <Tabs defaultActiveKey="search">
              <TabPane tab="搜索" key="search">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Input.Search
                    placeholder="搜索节点"
                    onSearch={handleNodeSearch}
                  />
                  
                  <Select
                    mode="multiple"
                    placeholder="节点类型"
                    style={{ width: '100%' }}
                    options={[
                      { label: '品牌', value: 'Brand' },
                      { label: '产品', value: 'Product' },
                      { label: '成分', value: 'Ingredient' }
                    ]}
                    onChange={handleNodeTypeFilter}
                  />
                  
                  <Slider
                    range
                    defaultValue={[1, 3]}
                    min={1}
                    max={5}
                    marks={{ 1: '1度', 3: '3度', 5: '5度' }}
                    onChange={handleDepthChange}
                  />
                </Space>
              </TabPane>
              
              <TabPane tab="布局" key="layout">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Select
                    defaultValue="physics"
                    style={{ width: '100%' }}
                    onChange={handleLayoutChange}
                  >
                    <Option value="physics">物理仿真</Option>
                    <Option value="hierarchical">层次布局</Option>
                    <Option value="circular">环形布局</Option>
                    <Option value="random">随机布局</Option>
                  </Select>
                  
                  <Divider />
                  
                  <Text strong>物理参数</Text>
                  <div>
                    <Text>引力强度</Text>
                    <Slider defaultValue={-30} min={-100} max={-10} />
                  </div>
                  <div>
                    <Text>斥力距离</Text>
                    <Slider defaultValue={100} min={50} max={300} />
                  </div>
                </Space>
              </TabPane>
              
              <TabPane tab="分析" key="analysis">
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Button block onClick={findShortestPath}>
                    最短路径分析
                  </Button>
                  <Button block onClick={findCommunities}>
                    社区发现
                  </Button>
                  <Button block onClick={calculateCentrality}>
                    中心性分析
                  </Button>
                  
                  <Divider />
                  
                  <Statistic title="当前节点数" value={graphStats.nodeCount} />
                  <Statistic title="边数" value={graphStats.edgeCount} />
                  <Statistic title="连通分量" value={graphStats.components} />
                </Space>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
        
        {/* 主图区域 */}
        <Col span={12}>
          <Card 
            title="知识图谱"
            extra={
              <Space>
                <Button icon={<ZoomInOutlined />} onClick={zoomIn} />
                <Button icon={<ZoomOutOutlined />} onClick={zoomOut} />
                <Button icon={<ReloadOutlined />} onClick={resetView} />
                <Button icon={<FullscreenOutlined />} onClick={toggleFullscreen} />
              </Space>
            }
            style={{ height: '100%' }}
          >
            <div 
              ref={graphContainer}
              style={{ width: '100%', height: 'calc(100% - 57px)' }}
            />
          </Card>
        </Col>
        
        {/* 右侧详情面板 */}
        <Col span={6}>
          <Card title="节点详情" style={{ height: '100%' }}>
            {selectedNode ? (
              <NodeDetailPanel node={selectedNode} />
            ) : (
              <Empty description="点击节点查看详情" />
            )}
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};
```

#### 5.2.2 图配置参数

```tsx
const networkOptions = {
  nodes: {
    shape: 'dot',
    size: 16,
    font: {
      size: 14,
      face: 'Tahoma, Arial, sans-serif'
    },
    borderWidth: 2,
    shadow: true
  },
  edges: {
    width: 2,
    color: { inherit: 'from' },
    smooth: {
      type: 'continuous',
      forceDirection: 'none',
      roundness: 0.5
    },
    arrows: {
      to: { enabled: true, scaleFactor: 1.2 }
    },
    font: {
      size: 12,
      align: 'horizontal'
    }
  },
  physics: {
    enabled: true,
    stabilization: { iterations: 100 },
    barnesHut: {
      gravitationalConstant: -30000,
      centralGravity: 0.3,
      springLength: 95,
      springConstant: 0.04,
      damping: 0.09
    }
  },
  interaction: {
    hover: true,
    hoverConnectedEdges: true,
    selectConnectedEdges: false,
    tooltipDelay: 300,
    zoomView: true
  }
};
```

### 5.3 节点和边的样式设计

#### 5.3.1 节点样式配置
```javascript
const nodeStyles = {
  Brand: {
    color: { background: '#4285f4', border: '#1a73e8' },
    shape: 'box',
    icon: { face: 'FontAwesome', code: '\uf1ad', size: 20, color: 'white' }
  },
  Product: {
    color: { background: '#9c27b0', border: '#7b1fa2' },
    shape: 'ellipse',
    icon: { face: 'FontAwesome', code: '\uf291', size: 20, color: 'white' }
  },
  Ingredient: {
    color: { background: '#4caf50', border: '#388e3c' },
    shape: 'triangle',
    icon: { face: 'FontAwesome', code: '\uf0c3', size: 20, color: 'white' }
  }
};

const edgeStyles = {
  MANUFACTURED_BY: {
    color: { color: '#4285f4' },
    width: 3,
    dashes: false,
    label: '制造商'
  },
  CONTAINS: {
    color: { color: '#4caf50' },
    width: 2,
    dashes: [5, 5],
    label: '包含'
  }
};
```

### 5.4 交互功能实现

#### 5.4.1 节点点击事件
```tsx
const handleNodeClick = (params) => {
  if (params.nodes.length > 0) {
    const nodeId = params.nodes[0];
    const nodeData = networkData.nodes.get(nodeId);
    
    setSelectedNode(nodeData);
    
    // 高亮相邻节点
    const connectedNodes = network.getConnectedNodes(nodeId);
    const highlightNodes = [nodeId, ...connectedNodes];
    
    network.selectNodes(highlightNodes);
    
    // 获取节点详细信息
    fetchNodeDetails(nodeId);
  }
};

const handleEdgeClick = (params) => {
  if (params.edges.length > 0) {
    const edgeId = params.edges[0];
    const edgeData = networkData.edges.get(edgeId);
    
    setSelectedEdge(edgeData);
    showEdgeDetails(edgeData);
  }
};
```

#### 5.4.2 路径查询功能
```tsx
const PathQueryModal = ({ visible, onCancel }) => {
  return (
    <Modal
      title="最短路径查询"
      open={visible}
      onCancel={onCancel}
      width={600}
    >
      <Form layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="起始节点">
              <Select
                showSearch
                placeholder="选择起始节点"
                optionFilterProp="children"
                onChange={setSourceNode}
              >
                {allNodes.map(node => (
                  <Option key={node.id} value={node.id}>
                    {node.label} ({node.type})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="目标节点">
              <Select
                showSearch
                placeholder="选择目标节点"
                optionFilterProp="children"
                onChange={setTargetNode}
              >
                {allNodes.map(node => (
                  <Option key={node.id} value={node.id}>
                    {node.label} ({node.type})
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item>
          <Button 
            type="primary" 
            onClick={findPath}
            loading={pathFinding}
          >
            查找路径
          </Button>
        </Form.Item>
        
        {pathResult && (
          <Alert
            message={`找到路径，长度: ${pathResult.length - 1}`}
            description={
              <div>
                <Text>路径: </Text>
                {pathResult.map((node, index) => (
                  <span key={node.id}>
                    <Tag color="blue">{node.label}</Tag>
                    {index < pathResult.length - 1 && ' → '}
                  </span>
                ))}
              </div>
            }
            type="success"
          />
        )}
      </Form>
    </Modal>
  );
};
```

---

## 6. 后端API设计

### 6.1 实体管理API

#### 6.1.1 品牌管理接口

```python
# 品牌列表
@router.get("/brands", response_model=BrandListResponse)
async def get_brands(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    search: str = Query(None),
    filter_type: str = Query(None),  # active, unused, duplicate
    sort_by: str = Query("created_at"),
    sort_order: str = Query("desc")
):
    """获取品牌列表"""
    
# 品牌详情
@router.get("/brands/{brand_id}", response_model=BrandDetailResponse)
async def get_brand_detail(brand_id: str):
    """获取品牌详情"""
    
# 更新品牌
@router.put("/brands/{brand_id}", response_model=BrandUpdateResponse)
async def update_brand(brand_id: str, brand_data: BrandUpdateRequest):
    """更新品牌信息"""
    
# 删除品牌
@router.delete("/brands/{brand_id}")
async def delete_brand(brand_id: str):
    """删除品牌（需检查关联关系）"""
    
# 批量合并品牌
@router.post("/brands/merge", response_model=BrandMergeResponse)
async def merge_brands(merge_request: BrandMergeRequest):
    """合并多个品牌"""
```

### 6.2 数据质量API

```python
# 质量监控概览
@router.get("/quality/overview", response_model=QualityOverviewResponse)
async def get_quality_overview():
    """获取数据质量概览"""
    
# 重复检测
@router.post("/quality/detect-duplicates")
async def detect_duplicates(detection_config: DuplicateDetectionConfig):
    """检测重复实体"""
    
# 孤立节点检测
@router.get("/quality/orphan-entities")
async def get_orphan_entities():
    """获取孤立实体列表"""
    
# 数据清理
@router.post("/quality/cleanup")
async def cleanup_data(cleanup_config: DataCleanupConfig):
    """执行数据清理操作"""
```

### 6.3 图数据API

```python
# 获取子图数据
@router.get("/graph/subgraph", response_model=SubgraphResponse)
async def get_subgraph(
    center_node: str = Query(...),
    depth: int = Query(2, ge=1, le=5),
    node_types: List[str] = Query([]),
    max_nodes: int = Query(100, ge=10, le=1000)
):
    """获取以指定节点为中心的子图"""
    
# 路径查询
@router.post("/graph/shortest-path", response_model=PathResponse)
async def find_shortest_path(path_request: PathRequest):
    """查找两个节点间的最短路径"""
    
# 邻居节点查询
@router.get("/graph/neighbors/{node_id}")
async def get_node_neighbors(
    node_id: str,
    depth: int = Query(1, ge=1, le=3)
):
    """获取节点的邻居"""
    
# 图统计信息
@router.get("/graph/statistics", response_model=GraphStatsResponse)
async def get_graph_statistics():
    """获取图的统计信息"""
```

---

## 7. 数据模型设计

### 7.1 扩展的Neo4j模型

```cypher
// 增强的品牌节点
(b:KGMS_Brand {
  uuid: "brand_001",
  name: "Nature's Bounty",
  aliases: ["natures bounty", "自然之宝"],
  brand_code: "NB",
  description: "知名保健品品牌",
  embedding: [0.123, -0.456, ...],
  statistics: {
    product_count: 238,
    ingredient_variety: 89,
    avg_rating: 4.5,
    market_share: 0.15
  },
  quality_metrics: {
    completeness_score: 0.95,
    consistency_score: 0.88,
    accuracy_score: 0.92
  },
  created_at: datetime(),
  updated_at: datetime(),
  last_used: datetime()
})

// 增强的成分节点
(i:KGMS_Ingredient {
  uuid: "ing_001",
  name: "维生素C",
  standardized_name: "L-抗坏血酸",
  category: "维生素",
  common_units: ["mg", "g", "IU"],
  usage_statistics: {
    frequency: 156,
    avg_amount: 500,
    common_unit: "mg"
  },
  created_at: datetime(),
  updated_at: datetime()
})

// 质量评估节点
(q:QualityIssue {
  uuid: "issue_001",
  issue_type: "duplicate_brand",
  severity: "high",
  entity_ids: ["brand_001", "brand_002"],
  similarity_score: 0.95,
  status: "pending",
  description: "疑似重复品牌",
  created_at: datetime()
})
```

### 7.2 新增关系类型

```cypher
// 相似关系
(b1:KGMS_Brand)-[:SIMILAR_TO {similarity: 0.85}]->(b2:KGMS_Brand)

// 别名关系
(b:KGMS_Brand)-[:HAS_ALIAS {alias: "自然之宝"}]->(b:KGMS_Brand)

// 质量问题关系
(e:KGMS_Brand)-[:HAS_QUALITY_ISSUE]->(q:QualityIssue)
```

---

## 8. 实施计划

### 8.1 开发阶段

#### Phase 1: 实体管理基础功能 (2周)
- **Week 1**: 
  - 后端API开发 (品牌/成分/产品CRUD)
  - 数据库模型扩展
  - 基础前端界面

- **Week 2**:
  - 搜索、筛选、分页功能
  - 批量操作功能
  - 数据导入导出

#### Phase 2: 数据质量工具 (2周)  
- **Week 3**:
  - 重复检测算法
  - 质量监控Dashboard
  - 自动化清理工具

- **Week 4**:
  - 数据质量评分系统
  - 质量报告生成
  - 问题修复向导

#### Phase 3: 图可视化核心功能 (3周)
- **Week 5**:
  - vis-network集成
  - 基础图展示功能
  - 节点/边样式配置

- **Week 6**:
  - 交互功能实现
  - 布局算法集成
  - 搜索和过滤

- **Week 7**:
  - 路径查询功能
  - 图分析工具
  - 性能优化

#### Phase 4: 高级功能和优化 (1周)
- **Week 8**:
  - 社区发现算法
  - 中心性分析
  - 用户体验优化

### 8.2 测试计划

#### 功能测试
1. **CRUD操作测试**: 验证所有增删改查功能
2. **数据质量测试**: 测试重复检测准确率
3. **图可视化测试**: 验证图展示和交互功能
4. **性能测试**: 大数据量下的响应时间

#### 集成测试
1. **前后端API集成**: 验证数据传输正确性
2. **数据库操作**: 验证复杂查询的准确性
3. **图算法集成**: 验证路径查询和分析功能

### 8.3 上线部署

#### 部署环境要求
- **前端**: Nginx + React SPA
- **后端**: FastAPI + Uvicorn
- **数据库**: Neo4j 5.x
- **监控**: 图数据性能监控

#### 性能目标
- 实体列表加载: <1秒
- 图可视化渲染: <2秒 (1000节点)
- API响应时间: <500ms
- 数据质量检测: <5秒

---

## 9. 风险评估与对策

### 9.1 技术风险

| 风险 | 影响 | 概率 | 对策 |
|-----|-----|-----|-----|
| 图可视化性能问题 | 高 | 中 | 1. 限制节点数量<br>2. 实现分页加载<br>3. 使用WebGL渲染 |
| Neo4j查询性能 | 中 | 中 | 1. 优化Cypher查询<br>2. 创建合适索引<br>3. 查询结果缓存 |
| 前端内存占用 | 中 | 低 | 1. 虚拟滚动<br>2. 数据分片加载<br>3. 内存泄漏检测 |

### 9.2 业务风险

| 风险 | 影响 | 概率 | 对策 |
|-----|-----|-----|-----|
| 数据误删除 | 高 | 低 | 1. 软删除机制<br>2. 操作确认流程<br>3. 数据备份策略 |
| 批量操作错误 | 中 | 中 | 1. 操作预览功能<br>2. 回滚机制<br>3. 权限控制 |

---

## 10. 成功标准

### 10.1 量化指标

| 指标 | 目标值 | 验收标准 |
|-----|-------|---------|
| 数据质量分数 | >90分 | 自动质量评估系统 |
| 重复实体检出率 | >95% | 人工验证100个样本 |
| 图可视化响应时间 | <2秒 | 1000节点子图加载 |
| 用户操作效率提升 | >60% | 对比人工SQL操作 |
| 系统可用性 | >99% | 7×24小时监控 |

### 10.2 定性标准

1. **用户体验**: 界面直观易用，操作流程顺畅
2. **功能完整性**: 覆盖实体管理的所有核心需求
3. **数据安全性**: 防止误操作，支持数据恢复
4. **系统稳定性**: 大数据量下系统稳定运行

---

## 11. 附录

### 11.1 技术选型对比

#### 图可视化库对比

| 库名 | 大小 | 性能 | 功能 | 学习成本 | 推荐度 |
|-----|-----|-----|-----|---------|-------|
| vis-network | 160KB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| D3.js | 300KB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| Cytoscape.js | 500KB | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |

### 11.2 界面原型示例

#### 品牌管理界面
```
┌─────────────────────────────────────────────────────────┐
│ 🔍 搜索品牌  [筛选] [导出] [批量合并] [+ 新增品牌]        │
├─────────────────────────────────────────────────────────┤
│ ✓ 品牌名称    关联产品  创建时间    质量  操作            │
│ ✓ Nature's Bounty  238   2024-01-15  优   查看|编辑|删除 │
│ ✓ GNC              156   2024-01-20  中   查看|编辑|删除 │
│ ✓ Swisse           89    2024-02-01  差   查看|编辑|删除 │
│                                                         │
│ 第 1-20 条，共 156 条  [<] [1] [2] [3] [>]              │
└─────────────────────────────────────────────────────────┘
```

#### 图可视化界面
```
┌─────────┬───────────────────────────────┬─────────┐
│ 控制面板 │           图可视化区域         │ 详情面板 │
│         │                              │         │
│ 🔍搜索  │     ●──●──●                  │ 节点详情 │
│ 📊筛选  │    /│\ /│\ /│\                │         │
│ ⚙️布局  │   ● ● ● ● ● ●                │ 统计信息 │
│ 📈分析  │    \│/ \│/ \│/                │         │
│         │     ●──●──●                  │ 关系列表 │
│ 统计:   │                              │         │
│ 节点:89 │  [🔍] [+] [-] [↻] [⛶]       │         │
│ 边:156  │                              │         │
└─────────┴───────────────────────────────┴─────────┘
```

---

**文档结束**

本PRD文档为实体数据管理与图可视化功能提供了完整的设计方案，确保系统能够提供专业级的数据管理和分析能力。