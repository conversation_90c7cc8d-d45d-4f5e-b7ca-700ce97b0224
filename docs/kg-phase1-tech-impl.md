# Phase 1 技术实现方案

## 1. 技术栈定义

### 前端技术栈
- **框架**: React 18.x + TypeScript
- **UI组件库**: Ant Design 5.x
- **状态管理**: <PERSON>ustand（轻量级，适合MVP）
- **HTTP客户端**: Axios
- **开发工具**: Vite

### 后端技术栈
- **框架**: FastAPI 0.100+
- **AI框架**: LangChain（Phase 1）/ LangGraph（Phase 2预留）
- **LLM**: OpenAI GPT-4 / Anthropic Claude
- **数据库**: Neo4j 5.x
- **异步驱动**: neo4j-python-driver (async)
- **验证**: Pydantic v2

---

## 2. 前端实现方案

### 2.1 项目结构
```
frontend/
├── src/
│   ├── components/
│   │   ├── ProductForm/
│   │   │   ├── index.tsx
│   │   │   ├── ExtractionPanel.tsx    # AI提取面板
│   │   │   ├── FormFields.tsx         # 表单字段
│   │   │   ├── EntitySelector.tsx     # 实体选择器
│   │   │   └── styles.module.css
│   │   └── common/
│   ├── services/
│   │   ├── api.ts                     # API调用
│   │   └── types.ts                   # TypeScript类型
│   ├── stores/
│   │   └── productStore.ts            # Zustand store
│   └── utils/
│       └── validators.ts              # 表单验证
```

### 2.2 核心组件实现

#### ExtractionPanel组件
```tsx
import { Button, Input, Alert, Spin } from 'antd';
import { RobotOutlined, ClearOutlined } from '@ant-design/icons';

const ExtractionPanel: React.FC = () => {
  const { rawText, setRawText, extractData, isExtracting, error } = useProductStore();

  return (
    <Card title="AI智能提取" extra={<RobotOutlined />}>
      <Input.TextArea
        rows={8}
        value={rawText}
        onChange={(e) => setRawText(e.target.value)}
        placeholder="粘贴产品描述文本..."
        maxLength={10000}
        showCount
      />
      
      {error && (
        <Alert
          message="提取失败"
          description={error}
          type="error"
          showIcon
          closable
          style={{ marginTop: 16 }}
        />
      )}
      
      <Space style={{ marginTop: 16 }}>
        <Button
          icon={<ClearOutlined />}
          onClick={() => setRawText('')}
          disabled={!rawText || isExtracting}
        >
          清空文本
        </Button>
        
        <Button
          type="primary"
          icon={<RobotOutlined />}
          onClick={extractData}
          loading={isExtracting}
          disabled={!rawText}
        >
          AI 自动填充表单
        </Button>
      </Space>
    </Card>
  );
};
```

#### ProductForm组件
```tsx
import { Form, Input, Select, Button, Space, Card, Divider } from 'antd';
import { PlusOutlined, DeleteOutlined, SaveOutlined } from '@ant-design/icons';

const ProductForm: React.FC = () => {
  const [form] = Form.useForm();
  const { formData, saveProduct, isSaving } = useProductStore();

  // 监听AI提取结果并填充表单
  useEffect(() => {
    if (formData) {
      form.setFieldsValue(formData);
    }
  }, [formData]);

  return (
    <Card title="产品信息表单">
      <Form
        form={form}
        layout="vertical"
        onFinish={saveProduct}
        initialValues={{ product_type: '保健品' }}
      >
        {/* 基础信息 */}
        <Divider orientation="left">基础信息</Divider>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="产品名称"
              name="product_name"
              rules={[
                { required: true, message: '请输入产品名称' },
                { min: 2, max: 100, message: '产品名称长度应在2-100字符之间' }
              ]}
            >
              <Input placeholder="请输入产品名称" />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              label="品牌"
              name="brand"
              rules={[{ required: true, message: '请选择或输入品牌' }]}
            >
              <EntitySelector
                entityType="Brand"
                placeholder="请选择或输入品牌"
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 成分信息 */}
        <Divider orientation="left">成分信息</Divider>
        
        <Form.List name="ingredients">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Space key={key} align="baseline" style={{ width: '100%' }}>
                  <Form.Item
                    {...restField}
                    name={[name, 'name']}
                    rules={[{ required: true, message: '请输入成分名称' }]}
                    style={{ width: 200 }}
                  >
                    <EntitySelector
                      entityType="Ingredient"
                      placeholder="成分名称"
                    />
                  </Form.Item>
                  
                  <Form.Item
                    {...restField}
                    name={[name, 'amount']}
                    style={{ width: 100 }}
                  >
                    <InputNumber placeholder="含量" min={0} />
                  </Form.Item>
                  
                  <Form.Item
                    {...restField}
                    name={[name, 'unit']}
                    style={{ width: 100 }}
                  >
                    <Select placeholder="单位">
                      <Select.Option value="mg">mg</Select.Option>
                      <Select.Option value="g">g</Select.Option>
                      <Select.Option value="ml">ml</Select.Option>
                      <Select.Option value="IU">IU</Select.Option>
                    </Select>
                  </Form.Item>
                  
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => remove(name)}
                  />
                </Space>
              ))}
              
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  添加成分
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>

        {/* 操作按钮 */}
        <Form.Item>
          <Space>
            <Button htmlType="button" onClick={() => form.resetFields()}>
              清空表单
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={isSaving}
              icon={<SaveOutlined />}
            >
              保存
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={isSaving}
              onClick={() => {/* 保存后清空 */}}
            >
              保存并新建
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};
```

#### EntitySelector组件（实体选择器）
```tsx
import { AutoComplete, Tag } from 'antd';
import { CheckCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';

const EntitySelector: React.FC<{
  entityType: 'Brand' | 'Ingredient';
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}> = ({ entityType, value, onChange, placeholder }) => {
  const [options, setOptions] = useState([]);
  const [searching, setSearching] = useState(false);
  const [entityStatus, setEntityStatus] = useState<'new' | 'existing' | null>(null);

  const handleSearch = debounce(async (searchText: string) => {
    if (searchText.length < 2) {
      setOptions([]);
      return;
    }

    setSearching(true);
    try {
      const results = await searchEntities(entityType, searchText);
      setOptions(results.map(item => ({
        value: item.name,
        label: (
          <Space>
            {item.name}
            <Tag color="green" icon={<CheckCircleOutlined />}>
              已存在
            </Tag>
          </Space>
        ),
        existing: true
      })));

      // 检查是否为新实体
      const exactMatch = results.find(r => r.name === searchText);
      setEntityStatus(exactMatch ? 'existing' : 'new');
    } finally {
      setSearching(false);
    }
  }, 300);

  return (
    <Space.Compact style={{ width: '100%' }}>
      <AutoComplete
        value={value}
        onChange={(val) => {
          onChange?.(val);
          handleSearch(val);
        }}
        options={options}
        onSearch={handleSearch}
        placeholder={placeholder}
        loading={searching}
        style={{ width: '100%' }}
      />
      {entityStatus && (
        <Tag
          color={entityStatus === 'existing' ? 'success' : 'warning'}
          icon={entityStatus === 'existing' ? 
            <CheckCircleOutlined /> : 
            <PlusCircleOutlined />
          }
        >
          {entityStatus === 'existing' ? '已链接' : '将创建'}
        </Tag>
      )}
    </Space.Compact>
  );
};
```

---

## 3. 后端实现方案

### 3.1 项目结构
```
backend/
├── app/
│   ├── api/
│   │   ├── endpoints/
│   │   │   ├── extract.py
│   │   │   ├── products.py
│   │   │   └── entities.py
│   │   └── deps.py
│   ├── core/
│   │   ├── config.py
│   │   └── security.py
│   ├── db/
│   │   ├── neo4j.py
│   │   └── queries.py
│   ├── langchain/
│   │   ├── chains.py
│   │   ├── prompts.py
│   │   └── parsers.py
│   ├── models/
│   │   ├── product.py
│   │   └── entity.py
│   └── main.py
├── tests/
└── requirements.txt
```

### 3.2 核心模块实现

#### LangChain提取链
```python
# app/langchain/chains.py
from langchain.chat_models import ChatOpenAI
from langchain.output_parsers import PydanticOutputParser
from langchain.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from typing import List, Optional

class IngredientInfo(BaseModel):
    name: str = Field(description="成分名称")
    amount: Optional[float] = Field(description="含量数值")
    unit: Optional[str] = Field(description="单位")

class ProductInfo(BaseModel):
    product_name: str = Field(description="产品名称")
    brand: str = Field(description="品牌名称")
    product_type: str = Field(default="保健品", description="产品类型")
    sku: Optional[str] = Field(description="产品SKU")
    ingredients: List[IngredientInfo] = Field(description="成分列表")
    benefits: Optional[str] = Field(description="功效描述")

class ProductExtractionChain:
    def __init__(self, llm_model: str = "gpt-4"):
        self.llm = ChatOpenAI(
            model=llm_model,
            temperature=0,
            max_retries=2
        )
        self.parser = PydanticOutputParser(pydantic_object=ProductInfo)
        
        # 构建提示模板
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个保健品行业的数据分析专家。
            请从提供的文本中提取产品信息。
            {format_instructions}
            
            注意事项：
            1. 仔细识别所有成分及其含量
            2. 品牌名称要准确完整
            3. 如果某个字段无法确定，可以留空
            """),
            ("user", "请从以下文本中提取产品信息：\n\n{text}")
        ])
        
        # 构建链
        self.chain = self.prompt | self.llm | self.parser
    
    async def extract(self, text: str) -> ProductInfo:
        """执行产品信息提取"""
        try:
            result = await self.chain.ainvoke({
                "text": text,
                "format_instructions": self.parser.get_format_instructions()
            })
            return result
        except Exception as e:
            # 降级处理：返回部分提取结果
            logger.error(f"Extraction failed: {e}")
            # 尝试使用更简单的提取策略
            return await self._fallback_extraction(text)
    
    async def _fallback_extraction(self, text: str) -> ProductInfo:
        """降级提取策略"""
        # 使用正则或简单规则提取基础信息
        # 这里是伪代码示例
        return ProductInfo(
            product_name="需要手动填写",
            brand="需要手动填写",
            ingredients=[]
        )
```

#### FastAPI端点实现
```python
# app/api/endpoints/extract.py
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from app.langchain.chains import ProductExtractionChain

router = APIRouter()

class ExtractionRequest(BaseModel):
    text: str

class ExtractionResponse(BaseModel):
    success: bool
    data: dict
    confidence: float = 0.0
    error: Optional[str] = None

extraction_chain = ProductExtractionChain()

@router.post("/extract", response_model=ExtractionResponse)
async def extract_product_info(request: ExtractionRequest):
    """AI提取产品信息"""
    try:
        # 执行提取
        result = await extraction_chain.extract(request.text)
        
        # 计算置信度（示例逻辑）
        confidence = calculate_confidence(result)
        
        return ExtractionResponse(
            success=True,
            data=result.dict(),
            confidence=confidence
        )
    except Exception as e:
        logger.error(f"Extraction error: {e}")
        return ExtractionResponse(
            success=False,
            data={},
            error=str(e)
        )

def calculate_confidence(result: ProductInfo) -> float:
    """计算提取结果的置信度"""
    score = 0.0
    total = 5.0
    
    if result.product_name and result.product_name != "需要手动填写":
        score += 1.0
    if result.brand and result.brand != "需要手动填写":
        score += 1.0
    if result.ingredients:
        score += 1.0
    if result.benefits:
        score += 1.0
    if result.sku:
        score += 1.0
    
    return score / total
```

#### Neo4j操作层
```python
# app/db/neo4j.py
from neo4j import AsyncGraphDatabase
from typing import List, Dict, Optional
import uuid

class Neo4jService:
    def __init__(self, uri: str, username: str, password: str):
        self.driver = AsyncGraphDatabase.driver(
            uri, 
            auth=(username, password)
        )
    
    async def save_product(self, product_data: dict) -> str:
        """保存产品到知识图谱"""
        product_id = str(uuid.uuid4())
        
        async with self.driver.session() as session:
            # 使用事务确保原子性
            async with session.begin_transaction() as tx:
                # 创建或匹配品牌
                await tx.run("""
                    MERGE (b:Brand {name: $brand_name})
                    ON CREATE SET b.uuid = $brand_uuid, 
                                  b.created_at = datetime()
                """, 
                brand_name=product_data['brand'],
                brand_uuid=str(uuid.uuid4()))
                
                # 创建产品节点
                await tx.run("""
                    CREATE (p:Product {
                        uuid: $uuid,
                        name: $name,
                        sku: $sku,
                        product_type: $product_type,
                        benefits: $benefits,
                        created_at: datetime()
                    })
                """, 
                uuid=product_id,
                name=product_data['product_name'],
                sku=product_data.get('sku'),
                product_type=product_data.get('product_type', '保健品'),
                benefits=product_data.get('benefits'))
                
                # 建立品牌关系
                await tx.run("""
                    MATCH (p:Product {uuid: $product_uuid})
                    MATCH (b:Brand {name: $brand_name})
                    CREATE (p)-[:MANUFACTURED_BY]->(b)
                """,
                product_uuid=product_id,
                brand_name=product_data['brand'])
                
                # 处理成分
                for ingredient in product_data.get('ingredients', []):
                    # 创建或匹配成分
                    await tx.run("""
                        MERGE (i:Ingredient {name: $name})
                        ON CREATE SET i.uuid = $uuid,
                                      i.created_at = datetime()
                    """,
                    name=ingredient['name'],
                    uuid=str(uuid.uuid4()))
                    
                    # 建立包含关系
                    await tx.run("""
                        MATCH (p:Product {uuid: $product_uuid})
                        MATCH (i:Ingredient {name: $ingredient_name})
                        CREATE (p)-[:CONTAINS {
                            amount: $amount,
                            unit: $unit
                        }]->(i)
                    """,
                    product_uuid=product_id,
                    ingredient_name=ingredient['name'],
                    amount=ingredient.get('amount'),
                    unit=ingredient.get('unit'))
                
                await tx.commit()
        
        return product_id
    
    async def search_entities(
        self, 
        entity_type: str, 
        keyword: str, 
        limit: int = 5
    ) -> List[Dict]:
        """搜索实体"""
        async with self.driver.session() as session:
            query = f"""
                MATCH (e:{entity_type})
                WHERE toLower(e.name) CONTAINS toLower($keyword)
                RETURN e.name as name, e.uuid as uuid
                ORDER BY size(e.name)
                LIMIT $limit
            """
            
            result = await session.run(
                query,
                keyword=keyword,
                limit=limit
            )
            
            return [dict(record) async for record in result]
```

#### 主应用入口
```python
# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import extract, products, entities
from app.core.config import settings

app = FastAPI(
    title="知识图谱智能录入系统",
    version="1.0.0",
    description="Phase 1 - MVP核心功能"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(
    extract.router,
    prefix="/api/v1",
    tags=["extraction"]
)
app.include_router(
    products.router,
    prefix="/api/v1",
    tags=["products"]
)
app.include_router(
    entities.router,
    prefix="/api/v1",
    tags=["entities"]
)

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    # 初始化Neo4j连接池
    # 预热LLM模型
    pass

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    # 关闭数据库连接
    pass

@app.get("/health")
async def health_check():
    return {"status": "healthy"}
```

---

## 4. 配置文件

### 4.1 前端环境配置
```typescript
// .env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

// .env.production
VITE_API_BASE_URL=https://api.knowledge-graph.com
VITE_API_TIMEOUT=30000
```

### 4.2 后端环境配置
```python
# app/core/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # API配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "知识图谱智能录入系统"
    
    # Neo4j配置
    NEO4J_URI: str = "bolt://localhost:7687"
    NEO4J_USER: str = "neo4j"
    NEO4J_PASSWORD: str
    
    # LLM配置
    OPENAI_API_KEY: str
    OPENAI_MODEL: str = "gpt-4"
    OPENAI_MAX_RETRIES: int = 3
    OPENAI_TIMEOUT: int = 30
    
    # LangChain配置
    LANGCHAIN_CACHE_ENABLED: bool = True
    LANGCHAIN_VERBOSE: bool = False
    
    class Config:
        env_file = ".env"

settings = Settings()
```

---

## 5. 部署配置

### 5.1 Docker Compose
```yaml
version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    environment:
      - VITE_API_BASE_URL=http://backend:8000
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    depends_on:
      - neo4j

  neo4j:
    image: neo4j:5.12
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD}
      - NEO4J_PLUGINS=["apoc"]
    volumes:
      - neo4j_data:/data

volumes:
  neo4j_data:
```

---

## 6. 测试策略

### 6.1 前端测试
```typescript
// ProductForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

describe('ProductForm', () => {
  it('should auto-fill form after AI extraction', async () => {
    // Mock API response
    mockExtractAPI.mockResolvedValue({
      product_name: 'Test Product',
      brand: 'Test Brand',
      ingredients: [{ name: 'Vitamin C', amount: 1000, unit: 'mg' }]
    });
    
    render(<ProductForm />);
    
    // 输入文本
    const textArea = screen.getByPlaceholderText('粘贴产品描述文本...');
    await userEvent.type(textArea, 'Test product description');
    
    // 点击提取按钮
    const extractButton = screen.getByText('AI 自动填充表单');
    fireEvent.click(extractButton);
    
    // 验证表单被填充
    await waitFor(() => {
      expect(screen.getByDisplayValue('Test Product')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Brand')).toBeInTheDocument();
    });
  });
});
```

### 6.2 后端测试
```python
# tests/test_extraction.py
import pytest
from httpx import AsyncClient

@pytest.mark.asyncio
async def test_extract_product_info(client: AsyncClient):
    """测试产品信息提取"""
    response = await client.post(
        "/api/v1/extract",
        json={
            "text": "Nature's Bounty维生素C 1000mg，增强免疫力"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "Nature's Bounty" in data["data"]["brand"]
    assert len(data["data"]["ingredients"]) > 0
```

---

## 7. 监控与日志

### 7.1 关键指标监控
```python
# app/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge

# 定义指标
extraction_requests = Counter(
    'extraction_requests_total',
    'Total number of extraction requests'
)

extraction_duration = Histogram(
    'extraction_duration_seconds',
    'Time spent on extraction'
)

extraction_success_rate = Gauge(
    'extraction_success_rate',
    'Success rate of extractions'
)

active_users = Gauge(
    'active_users',
    'Number of active users'
)
```

### 7.2 日志配置
```python
# app/core/logging.py
import logging
from pythonjsonlogger import jsonlogger

def setup_logging():
    logHandler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        fmt='%(asctime)s %(levelname)s %(name)s %(message)s'
    )
    logHandler.setFormatter(formatter)
    
    logger = logging.getLogger()
    logger.addHandler(logHandler)
    logger.setLevel(logging.INFO)
    
    return logger
```

---

## 8. Phase 2 准备（LangGraph预留接口）

```python
# app/langgraph/workflows.py (预留)
from typing import TypedDict
from langgraph.graph import StateGraph, END

class ExtractionState(TypedDict):
    """定义工作流状态"""
    raw_text: str
    extracted_data: dict
    validation_errors: list
    enriched_data: dict
    confidence_score: float

def create_extraction_workflow():
    """创建复杂的提取工作流（Phase 2使用）"""
    workflow = StateGraph(ExtractionState)
    
    # 添加节点
    workflow.add_node("extract", extract_with_llm)
    workflow.add_node("validate", validate_extraction)
    workflow.add_node("enrich", enrich_with_knowledge_base)
    workflow.add_node("review", human_review_needed)
    
    # 定义流程
    workflow.set_entry_point("extract")
    workflow.add_edge("extract", "validate")
    workflow.add_conditional_edges(
        "validate",
        should_enrich,
        {
            True: "enrich",
            False: "review"
        }
    )
    
    return workflow.compile()
```

---

## 总结

这个技术实现方案：

1. **充分利用Ant Design**的成熟组件，特别是Form、AutoComplete等
2. **FastAPI + LangChain**的组合既简单又强大，适合Phase 1
3. **预留了LangGraph接口**，方便Phase 2升级
4. **完整的测试和监控**方案，确保系统稳定性

建议Phase 1先用LangChain实现核心功能，待验证后在Phase 2引入LangGraph处理更复杂的工作流。