#!/bin/bash
# KGMS Production Deployment Script
# 一键部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        print_warning "不建议使用root用户运行，但继续部署..."
    fi
}

# 检查Docker和Docker Compose
check_dependencies() {
    print_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_success "依赖检查通过"
}

# 检查环境变量文件
check_env() {
    print_info "检查环境变量配置..."
    
    if [[ ! -f .env ]]; then
        if [[ -f .env.production ]]; then
            print_warning ".env文件不存在，复制.env.production模板..."
            cp .env.production .env
            print_warning "请编辑.env文件填写正确的配置后重新运行部署脚本"
            exit 1
        else
            print_error "环境变量文件不存在，请创建.env文件"
            exit 1
        fi
    fi
    
    # 检查关键配置
    if ! grep -q "ARK_API_KEY=" .env || grep -q "your_api_key_here" .env; then
        print_error "请在.env文件中配置正确的ARK_API_KEY"
        exit 1
    fi
    
    if ! grep -q "NEO4J_PASSWORD=" .env || grep -q "changeme123" .env; then
        print_warning "建议修改默认的Neo4j密码"
    fi
    
    print_success "环境变量检查通过"
}

# 创建必要目录
create_directories() {
    print_info "创建必要目录..."
    
    mkdir -p logs
    mkdir -p backups
    mkdir -p nginx/ssl
    
    print_success "目录创建完成"
}

# 构建镜像
build_images() {
    print_info "构建Docker镜像..."
    
    # 构建后端镜像
    print_info "构建后端镜像..."
    docker build -t kgms-backend ./backend
    
    # 构建前端镜像
    print_info "构建前端镜像..."
    docker build -t kgms-frontend ./frontend
    
    print_success "镜像构建完成"
}

# 停止现有服务
stop_services() {
    print_info "停止现有服务..."
    
    if docker-compose ps -q > /dev/null 2>&1; then
        docker-compose down
    elif docker compose ps -q > /dev/null 2>&1; then
        docker compose down
    fi
    
    print_success "服务停止完成"
}

# 启动服务
start_services() {
    print_info "启动服务..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    else
        docker compose up -d
    fi
    
    print_success "服务启动完成"
}

# 等待服务健康
wait_for_health() {
    print_info "等待服务健康检查..."
    
    local max_attempts=30
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        if curl -f http://localhost/health > /dev/null 2>&1; then
            print_success "服务健康检查通过"
            return 0
        fi
        
        print_info "等待服务启动... (${attempt}/${max_attempts})"
        sleep 10
        ((attempt++))
    done
    
    print_error "服务健康检查超时"
    return 1
}

# 显示服务状态
show_status() {
    print_info "服务状态:"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        docker compose ps
    fi
    
    echo ""
    print_info "服务访问地址:"
    echo "  - 前端应用: http://localhost"
    echo "  - API文档:  http://localhost/api/docs" 
    echo "  - Neo4j浏览器: http://localhost:7474"
    echo "  - 健康检查: http://localhost/health"
}

# 主函数
main() {
    echo "========================================"
    echo "    KGMS 生产环境部署脚本"
    echo "========================================"
    echo ""
    
    check_root
    check_dependencies
    check_env
    create_directories
    stop_services
    build_images
    start_services
    
    if wait_for_health; then
        echo ""
        print_success "🎉 KGMS部署成功！"
        show_status
        
        echo ""
        print_info "后续步骤："
        echo "  1. 访问 http://localhost 查看前端应用"
        echo "  2. 访问 http://localhost:7474 配置Neo4j数据库"
        echo "  3. 查看日志: docker-compose logs -f"
        echo "  4. 运行 ./backup.sh 创建数据备份"
        
    else
        print_error "部署过程中出现问题，请检查日志"
        if command -v docker-compose &> /dev/null; then
            docker-compose logs
        else
            docker compose logs
        fi
        exit 1
    fi
}

# 运行主函数
main "$@"