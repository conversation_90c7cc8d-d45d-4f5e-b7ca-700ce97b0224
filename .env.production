# KGMS Production Environment Configuration
# 复制此文件为 .env 并填写实际的生产环境配置

# ==========================================
# 数据库配置
# ==========================================

# Neo4j 图数据库配置
NEO4J_PASSWORD=your_strong_password_here
# 注意: 生产环境中请使用强密码，至少12位包含大小写字母、数字和特殊字符

# ==========================================  
# AI服务配置
# ==========================================

# 火山引擎 API 配置
ARK_API_KEY=your_volcano_api_key_here
# 获取方式: https://console.volcengine.com/ark

# ==========================================
# 应用配置  
# ==========================================

# 域名配置 (如果使用域名)
DOMAIN=kgms.kilofolo.com
# 例如: DOMAIN=kgms.company.com

# SSL证书路径 (如果启用HTTPS)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem  
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ==========================================
# 性能配置
# ==========================================

# Neo4j 内存配置 (根据服务器内存调整)
NEO4J_HEAP_INITIAL=512m
NEO4J_HEAP_MAX=2G
NEO4J_PAGECACHE=1G

# FastAPI Worker 进程数 (根据CPU核心数调整)
BACKEND_WORKERS=4

# ==========================================
# 安全配置
# ==========================================

# JWT Secret (生成随机字符串)
JWT_SECRET=your_jwt_secret_key_here
# 生成命令: openssl rand -base64 32

# 允许的域名 (CORS配置)
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# ==========================================
# 监控配置
# ==========================================

# 日志级别
LOG_LEVEL=INFO

# 监控开关
ENABLE_MONITORING=true

# ==========================================
# 备份配置  
# ==========================================

# 自动备份开关
ENABLE_AUTO_BACKUP=true

# 备份保留天数
BACKUP_RETENTION_DAYS=30

# 备份存储路径
BACKUP_PATH=/opt/kgms/backups

# ==========================================
# 示例填写 (请根据实际情况修改)
# ==========================================

# NEO4J_PASSWORD=KgMs2024!@#$
# ARK_API_KEY=a410712b-69d4-4812-8d2a-753b6a51bbd6  
# DOMAIN=kgms.mycompany.com
# JWT_SECRET=abcdef1234567890abcdef1234567890
# ALLOWED_ORIGINS=https://kgms.mycompany.com