# KGMS - Knowledge Graph Management System

🎯 **企业级智能产品数据录入平台** - 基于AI的知识图谱管理系统，集成先进的向量搜索、智能去重和批量管理功能。

## ✨ 核心特性

### 🤖 智能AI录入
- **AI文本提取**: 火山引擎LLM自动从产品描述中提取结构化信息
- **实体智能链接**: 基于向量相似度的语义匹配，自动关联现有实体
- **批量确认操作**: 一键批量确认实体，提升录入效率90%+

### 📊 企业级管理
- **完整CRUD界面**: 品牌、成分、产品的全生命周期管理  
- **批量操作工具**: 智能合并、安全删除、批量向量化
- **实时监控Dashboard**: 向量化进度、健康状态、质量指标

### 🔍 智能搜索与去重
- **语义向量搜索**: 基于火山引擎Embedding的高精度相似度匹配
- **智能去重检测**: HIGH/MEDIUM/LOW风险等级评估和推荐操作
- **数据质量监控**: 6种检查类型，0-100分量化评分系统

### 🎨 优秀用户体验  
- **左右分栏布局**: 解决长页面滚动问题，固定头部/底部设计
- **实时状态反馈**: 完整的加载状态、进度监控、错误提示
- **响应式设计**: 桌面/移动端完美适配，中文界面本地化

## 🏗️ 技术架构

### Frontend (企业级UI)
```
React 19.1.1 + TypeScript 5.8.3
├── UI框架: Ant Design 5.27.0 (企业级设计语言)
├── 状态管理: Zustand 5.0.7 (轻量级响应式)
├── 构建工具: Vite 7.1.2 (快速构建)
├── 路由管理: React Router DOM 7.1.1
└── HTTP客户端: Axios 1.11.0 (类型安全)
```

### Backend (异步微服务)
```
FastAPI 0.104.1 + Python异步架构
├── AI服务: LangChain + 火山引擎ARK API
├── 向量化: Doubao Embedding (语义搜索)
├── 数据库: Neo4j 5.26.0 (图数据库+向量存储)
├── 验证框架: Pydantic v2 (类型安全)
└── 科学计算: NumPy (向量计算)
```

### Database (图数据库)
```
Neo4j 5.26.0 知识图谱
├── 节点类型: Product, Brand, Ingredient (带向量属性)
├── 关系类型: MANUFACTURED_BY, CONTAINS {amount, unit}
├── 数据隔离: KGMS_前缀 (兼容现有实例)
└── 索引优化: 名称搜索 + 向量相似度索引
```

## 🚀 快速开始

### 环境要求
- **Node.js** ≥ 18.0.0 (前端开发)
- **Python** ≥ 3.8 (后端服务) 
- **Neo4j** ≥ 5.0 (图数据库)
- **火山引擎账号** (AI服务API密钥)

### 1️⃣ 启动后端服务
```bash
# 进入后端目录
cd backend

# 安装Python依赖
pip install -r requirements.txt

# 配置环境变量 (复制并编辑)
cp .env.example .env

# 启动开发服务器
python -m uvicorn app.main:app --host 0.0.0.0 --port 8080 --reload
```

**后端服务地址**: http://localhost:8080  
**API文档**: http://localhost:8080/docs

### 2️⃣ 启动前端应用
```bash
# 进入前端目录  
cd frontend

# 安装Node.js依赖
npm install

# 启动开发服务器
npm run dev
```

**前端应用地址**: http://localhost:5173

### 3️⃣ 配置Neo4j数据库
```bash
# 确保Neo4j服务运行在默认端口
# 连接地址: bolt://localhost:7687
# 默认用户名: neo4j
# 默认密码: password (请在.env中配置)

# 系统会自动创建约束和索引，无需手动初始化
```

## 📋 环境配置

### Backend配置 (.env)
```bash
# Neo4j图数据库
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j  
NEO4J_PASSWORD=password
NEO4J_LABEL_PREFIX=KGMS

# 火山引擎AI服务
ARK_API_KEY=your_volcano_api_key_here
BASE_URL=https://ark.cn-beijing.volces.com/api/v3
MODEL_ID=doubao-seed-1-6-flash-250715
EMBEDDING_MODEL=doubao-embedding-large-text-250515
```

### Frontend配置 (自动检测)
```bash
# Vite会自动检测后端服务地址
# 默认配置: http://localhost:8080
# 支持动态hostname检测，无需手动配置
```

## 🎯 核心功能使用

### 智能产品录入
1. **AI文本提取**: 在左侧面板粘贴产品描述，点击"开始AI提取"
2. **实体确认**: 在右侧面板确认提取的品牌和成分信息
3. **批量操作**: 使用"全选"和"批量确认"提升处理效率
4. **保存产品**: 完成实体确认后点击"确认并保存产品"

### 企业级管理
1. **实体管理**: 访问品牌/成分/产品管理页面进行CRUD操作
2. **批量工具**: 使用批量操作界面进行合并、删除、向量化
3. **监控Dashboard**: 查看向量化覆盖率、健康状态、质量指标
4. **数据质量**: 运行质量检查，获取详细的质量报告

## 📡 API接口文档

### Phase 1 核心API (/api/v1)
```bash
POST /api/v1/extract              # AI文本提取
GET  /api/v1/entities/search      # 实体搜索(含产品属性) 
POST /api/v1/products             # 基础产品保存
POST /api/v1/products/enhanced    # 增强产品保存(实体链接)
```

### Phase 2 企业级API (/api/v2)  
```bash
# 实体管理
GET    /api/v2/entity/{type}           # 实体列表(分页/搜索)
POST   /api/v2/entity/{type}           # 创建实体(自动向量化)
PUT    /api/v2/entity/{type}/{id}      # 更新实体  
DELETE /api/v2/entity/{type}/{id}      # 删除实体
POST   /api/v2/entity/batch-operation  # 批量操作

# 智能去重
POST /api/v2/duplicate/detect          # 去重检测
POST /api/v2/duplicate/batch-detect    # 批量去重

# 向量化管理  
GET  /api/v2/vectorization/status      # 向量化状态
GET  /api/v2/vectorization/dashboard/* # 监控Dashboard
POST /api/v2/vectorization/retry/*     # 重试任务

# 数据质量
POST /api/v2/quality/report           # 质量报告
GET  /api/v2/quality/stats            # 质量统计

# 图数据可视化
GET  /api/v2/graph/overview           # 图谱概览
POST /api/v2/graph/subgraph           # 子图查询
```

完整API文档: http://localhost:8080/docs

## 🔧 开发工具

### 前端开发
```bash
npm run dev      # 开发服务器 (热重载)
npm run build    # 生产构建
npm run lint     # 代码检查
npm run preview  # 构建预览
```

### 后端开发  
```bash
python test_api.py                                    # API集成测试
python app/tools/batch_vectorization.py --help       # 批量向量化工具
python app/tools/batch_vectorization.py --stats      # 向量化统计
python app/tools/batch_vectorization.py --type all   # 批量处理所有实体
```

### 数据库工具
```bash
# Neo4j Browser: http://localhost:7474
# 系统会自动创建KGMS_前缀的节点和关系
# 支持向量相似度查询和图遍历操作
```

## 📊 性能指标

### 响应时间基准  
- **AI文本提取**: ≤ 3秒 (火山引擎LLM)
- **向量化处理**: ≤ 2秒 (单个实体)  
- **实体搜索**: ≤ 500ms (含产品属性)
- **相似度搜索**: ≤ 300ms (向量查询)
- **批量操作**: 50-100个/分钟
- **页面加载**: ≤ 1秒

### 数据处理能力
- **文本输入**: 最大10,000字符
- **并发支持**: FastAPI异步架构
- **向量维度**: 1024维语义向量
- **相似度阈值**: 可配置(默认0.75)

## 🛠️ 生产部署

### Docker部署 (推荐)
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志  
docker-compose logs -f
```

### 手动部署
```bash
# 前端构建
cd frontend && npm run build

# 后端生产服务器
cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8080

# 使用Nginx反向代理 (可选)
# 配置SSL证书和域名解析
```

### 环境检查
```bash
# 后端健康检查
curl http://localhost:8080/health

# 前端应用检查
curl http://localhost:5173

# Neo4j连接检查  
curl http://localhost:7474
```

## 🧪 测试

### 后端测试
```bash
cd backend
python test_api.py                    # 完整API集成测试
python -m pytest                      # 单元测试 (如果配置)
python app/tools/batch_vectorization.py --test  # 向量化测试
```

### 前端测试
```bash  
cd frontend
npm run lint                          # 代码规范检查
# 注: 可扩展添加Jest/Vitest单元测试
```

## 🤝 贡献指南

### 开发流程
1. Fork本仓库并创建特性分支
2. 遵循现有代码风格和架构规范  
3. 确保前后端类型定义同步更新
4. 运行测试确保功能正常
5. 提交PR并描述变更内容

### 代码规范
- **Frontend**: TypeScript严格模式 + ESLint
- **Backend**: Python类型注解 + Pydantic验证
- **API**: RESTful设计原则 + OpenAPI文档
- **Database**: Cypher查询优化 + 事务处理

### 架构原则
- **异步优先**: 全栈异步架构设计
- **类型安全**: 端到端类型定义和验证
- **容错设计**: 优雅降级和错误处理  
- **性能优化**: 缓存策略和批量处理
- **用户体验**: 实时反馈和智能引导

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 支持与反馈

- **技术文档**: 查看 [CLAUDE.md](CLAUDE.md) 详细技术说明
- **前端文档**: 查看 [frontend/CLAUDE.md](frontend/CLAUDE.md)  
- **后端文档**: 查看 [backend/CLAUDE.md](backend/CLAUDE.md)
- **API文档**: http://localhost:8080/docs (启动后端后访问)

## 🎯 项目状态

**开发状态**: 🎯 **生产就绪 - 企业级完整实现**  
**最后更新**: 2025年8月28日  
**核心特色**: 智能录入 + 向量搜索 + 批量管理 + 实时监控 + 质量保证

---

<div align="center">
  <strong>🚀 Enterprise-Grade Knowledge Graph Management System</strong><br>
  <em>Powered by Volcano Engine AI + Neo4j + React</em>
</div>