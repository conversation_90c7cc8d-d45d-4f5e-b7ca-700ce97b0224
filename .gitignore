# KGMS Project - Git Ignore Configuration

backups/

# ==============================================================================
# Node.js / Frontend
# ==============================================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production builds
dist/
build/
*.tsbuildinfo

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Eslint cache
.eslintcache

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Vite
.vite

# ==============================================================================
# Python / Backend
# ==============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments (see below for more specific patterns)
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# ==============================================================================
# Environment Variables & Secrets
# ==============================================================================

# Environment configuration files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example

# API Keys and secrets
*.key
*.pem
secrets.json
config/secrets.yml

# Firebase
.firebase/
.firebaserc
firebase-debug.log

# ==============================================================================
# Database Files
# ==============================================================================

# SQLite databases
*.db
*.sqlite
*.sqlite3
app_users.db

# Neo4j local data (if running embedded)
data/
logs/
import/
metrics/

# Database backups
*.sql
*.dump

# ==============================================================================
# IDE & Editor Files
# ==============================================================================

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==============================================================================
# Operating System Files
# ==============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==============================================================================
# Logs & Runtime Files
# ==============================================================================

# Application logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# ==============================================================================
# Temporary & Cache Files
# ==============================================================================

# Temporary folders
tmp/
temp/
.tmp/
.temp/

# Cache directories
.cache/
.parcel-cache/
.next/
.nuxt/
.vuepress/dist

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ==============================================================================
# Project Specific
# ==============================================================================

# KGMS specific temporary files
/backend/test_data/
/backend/logs/
/backend/temp/

# Frontend specific
/frontend/dist/
/frontend/.vite/

# Documentation builds (if using automated docs)
/docs/build/
/docs/.vuepress/dist/

# ==============================================================================
# Docker & Deployment
# ==============================================================================

# Docker
Dockerfile.local
docker-compose.override.yml
.dockerignore

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# ==============================================================================
# Monitoring & Analytics
# ==============================================================================

# Sentry
.sentryclirc

# ==============================================================================
# Package Managers
# ==============================================================================

# Yarn
.yarn/*
!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# ==============================================================================
# End of .gitignore
# ==============================================================================